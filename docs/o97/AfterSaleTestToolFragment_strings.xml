<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- AfterSaleTestToolFragment 字符串资源 -->
    <string name="afterSaleToolTitle">售后自检工具</string>
    <string name="afterSaleDeviceName">设备名称</string>
    <string name="afterSaleDeviceSN">SN</string>
    <string name="afterSaleDeviceVersion">版本号</string>
    <string name="afterSaleDeviceBattery">电池电量</string>
    <string name="afterSaleStart">开始检测</string>
    <string name="afterSaleExit">退出</string>
    <string name="afterSaleNeedDischarging">眼镜充电中，请停止充电后启动检测</string>
    <string name="afterSaleEnterFail">进入售后自检模式出错</string>
    <string name="afterSaleNoTicket">该设备无售后工单</string>
    
    <!-- 相关页面字符串资源 -->
    <string name="afterSaleLightPageTitle">1/5 指示灯检测</string>
    <string name="afterSaleLightOnCaption">请点击按钮，人工检查眼镜指示灯是否正常亮起</string>
    <string name="afterSaleLightOffCaption">请点击按钮，人工检查眼镜指示灯是否正常熄灭</string>
    <string name="afterSaleLightOnButton">点亮指示灯</string>
    <string name="afterSaleLightOffButton">熄灭指示灯</string>
    <string name="afterSaleSARPageTitle">2/5 佩戴检测</string>
    <string name="afterSaleSARPageCaption">请根据提示依次正确完成佩戴/摘下操作，完成后点击右侧已完成按钮</string>
    <string name="afterSaleSAROnStatus">当前状态：已佩戴</string>
    <string name="afterSaleSAROffStatus">当前状态：未佩戴</string>
    <string name="afterSaleSAROnCaption">请摘下眼镜检查状态</string>
    <string name="afterSaleSAROffCaption">请戴上眼镜检测状态</string>
    <string name="afterSaleTouchTitle">3/5 触控检测</string>
    <string name="afterSaleTouchPageCaption">按操作图依次正确完成触控操作，完成后点击右侧已完成按钮</string>
    <string name="afterSaleTouchPageCaption2">为确保检测准确，请将手指按压在镜腿触摸板上，缓慢滑过全部触控区域，过程中请勿抬起手指。</string>
    <string name="afterSaleTouchLeftCaption">单指滑动左镜腿全部触控区域</string>
    <string name="afterSaleTouchRightCaption">单指滑动右镜腿全部触控区域</string>
    <string name="afterSaleTouchDoneCaption">已完成</string>
    <string name="afterSaleTouchStatusNormal">正常</string>
    <string name="afterSaleTouchStatusAbnormal">异常</string>
    <string name="afterSaleSpeakerTitle">4/5 扬声器检测</string>
    <string name="afterSaleSpeakerDesCaption">请点击开始检测</string>
    <string name="afterSaleSpeakerDesSubCaption">人工检查左右扬声器是否正常出声</string>
    <string name="afterSaleSpeakerPlayingCaption">播放中</string>
    <string name="afterSaleMicTitle">5/5 麦克风检测</string>
    <string name="afterSaleMicPageCaption">请点击开始检测，根据提示完成操作</string>
    <string name="afterSaleMicLeftCaption">左镜腿麦克风检测</string>
    <string name="afterSaleMicRightCaption">右镜腿麦克风检测</string>
    <string name="afterSaleMicTestingCaption">请在10s内完成操作：请在距离图示麦克风位置附近，大声读出 1、2、3</string>
    <string name="afterSaleMicInTestCaption">检测中</string>
    <string name="afterSaleReportTitle">检测报告</string>
    <string name="afterSaleReportLight">指示灯检测</string>
    <string name="afterSaleReportSar">佩戴检测</string>
    <string name="afterSaleReportTouch">触控检测</string>
    <string name="afterSaleReportSpeaker">扬声器检测</string>
    <string name="afterSaleReportMic">麦克风检测</string>
    <string name="afterSaleReportPass">通过</string>
    <string name="afterSaleReportFail">未通过</string>
    <string name="afterSaleReportButton">上传检测报告</string>
    <string name="afterSaleReportConfirm">确定</string>
    <string name="afterSaleReportCancel">取消</string>
    <string name="afterSaleReportRetry">重试</string>
    <string name="afterSaleFailCommandFail">与眼镜通信失败。</string>
    <string name="afterSaleFailButton">不通过</string>
    <string name="afterSalePassButton">通过</string>
    <string name="afterSaleBluetoothError">蓝牙状态异常</string>
    <string name="afterSaleBluetoothErrorDetails">请检查手机蓝牙开关是否打开，请检查设备是否在手机附近。操作无效后，可以点击下方按钮退出检测</string>
    <string name="afterSaleBluetoothErrorExit">退出检测</string>
    
    <!-- 通用字符串资源 -->
    <string name="tip_device_connected">设备已连接</string>
    <string name="ss2DefaultName">小米智能眼镜</string>
    <string name="libs_copy">已复制到剪贴板</string>
</resources>