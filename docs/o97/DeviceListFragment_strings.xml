<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- fragment_device_list.xml -->
    <string name="deviceList">设备列表</string>
    <string name="addDevice">添加设备</string>

    <!-- DeviceListFragmentAdapter.kt -->
    <string name="connected">已连接</string>
    <string name="unconnected">未连接</string>
    <string name="goBind">去绑定</string>
    <!-- adapter_item_devicelist.xml -->
    <string name="emptySetting">设置</string>

    <!-- DeviceNameEditFragment.kt -->
    <string name="deviceNameTip">请输入设备名称</string>
    <string name="libs_save_failed">保存失败</string>

    <!-- fragment_device_name_edit.xml -->
    <string name="deviceName">设备名称</string>

    <!--
        “添加设备”弹框 (DeviceBindDialog):
        此弹框触发的流程与 `DeviceAddFragment` 页面的绑定流程完全相同。
        因此，其相关的字符串资源请参考 `DeviceAddFragment_strings.xml` 文件。
        以下是该流程中独有的、未在 `DeviceAddFragment_strings.xml` 中列出的字符串。
    -->

    <!-- DeviceBindViewModel.kt & DeviceBindState.kt -->
    <string name="completeConnecting">正在连接…</string>
    <string name="bindFailedReason">绑定失败</string>

    <!-- HomeBottomBindFailedFragment.kt & its layout -->
    <string name="checkSolution"><![CDATA[<u>查看解决方案</u>]]></string>
    <string name="bindFailed">绑定失败</string>
    <string name="libs_retry">重试</string>

    <!-- HomeBottomBindSuccessFragment.kt & its layout -->
    <string name="bindSuccess">绑定成功</string>
    <string name="complete">完成</string>

    <!-- HomeBottomBindSuccessWithGuideFragment.kt & its layout -->
    <string name="libs_learn_basics">学习基本操作</string>

    <!-- HomeBottomLoadingFragment.kt & its layout -->
    <string name="completeBinding">正在绑定…</string>

    <!-- HomeBottomSearchFragment.kt & its layout -->
    <string name="searchingHost">查找设备中…</string>

    <!-- HomeBottomShowPairingFragment.kt & its layout -->
    <string name="pairingTip">轻点触摸板确认配对</string>

    <!-- HomeBottomWaitOOBFragment.kt & its layout -->
    <string name="plsConfirmCodeOnHost">请在设备屏幕上核对以上编码，
单击触控板进行确认</string>

    <!-- HomeBottomClassicBleStateDialog.kt & its layout -->
    <string name="connect_headphones">连接眼镜</string>
    <string name="classtic_ble_tip">佩戴后，长按双侧触控区，听到提示音后，成功开启配对。然后去“系统-蓝牙”进行连接</string>
    <string name="sss_classtic_ble_tip">佩戴后，长按双侧触控区2秒，听到提示音后，成功进入配对。然后去“系统-蓝牙”进行连接</string>
    <string name="goConnect">已开启配对 去连接</string>
    <string name="has_connect_go_bind">已连接 去绑定</string>

    <!-- HomeBottomReBindFragment.kt & its layout -->
    <string name="tips_device_rebind">%s已恢复出厂设置，
是否重新绑定？</string>
    <string name="action_rebind">重新绑定</string>
    <string name="action_unbind">解绑</string>
</resources>