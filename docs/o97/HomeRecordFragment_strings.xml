<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- com.superhexa.supervision.feature.audioglasses.presentation.recording.HomeRecordFragment -->

    <!-- from feature_audioglasses/src/main/res/values-zh-rCN/strings.xml -->
    <string name="ss2Record">录音</string>
    <string name="ss2RecordHomeTip">眼镜录音将实时传输到App，录制期间请确保App处于活跃状态。录音遇到问题？</string>
    <string name="ss2RecordHomeTiplight">录音遇到问题？</string>
    <string name="ss2RecordCall">通话录音</string>
    <string name="ss2RecordCallDes">拨打系统电话或网络电话时实时录音</string>
    <string name="ss2RecordVideo">音视频录音</string>
    <string name="ss2RecordVideoDes">播放音视频文件时实时录音</string>
    <string name="ss2RecordFaceToFace">现场录音</string>
    <string name="ss2RecordFaceToFaceDes">面对面交谈时实时录音</string>
    <string name="ss2RecordTipNoInMusic">当前未播放音频</string>
    <string name="ss2RecordTipDoing">眼镜正在录音中…</string>
    <string name="ss2RecordTipEnd">结束录音</string>
    <string name="ss2RecordTipStart">录音功能说明</string>
    <string name="ss2RecordTipStartDes">请合理使用录音功能，在使用过程中不得侵犯他人的合法权益，包括但不限于隐私权、知识产权等。此外，不得将相关录音用于非法目的。否则，您应独立承担由此产生的所有法律责任。
当您录制通话时，会在通话录制开始和结束时发出通知。
录音文件仅存储在手机本地，不会备份到云端。</string>
    <string name="ss2RecordTipStartBtRight">我知道了</string>
    <string name="ss2RecordTipEndDes">眼镜录音即将终止，是否确认操作？</string>
    <string name="ssDeviceNotConnected">设备未连接，请检查蓝牙状态</string>
    <string name="ss2RecordTipNoInCall">当前没有通话</string>

    <!-- from module_basic/library_string/src/main/res/values-zh-rCN/strings.xml -->
    <string name="libs_exit">退出</string>
    <string name="ss2RecordCheckTip1">眼镜充电中，请断电后重试</string>
    <string name="ss2RecordCheckTip2">正在通话中，请稍后再试</string>

    <!-- from module_basic/library_base_common/src/main/res/values-zh-rCN/strings.xml -->
    <string name="cancel">取消</string>
    <string name="sure">确定</string>
</resources>
