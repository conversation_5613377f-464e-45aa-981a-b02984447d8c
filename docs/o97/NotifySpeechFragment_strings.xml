<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- com.superhexa.supervision.feature.audioglasses.presentation.notifyspeech.NotifySpeechFragment -->

    <!-- from feature_audioglasses/src/main/res/values-zh-rCN/strings.xml -->
    <string name="ssNotifySpeech">通知播报</string>
    <string name="ssNotifySpeechTip_ss2">连接且佩戴眼镜后，手机锁屏时，自动播报通知。此功能依赖系统通知。</string>
    <string name="ssNotifySpeechTip">连接且佩戴眼镜后，手机锁屏时，自动播报通知。此功能依赖系统通知。播报过程中减小音量停止通知播报。</string>
    <string name="ssDeviceNotConnected">设备未连接，请检查蓝牙状态</string>
    <string name="ssNotifySpeechTip2">Beta功能在使用中可能会出现错误或不稳定。如果您需要进一步的帮助，请查看 通知无法正常播报？</string>
    <string name="ssNotifyNoSpeech">通知无法正常播报？</string>
    <string name="ssNotifySpeechRateSetting">播报设置</string>
    <string name="ssNotifySpeechRateTitle">播报语速</string>
    <string name="ssNotifySpeechListTip">播报下列通知</string>
    <string name="ssNotifySpeechPermission">通知授权</string>
    <string name="ssNotifySpeechPermissionTip">请在手机的“设置”中授予“&app_name;”通知访问权限</string>
    <string name="denyForeverContactsAllow">被永久拒绝授权，请手动授予通讯录权限</string>
    <string name="ssNotifySpeechContactsTip">我们需要获取通讯录权限，用于通知播报功能开启时对播报短信进行过滤。如果关闭，将不能实现前述功能。</string>
    <string name="ssNotifySpeechContent">播报内容</string>
    <string name="ssNotifySpeechRateTip">播报语速已设置</string>
    <string name="ssNotifySpeechRate075x">0.75x</string>
    <string name="ssNotifySpeechRate10x">1.0x</string>
    <string name="ssNotifySpeechRate125x">1.25x</string>
    <string name="ssNotifySpeechRate15x">1.5x</string>
    <string name="ssNotifySpeechRate20x">2.0x</string>
    <string name="ssNotifySpeechTypeAll">所有通知</string>
    <string name="ssNotifySpeechTypePhone">仅联系人</string>
    <string name="ssNotifySpeechItemMessage">短信</string>
    <string name="ssNotifySpeechItemPhone">电话</string>
    <string name="ssNotifySpeechItemMessage1">信息</string>

    <!-- from module_basic/library_base_common/src/main/res/values-zh-rCN/strings.xml -->
    <string name="cancel">取消</string>
    <string name="goOpen">去开启</string>

    <!-- from module_basic/library_string/src/main/res/values-zh-rCN/strings.xml -->
    <string name="eisClose">关闭</string>
</resources>
