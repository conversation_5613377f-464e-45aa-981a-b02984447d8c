<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- com.superhexa.supervision.feature.audioglasses.presentation.setting.ss2.SS2GestureSettingFragment -->

    <!-- from feature_audioglasses/src/main/res/values-zh-rCN/strings.xml -->
    <string name="ssGestureSettings">手势设置</string>
    <string name="ssGestureSlide">前后滑动</string>
    <string name="ssGestureTouch">轻触2次</string>
    <string name="ssGestureLongPress">长按</string>
    <string name="ssSlideLeftTemple">左镜腿</string>
    <string name="ssSlideRightTemple">右镜腿</string>
    <string name="ss2LeftIncomingCall">左镜腿 (来电时)</string>
    <string name="ss2LeftTheNonCall">左镜腿 (未通话时)</string>
    <string name="ss2RightIncomingCall">右镜腿 (来电时)</string>
    <string name="ss2RightTheNonCall">右镜腿 (未通话时)</string>
    <string name="ss2LeftInCall">左镜腿 (通话时)</string>
    <string name="ss2RightInCall">右镜腿 (通话时)</string>
    <string name="dialogleftSlideTitle">前后滑动左镜腿</string>
    <string name="dialogRightSlideTitle">前后滑动右镜腿</string>
    <string name="dialogLeftTouchTitle1">来电时轻触2次左镜腿</string>
    <string name="dialogLeftTouchTitle2">未通话时轻触2次左镜腿</string>
    <string name="dialogRightTouchTitle1">来电时轻触2次右镜腿</string>
    <string name="dialogRightTouchTitle2">未通话时轻触2次右镜腿</string>
    <string name="dialogLeftLongPressTitle1">通话时长按左镜腿</string>
    <string name="dialogLeftLongPressTitle2">未通话时长按左镜腿</string>
    <string name="dialogRightLongPressTitle1">通话时长按右镜腿</string>
    <string name="dialogRightLongPressTitle2">未通话时长按右镜腿</string>
    <string name="dialogGestureNone">无</string>
    <string name="dialogLongPressItem3">唤起语音助手</string>
    <string name="dialogTouchItem2">播放 / 暂停</string>
    <string name="dialogTouchItem4">接听 / 挂断</string>
    <string name="dialogSlideItem1">下一首 / 上一首</string>
    <string name="dialogSlideItem2">音量+ / 音量-</string>
    <string name="dialogLongPressItem4">断开蓝牙</string>
    <string name="dialogLongPressItem1">拒接电话</string>
    <string name="dialogLongPressItem5">开启 / 结束现场录音</string>
    <string name="dialogLongPressItem6">开启 / 结束通话录音</string>
    <string name="dialogTouchItem3">中断通知播报</string>
    <string name="dialogLongPressItem7">切换隐私模式开关</string>
    <string name="cancel">取消</string>
</resources>
