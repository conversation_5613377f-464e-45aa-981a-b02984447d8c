<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- feature_audioglasses/src/main/res/values-zh-rCN/strings.xml -->
    <string name="ss2HomeItemTitle">更多功能</string>
    <string name="ssGameMode">游戏模式</string>
    <string name="ssGameModeDes">开启后可降低音频延迟，但音质会受影响</string>
    <string name="ssDualDeviceConnection">双设备连接</string>
    <string name="ssDualDeviceConnectionDes">开启后，眼镜可以同时连接两台硬件设备</string>
    <string name="ss2VolumeMeterTitle">隐私模式</string>
    <string name="ss2VolumeMeterDes">开启后将会切换调音风格，减少声音外泄</string>
    <string name="ss2VoiceControlTitle">语音控制</string>
    <string name="ss2VoiceControlDes">开启后，可直接对眼镜说小爱同学唤醒手机语音助手，眼镜续航时间将会减少</string>
    <string name="ssAutomaticVolume">音量自动调节</string>
    <string name="ssAutomaticVolumeDes">开启后，眼镜可以根据环境自动调节音量</string>
    <string name="ssFastDial">快捷拨号</string>
    <string name="ssStandBy">自动待机时间</string>
    <string name="deviceWearDetection">佩戴检测</string>
    <string name="deviceWearSensitivityWarning">提示</string>
    <string name="deviceWearSensitivityVoiceDesc">开启功能后，眼镜续航时间将会减少，是否开启？</string>
    <string name="ssDeviceNotConnected">设备未连接，请检查蓝牙状态</string>
    <string name="ss2VolumeMeterTips">通话中切换失败，请在通话结束后重试</string>

    <!-- module_basic/library_string/src/main/res/values-zh-rCN/strings.xml -->
    <string name="ss2RecordCheckTip2">正在通话中，请稍后再试</string>

    <!-- module_basic/library_base_common/src/main/res/values-zh-rCN/strings.xml -->
    <string name="cancel">取消</string>
    <string name="sure">确定</string>
    <string name="close">关</string>
    <string name="open">开</string>
    <string name="configFailed">设置失败</string>

    <!-- 动态文本，具体内容由 SettingMoreViewModel 决定 -->
    <!-- R.string.ssStandByTime* (e.g., ssStandby30s, ssStandby1m) -->
    <string name="ssStandbyImm">立即</string>
    <string name="ssStandby30s">30秒</string>
    <string name="ssStandby1m">1分钟</string>
    <string name="ssStandby3m">3分钟</string>
    <string name="ssStandbyAuto">非工作状态下3分钟</string>
</resources>