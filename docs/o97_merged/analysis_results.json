{"total_files": 26, "total_strings": 552, "conflicts": {"deviceWearDetection": [{"file": "WearDetectionFragment_strings.xml", "value": "佩戴检测"}, {"file": "SS2HomeFragment_strings.xml", "value": "佩戴检测"}, {"file": "SettingMoreFragment_strings.xml", "value": "佩戴检测"}], "cancel": [{"file": "WearDetectionFragment_strings.xml", "value": "取消"}, {"file": "DeviceMangerFragment_strings.xml", "value": "取消"}, {"file": "RecordPlayFragment_strings.xml", "value": "取消"}, {"file": "GlassesSettingFragment_strings.xml", "value": "取消"}, {"file": "RecordPageFragment_strings.xml", "value": "取消"}, {"file": "RecordTranscriptionFragment_strings.xml", "value": "取消"}, {"file": "SS2GestureSettingFragment_strings.xml", "value": "取消"}, {"file": "NotifySpeechFragment_strings.xml", "value": "取消"}, {"file": "RecordListFragment_strings.xml", "value": "取消"}, {"file": "HomeRecordFragment_strings.xml", "value": "取消"}, {"file": "SettingMoreFragment_strings.xml", "value": "取消"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "取消"}], "sure": [{"file": "WearDetectionFragment_strings.xml", "value": "确定"}, {"file": "RecordPlayFragment_strings.xml", "value": "确认"}, {"file": "GlassesSettingFragment_strings.xml", "value": "确定"}, {"file": "RecordPageFragment_strings.xml", "value": "确定"}, {"file": "RecordTranscriptionFragment_strings.xml", "value": "确定"}, {"file": "RecordListFragment_strings.xml", "value": "确定"}, {"file": "HomeRecordFragment_strings.xml", "value": "确定"}, {"file": "SettingMoreFragment_strings.xml", "value": "确定"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "确定"}], "complete": [{"file": "WearDetectionFragment_strings.xml", "value": "完成"}, {"file": "OnboardingFragment_strings.xml", "value": "完成"}, {"file": "DeviceListFragment_strings.xml", "value": "完成"}], "configFailed": [{"file": "WearDetectionFragment_strings.xml", "value": "设置失败"}, {"file": "SettingMoreFragment_strings.xml", "value": "设置失败"}], "ssDeviceNotConnected": [{"file": "WearDetectionFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}, {"file": "DeviceMangerFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}, {"file": "AutoStandbyFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}, {"file": "NotifySpeechFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}, {"file": "RecordListFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}, {"file": "HomeRecordFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}, {"file": "SettingMoreFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}, {"file": "FindGlassesFragment_strings.xml", "value": "设备未连接，请检查蓝牙状态"}], "ssDeviceConnectionManagement": [{"file": "DeviceMangerFragment_strings.xml", "value": "设备连接管理"}, {"file": "SS2HomeFragment_strings.xml", "value": "设备连接管理"}], "addDevice": [{"file": "DeviceAddFragment_strings.xml", "value": "添加设备"}, {"file": "DeviceListFragment_strings.xml", "value": "添加设备"}], "o95DefaultName": [{"file": "DeviceAddFragment_strings.xml", "value": "小米AI眼镜"}, {"file": "HelpFragment_strings.xml", "value": "小米AI眼镜"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "米家智能音频眼镜"}], "ss2DefaultName": [{"file": "DeviceAddFragment_strings.xml", "value": "mijia智能音频眼镜 2"}, {"file": "AfterSaleTestToolFragment_strings.xml", "value": "小米智能眼镜"}, {"file": "HelpFragment_strings.xml", "value": "mijia智能音频眼镜 2"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "米家智能音频眼镜"}], "sssDefaultName": [{"file": "DeviceAddFragment_strings.xml", "value": "mijia智能音频眼镜 悦享版"}, {"file": "HelpFragment_strings.xml", "value": "mijia智能音频眼镜 悦享版"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "智能眼镜S"}], "ssDefaultName": [{"file": "DeviceAddFragment_strings.xml", "value": "mijia智能音频眼镜"}, {"file": "HelpFragment_strings.xml", "value": "mijia智能音频眼镜"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "智能眼镜"}], "svDefaultName": [{"file": "DeviceAddFragment_strings.xml", "value": "MIJIA眼镜相机"}, {"file": "HelpFragment_strings.xml", "value": "MIJIA眼镜相机"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "SuperVision"}], "libs_cancel": [{"file": "DeviceAddFragment_strings.xml", "value": "取消"}, {"file": "GlassesSettingFragment_strings.xml", "value": "取消"}, {"file": "RecordListFragment_strings.xml", "value": "取消"}], "libs_ok": [{"file": "DeviceAddFragment_strings.xml", "value": "确定"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "确定"}], "ss2VolumeMeterTitle": [{"file": "OnboardingFragment_strings.xml", "value": "隐私模式"}, {"file": "SettingMoreFragment_strings.xml", "value": "隐私模式"}], "ss2RecordEditName": [{"file": "RecordPlayFragment_strings.xml", "value": "修改名称"}, {"file": "RecordTranscriptionFragment_strings.xml", "value": "修改名称"}], "ss2RecordDelete": [{"file": "RecordPlayFragment_strings.xml", "value": "删除文件"}, {"file": "RecordTranscriptionFragment_strings.xml", "value": "删除文件"}, {"file": "RecordListFragment_strings.xml", "value": "删除文件"}], "ss2RecordDeleteFileTip": [{"file": "RecordPlayFragment_strings.xml", "value": "永久删除音频文件"}, {"file": "RecordListFragment_strings.xml", "value": "永久删除音频文件"}], "ss2RecordDeleteFileTipDes": [{"file": "RecordPlayFragment_strings.xml", "value": "即将永久删除选中的音频文件，此操作不可撤销，一旦删除，文件将无法恢复。请仔细确认是否删除？"}, {"file": "RecordListFragment_strings.xml", "value": "即将永久删除选中的音频文件，此操作不可撤销，一旦删除，文件将无法恢复。请仔细确认是否删除？"}], "libs_connecting_device": [{"file": "SS2HomeFragment_strings.xml", "value": "连接中"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "正在连接设备…"}], "ssDualDeviceConnection": [{"file": "SS2HomeFragment_strings.xml", "value": "双设备连接"}, {"file": "SettingMoreFragment_strings.xml", "value": "双设备连接"}], "ssDualDeviceConnectionDes": [{"file": "SS2HomeFragment_strings.xml", "value": "开启后，眼镜可以同时连接两台硬件设备"}, {"file": "SettingMoreFragment_strings.xml", "value": "开启后，眼镜可以同时连接两台硬件设备"}], "ssGestureSettings": [{"file": "SS2HomeFragment_strings.xml", "value": "手势设置"}, {"file": "SS2GestureSettingFragment_strings.xml", "value": "手势设置"}], "ssNotifySpeech": [{"file": "SS2HomeFragment_strings.xml", "value": "通知播报"}, {"file": "NotifySpeechFragment_strings.xml", "value": "通知播报"}], "ssAutomaticVolume": [{"file": "SS2HomeFragment_strings.xml", "value": "音量自动调节"}, {"file": "SettingMoreFragment_strings.xml", "value": "音量自动调节"}], "ssAutomaticVolumeDes": [{"file": "SS2HomeFragment_strings.xml", "value": "开启后，眼镜可以根据环境自动调节音量"}, {"file": "SettingMoreFragment_strings.xml", "value": "开启后，眼镜可以根据环境自动调节音量"}], "ssFastDial": [{"file": "SS2HomeFragment_strings.xml", "value": "快捷拨号"}, {"file": "SettingMoreFragment_strings.xml", "value": "快捷拨号"}], "ssFindGlasses": [{"file": "SS2HomeFragment_strings.xml", "value": "查找眼镜"}, {"file": "FindGlassesFragment_strings.xml", "value": "查找眼镜"}], "ssStandBy": [{"file": "SS2HomeFragment_strings.xml", "value": "自动待机时间"}, {"file": "AutoStandbyFragment_strings.xml", "value": "自动待机时间"}, {"file": "SettingMoreFragment_strings.xml", "value": "自动待机时间"}], "ssStandbyImm": [{"file": "SS2HomeFragment_strings.xml", "value": "立即"}, {"file": "AutoStandbyFragment_strings.xml", "value": "立即"}, {"file": "SettingMoreFragment_strings.xml", "value": "立即"}], "ssStandby30s": [{"file": "SS2HomeFragment_strings.xml", "value": "30秒"}, {"file": "AutoStandbyFragment_strings.xml", "value": "30秒"}, {"file": "SettingMoreFragment_strings.xml", "value": "30秒"}], "ssStandby1m": [{"file": "SS2HomeFragment_strings.xml", "value": "1分钟"}, {"file": "AutoStandbyFragment_strings.xml", "value": "1分钟"}, {"file": "SettingMoreFragment_strings.xml", "value": "1分钟"}], "ssStandby3m": [{"file": "SS2HomeFragment_strings.xml", "value": "3分钟"}, {"file": "AutoStandbyFragment_strings.xml", "value": "3分钟"}, {"file": "SettingMoreFragment_strings.xml", "value": "3分钟"}], "ssStandbyAuto": [{"file": "SS2HomeFragment_strings.xml", "value": "非工作状态下3分钟"}, {"file": "AutoStandbyFragment_strings.xml", "value": "非工作状态下3分钟"}, {"file": "SettingMoreFragment_strings.xml", "value": "非工作状态下3分钟"}], "ssGameMode": [{"file": "SS2HomeFragment_strings.xml", "value": "游戏模式"}, {"file": "SettingMoreFragment_strings.xml", "value": "游戏模式"}], "ssGameModeDes": [{"file": "SS2HomeFragment_strings.xml", "value": "开启后可降低音频延迟，但音质会受影响"}, {"file": "SettingMoreFragment_strings.xml", "value": "开启后可降低音频延迟，但音质会受影响"}], "ss2Record": [{"file": "SS2HomeFragment_strings.xml", "value": "录音"}, {"file": "HomeRecordFragment_strings.xml", "value": "录音"}], "ss2HomeItemTitle": [{"file": "SS2HomeFragment_strings.xml", "value": "更多功能"}, {"file": "SettingMoreFragment_strings.xml", "value": "更多功能"}], "open": [{"file": "SS2HomeFragment_strings.xml", "value": "开"}, {"file": "SettingMoreFragment_strings.xml", "value": "开"}], "close": [{"file": "SS2HomeFragment_strings.xml", "value": "关"}, {"file": "SettingMoreFragment_strings.xml", "value": "关"}], "deviceFirmWarreUpdate": [{"file": "GlassesSettingFragment_strings.xml", "value": "固件更新"}, {"file": "DeviceAboutFragment_strings.xml", "value": "固件更新"}], "deviceInfo": [{"file": "GlassesSettingFragment_strings.xml", "value": "设备信息"}, {"file": "SSDeviceInfoFragment_strings.xml", "value": "设备信息"}], "ss2RecordCheckTip3": [{"file": "GlassesSettingFragment_strings.xml", "value": "正在录音，请稍后重试"}, {"file": "RecordListFragment_strings.xml", "value": "正在录音，请稍后重试"}], "libs_copy": [{"file": "AfterSaleTestToolFragment_strings.xml", "value": "已复制到剪贴板"}, {"file": "SSDeviceInfoFragment_strings.xml", "value": "已复制"}], "deviceName": [{"file": "DeviceNameEditFragment_strings.xml", "value": "设备名称"}, {"file": "DeviceListFragment_strings.xml", "value": "设备名称"}], "deviceNameTip": [{"file": "DeviceNameEditFragment_strings.xml", "value": "请输入设备名称"}, {"file": "DeviceListFragment_strings.xml", "value": "请输入设备名称"}], "libs_save_failed": [{"file": "DeviceNameEditFragment_strings.xml", "value": "保存失败"}, {"file": "DeviceListFragment_strings.xml", "value": "保存失败"}], "ss2RecordTipEnd": [{"file": "RecordPageFragment_strings.xml", "value": "结束录音"}, {"file": "HomeRecordFragment_strings.xml", "value": "结束录音"}], "ss2RecordTipEndDes": [{"file": "RecordPageFragment_strings.xml", "value": "眼镜录音即将终止，是否确认操作？"}, {"file": "HomeRecordFragment_strings.xml", "value": "眼镜录音即将终止，是否确认操作？"}], "libs_retry": [{"file": "RecordListFragment_strings.xml", "value": "重试"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "重试"}, {"file": "DeviceListFragment_strings.xml", "value": "重试"}], "ss2RecordCheckTip2": [{"file": "HomeRecordFragment_strings.xml", "value": "正在通话中，请稍后再试"}, {"file": "SettingMoreFragment_strings.xml", "value": "正在通话中，请稍后再试"}]}, "file_details": [{"file_path": "docs/o97/WearDetectionFragment_strings.xml", "string_count": 23, "strings": {"deviceWearDetection": "佩戴检测", "deviceWearSensitivity": "佩戴检测灵敏度", "deviceCalibrate": "佩戴检测校准", "deviceWearSensitivityTitle3": "温馨提示", "deviceWearSensitivityTitle3Des": "关闭功能后，摘下眼镜不会自动断开蓝牙，眼镜续航时间将会减少，是否关闭？", "deviceWearSensitivityDes": "请根据使用体验，选择适合自己的佩戴检测灵敏度", "plsPrepare": "开始校准", "plsPrepareAdjust": "请将眼镜放在水平桌面开始校准传感器 \n为避免校准失败，过程中请不要播放音乐/接打电话", "calibration_start": "开始", "adjustFail": "校准失败", "adjustSuccess": "校准成功", "reAdjust": "重新校准", "deviceWearSensitivityTitle1": "灵敏", "deviceWearSensitivityTitle1Des": "适合头型较小，镜腿与皮肤贴合度较差的用户", "deviceWearSensitivityTitle2": "标准", "deviceWearSensitivityTitle2Des": "适合大部分用户，平衡了灵敏度和稳定性", "cancel": "取消", "sure": "确定", "complete": "完成", "configFailed": "设置失败", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态", "ss2RecordCheckTip4": "为避免校准失败，请暂停音乐后重试。", "ss2RecordCheckTip5": "为避免校准失败，请挂断电话后重试。"}}, {"file_path": "docs/o97/DeviceMangerFragment_strings.xml", "string_count": 10, "strings": {"ssDeviceConnectionManagement": "设备连接管理", "ssDeviceConnectionManagementAdd": "优先连接设备", "ssDeviceConnectionManagementHave": "已配对设备", "ssDeviceStatusConnect": "连接", "ssDeviceStatusDisconnect": "断开", "ssDeviceStatusUnpair": "取消配对", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态", "ssPriorityAuto": "自动", "ssDeviceStatusConnectFailed": "连接失败", "cancel": "取消"}}, {"file_path": "docs/o97/DeviceAddFragment_strings.xml", "string_count": 21, "strings": {"firstBindNeedNetWork": "首次绑定需要连接到网络", "addDevice": "添加设备", "o95DefaultName": "小米AI眼镜", "homeDeviceo95Des": "XIAOMI AI Glasses", "ss2DefaultName": "mijia智能音频眼镜 2", "homeDeviceSS2Des": "纤细强悍 随心录听", "sssDefaultName": "mijia智能音频眼镜 悦享版", "homeDeviceSSSDes": "\"不入耳 更好听\"", "ssDefaultName": "mijia智能音频眼镜", "homeDeviceSSDes": "开放声场 解放双耳", "svDefaultName": "MIJIA眼镜相机", "homeDeviceSV1Des": "影像酷装备 见前所未见", "home_beta": "Beta", "plsReadPrivarcyAndAgree": "请阅读并同意 用户协议 和 隐私政策 ", "device_user_agreement": "用户协议", "libs_cancel": "取消", "agreeGoOn": "同意并继续", "notFindHostCheckReason": "", "notYourDevice": "", "plsClickBindDevice": "请点击要绑定的设备", "libs_ok": "确定"}}, {"file_path": "docs/o97/OnboardingFragment_strings.xml", "string_count": 15, "strings": {"ss2VolumeMeterTitle": "隐私模式", "onboardingSkip": "跳过", "onboardingGesDec": "可在手势设置中查看/修改功能", "onboardingSetDec": "可在更多功能中查看/修改隐私模式", "onboardingGes1": "长按单侧镜腿", "onboardingGes2": "前后滑动镜腿", "onboardingGes3": "双击单侧镜腿", "onboardingGes1Dec": "快速启动录音", "onboardingGes2Dec": "切歌 调节音量", "onboardingGes3Dec": "接听/挂断电话\n播放/暂停音乐", "onboardingAudioMode": "选择适合你的模式", "onboardingAudioModeNormal": "默认音质", "onboardingAudioModeNormalDes": "更好的听觉体验", "onboardingAudioModeSecretDes": "更好的防漏音体验", "complete": "完成"}}, {"file_path": "docs/o97/RecordPlayFragment_strings.xml", "string_count": 9, "strings": {"ss2RecordMoreSet": "更多设置", "ss2RecordEditName": "修改名称", "ss2RecordShareFile": "分享文件", "ss2RecordDelete": "删除文件", "ss2RecordDeleteFileTip": "永久删除音频文件", "ss2RecordDeleteFileTipDes": "即将永久删除选中的音频文件，此操作不可撤销，一旦删除，文件将无法恢复。请仔细确认是否删除？", "cancel": "取消", "sure": "确认", "ss2RecordShareFileNone": "文件不存在"}}, {"file_path": "docs/o97/SS2HomeFragment_strings.xml", "string_count": 33, "strings": {"ssDualDeviceConnectionTip": "开启或关闭功能，眼镜会自动重启。\n注意：请在重启完成后将眼镜平放于桌面30秒，完成佩戴检测校准。", "chargingNoBatteryDeviceStatus": "设备充电中", "deviceConnectedNoBatteryStatus": "已连接", "deviceSS2ChangeStatus": "充电中 <font color=\"#333333\">|</font> 眼镜电量 %1$d%%", "deviceSS2ConnectedStatus": "已连接 <font color=\"#333333\">|</font> 眼镜电量 %1$d%%", "libs_connecting_device": "连接中", "deviceDisconnectPlsRetry2": "设备已断开，去蓝牙设置中连接设备", "deviceDisconnectPlsRetry": "设备已断开，下拉页面重新连接设备", "ssDualDeviceConnection": "双设备连接", "app_name": "小米眼镜", "ssDualDeviceConnectionDes": "开启后，眼镜可以同时连接两台硬件设备", "ssGestureSettings": "手势设置", "ssNotifySpeech": "通知播报", "ssAutomaticVolume": "音量自动调节", "ssAutomaticVolumeDes": "开启后，眼镜可以根据环境自动调节音量", "ssFastDial": "快捷拨号", "ssDeviceConnectionManagement": "设备连接管理", "ssFindGlasses": "查找眼镜", "ssStandBy": "自动待机时间", "ssStandbyImm": "立即", "ssStandby30s": "30秒", "ssStandby1m": "1分钟", "ssStandby3m": "3分钟", "ssStandbyAuto": "非工作状态下3分钟", "ssGameMode": "游戏模式", "ssGameModeDes": "开启后可降低音频延迟，但音质会受影响", "deviceWearDetection": "佩戴检测", "ss2Record": "录音", "ss2HomeItemTitle": "更多功能", "ssHearingProtection": "听力保护", "open": "开", "close": "关", "libs_retry_exit": "再次退出"}}, {"file_path": "docs/o97/GlassesSettingFragment_strings.xml", "string_count": 26, "strings": {"ssSettingGlasses": "眼镜设置", "ssUnbindTipConnected": "解除绑定后，设备将恢复出厂设置，确认解除绑定设备？\n请在解绑完成后将眼镜平放于桌面30秒，完成佩戴检测校准。", "ssUnbindTipDisconnected": "设备在恢复出厂设置前不可被再次绑定。确认解除绑定设备？", "ss2DeviceSetLightTitle": "充电指示灯", "ss2DeviceSetLightClose": "关闭充电指示灯", "ss2DeviceSetLightDes": "关闭充电指示灯，将无法提示充电状态。确认关闭？", "cancel": "取消", "sure": "确定", "quit": "退出", "libs_sure": "确认", "libs_cancel": "取消", "deviceNotConnectToOTA": "请连接设备后进行升级", "deviceNotConnected": "设备断开，请连接后重试", "deviceFirmWarreUpdate": "固件更新", "deviceUnbind": "解绑", "deviceInfo": "设备信息", "device_unBindTitle": "解绑提醒", "unBindSure": "确认解绑", "unBinding": "解绑中…", "unBindSuccess": "已解除绑定", "unBindFailed": "解绑失败，请重试", "ss2RecordCheckTip3": "正在录音，请稍后重试", "afterSaleSelfTest": "售后自检工具", "restartYourGlasses": "重启眼镜", "restartYourGlassesTip": "重启眼镜将断开所有设备的蓝牙连接，完成后重连。此过程预计需要几分钟，是否确认重启眼镜？\n\n注意：请在重启完成后将眼镜平放于桌面30秒，完成佩戴检测校准。", "deviceNotConnectToRestart": "请连接设备后重启"}}, {"file_path": "docs/o97/AboutAppFragment_strings.xml", "string_count": 16, "strings": {"settingAbout": "关于APP", "checkVersion": "版本检测", "open_source_statement": "开源声明", "evaluationApp": "评价APP", "permissManager": "系统权限管理", "privacyManager": "隐私政策管理", "privacySettings": "隐私设置", "appIcpCode": "APP备案编号", "llmIcpCode": "大模型备案", "resetApp": "重置APP使用引导", "devDeveloper": "开发者选项", "resetGuideConfirm": "重置APP引导意味着完整体验绑定、编辑等全部使用引导，请确认重置？", "No_Network": "网络不可用，请检查网络设置", "app_version_format": "V%s", "ScreenName_SV1_ABOUT_APP": "关于页面", "devDeveloperMode": "开发者模式"}}, {"file_path": "docs/o97/DeviceAboutFragment_strings.xml", "string_count": 11, "strings": {"deviceUpdateTip": "更新日志：", "deviceUpdateSure": "立即更新", "deviceSSUpdateTip": "升级过程中请保持眼镜处于充电状态，其他功能暂不可用", "deviceAboutIsNewVersion": "当前为最新版本", "deviceAboutIsReadyVersion": "最新版本准备就绪，在设备充电后完成升级", "deviceAboutCurrentVersion": "当前版本 V%s", "deviceAboutNewVersion": "最新版本 V%s (%.1fM)", "btConnectKnow": "我知道了", "deviceFirmWarreUpdate": "固件更新", "deviceOtaCheckChangeTitle": "请先为眼镜充电", "deviceOtaCheckChangeDes": "请在眼镜处于充电状态且电量超过30%时重试；升级过程中请保持眼镜处于充电状态。\n\n注意：充电时眼镜将重启，需要在手机蓝牙设置中重新连接眼镜后进行升级。"}}, {"file_path": "docs/o97/AfterSaleTestToolFragment_strings.xml", "string_count": 60, "strings": {"afterSaleToolTitle": "售后自检工具", "afterSaleDeviceName": "设备名称", "afterSaleDeviceSN": "SN", "afterSaleDeviceVersion": "版本号", "afterSaleDeviceBattery": "电池电量", "afterSaleStart": "开始检测", "afterSaleExit": "退出", "afterSaleNeedDischarging": "眼镜充电中，请停止充电后启动检测", "afterSaleEnterFail": "进入售后自检模式出错", "afterSaleNoTicket": "该设备无售后工单", "afterSaleLightPageTitle": "1/5 指示灯检测", "afterSaleLightOnCaption": "请点击按钮，人工检查眼镜指示灯是否正常亮起", "afterSaleLightOffCaption": "请点击按钮，人工检查眼镜指示灯是否正常熄灭", "afterSaleLightOnButton": "点亮指示灯", "afterSaleLightOffButton": "熄灭指示灯", "afterSaleSARPageTitle": "2/5 佩戴检测", "afterSaleSARPageCaption": "请根据提示依次正确完成佩戴/摘下操作，完成后点击右侧已完成按钮", "afterSaleSAROnStatus": "当前状态：已佩戴", "afterSaleSAROffStatus": "当前状态：未佩戴", "afterSaleSAROnCaption": "请摘下眼镜检查状态", "afterSaleSAROffCaption": "请戴上眼镜检测状态", "afterSaleTouchTitle": "3/5 触控检测", "afterSaleTouchPageCaption": "按操作图依次正确完成触控操作，完成后点击右侧已完成按钮", "afterSaleTouchPageCaption2": "为确保检测准确，请将手指按压在镜腿触摸板上，缓慢滑过全部触控区域，过程中请勿抬起手指。", "afterSaleTouchLeftCaption": "单指滑动左镜腿全部触控区域", "afterSaleTouchRightCaption": "单指滑动右镜腿全部触控区域", "afterSaleTouchDoneCaption": "已完成", "afterSaleTouchStatusNormal": "正常", "afterSaleTouchStatusAbnormal": "异常", "afterSaleSpeakerTitle": "4/5 扬声器检测", "afterSaleSpeakerDesCaption": "请点击开始检测", "afterSaleSpeakerDesSubCaption": "人工检查左右扬声器是否正常出声", "afterSaleSpeakerPlayingCaption": "播放中", "afterSaleMicTitle": "5/5 麦克风检测", "afterSaleMicPageCaption": "请点击开始检测，根据提示完成操作", "afterSaleMicLeftCaption": "左镜腿麦克风检测", "afterSaleMicRightCaption": "右镜腿麦克风检测", "afterSaleMicTestingCaption": "请在10s内完成操作：请在距离图示麦克风位置附近，大声读出 1、2、3", "afterSaleMicInTestCaption": "检测中", "afterSaleReportTitle": "检测报告", "afterSaleReportLight": "指示灯检测", "afterSaleReportSar": "佩戴检测", "afterSaleReportTouch": "触控检测", "afterSaleReportSpeaker": "扬声器检测", "afterSaleReportMic": "麦克风检测", "afterSaleReportPass": "通过", "afterSaleReportFail": "未通过", "afterSaleReportButton": "上传检测报告", "afterSaleReportConfirm": "确定", "afterSaleReportCancel": "取消", "afterSaleReportRetry": "重试", "afterSaleFailCommandFail": "与眼镜通信失败。", "afterSaleFailButton": "不通过", "afterSalePassButton": "通过", "afterSaleBluetoothError": "蓝牙状态异常", "afterSaleBluetoothErrorDetails": "请检查手机蓝牙开关是否打开，请检查设备是否在手机附近。操作无效后，可以点击下方按钮退出检测", "afterSaleBluetoothErrorExit": "退出检测", "tip_device_connected": "设备已连接", "ss2DefaultName": "小米智能眼镜", "libs_copy": "已复制到剪贴板"}}, {"file_path": "docs/o97/DeviceNameEditFragment_strings.xml", "string_count": 3, "strings": {"deviceName": "设备名称", "deviceNameTip": "请输入设备名称", "libs_save_failed": "保存失败"}}, {"file_path": "docs/o97/RecordPageFragment_strings.xml", "string_count": 6, "strings": {"ss2RecordTipEnd": "结束录音", "ss2RecordTipEndDes": "眼镜录音即将终止，是否确认操作？", "ss2RecordTipEndError": "蓝牙连接中断，录音已结束", "ss2RecordTipEndDevice": "录音已结束", "cancel": "取消", "sure": "确定"}}, {"file_path": "docs/o97/AutoStandbyFragment_strings.xml", "string_count": 9, "strings": {"ssStandBy": "自动待机时间", "ssStandByTip": "若检测到眼镜已摘下，自动断开蓝牙连接并进入待机。", "ssSetStandByTime": "设置自动待机时间", "ssStandbyImm": "立即", "ssStandby30s": "30秒", "ssStandby1m": "1分钟", "ssStandby3m": "3分钟", "ssStandbyAuto": "非工作状态下3分钟", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态"}}, {"file_path": "docs/o97/SSDeviceInfoFragment_strings.xml", "string_count": 9, "strings": {"deviceInfo": "设备信息", "deviceType": "设备型号", "deviceCode": "序列号", "libs_copy": "已复制", "ssDeviceTypeStr": "MJSS010FC", "sssDeviceTypeStr": "MJSS020FC", "ss2DeviceTypeStr": "XMSS030FC", "ss2DeviceTypeStr_tai": "XMSS031FC", "o95DeviceTypeStr": "M2442G1 "}}, {"file_path": "docs/o97/RecordTranscriptionFragment_strings.xml", "string_count": 36, "strings": {"ss2RecordDelete": "删除文件", "ss2RecordEditName": "修改名称", "text_transcription_speaker": "说话人%d:", "record_wait_transcribe_finish": "正在转写中，请稍后", "action_confirm": "确定", "text_rename_speaker_title": "重命名", "title_transcription": "转写", "title_summary": "总结", "text_re_transcribe": "重新转写", "text_re_summarize": "重新总结", "record_summarying_need_wait": "总结生成中，请稍后", "text_copy_transcribed": "复制转写", "text_copy_summarize": "复制总结", "text_share_audio": "分享音频", "title_generate_transcript": "生成转写", "text_distinguis_speakers": "区分说话人", "text_recording_language": "录音语言", "text_simplified_chinese": "中文简体-普通话", "title_select_summary_type": "请选择总结模版", "text_generate_now": "立即生成", "text_select_recording_language": "请选择录音语言", "device_settings_english": "英文", "cancel": "取消", "sure": "确定", "template_auto_mode": "自动模式", "template_auto_mode_desc": "智能识别场景", "template_meeting": "会议", "template_meeting_desc": "主题、待办事项", "template_interview": "面试", "template_interview_desc": "问答、观点", "template_lecture": "演讲", "template_lecture_desc": "要点、问题", "template_talk": "访谈", "template_talk_desc": "交流、概要", "template_class": "课堂", "template_class_desc": "知识点、讲解"}}, {"file_path": "docs/o97/SS2GestureSettingFragment_strings.xml", "string_count": 35, "strings": {"ssGestureSettings": "手势设置", "ssGestureSlide": "前后滑动", "ssGestureTouch": "轻触2次", "ssGestureLongPress": "长按", "ssSlideLeftTemple": "左镜腿", "ssSlideRightTemple": "右镜腿", "ss2LeftIncomingCall": "左镜腿 (来电时)", "ss2LeftTheNonCall": "左镜腿 (未通话时)", "ss2RightIncomingCall": "右镜腿 (来电时)", "ss2RightTheNonCall": "右镜腿 (未通话时)", "ss2LeftInCall": "左镜腿 (通话时)", "ss2RightInCall": "右镜腿 (通话时)", "dialogleftSlideTitle": "前后滑动左镜腿", "dialogRightSlideTitle": "前后滑动右镜腿", "dialogLeftTouchTitle1": "来电时轻触2次左镜腿", "dialogLeftTouchTitle2": "未通话时轻触2次左镜腿", "dialogRightTouchTitle1": "来电时轻触2次右镜腿", "dialogRightTouchTitle2": "未通话时轻触2次右镜腿", "dialogLeftLongPressTitle1": "通话时长按左镜腿", "dialogLeftLongPressTitle2": "未通话时长按左镜腿", "dialogRightLongPressTitle1": "通话时长按右镜腿", "dialogRightLongPressTitle2": "未通话时长按右镜腿", "dialogGestureNone": "无", "dialogLongPressItem3": "唤起语音助手", "dialogTouchItem2": "播放 / 暂停", "dialogTouchItem4": "接听 / 挂断", "dialogSlideItem1": "下一首 / 上一首", "dialogSlideItem2": "音量+ / 音量-", "dialogLongPressItem4": "断开蓝牙", "dialogLongPressItem1": "拒接电话", "dialogLongPressItem5": "开启 / 结束现场录音", "dialogLongPressItem6": "开启 / 结束通话录音", "dialogTouchItem3": "中断通知播报", "dialogLongPressItem7": "切换隐私模式开关", "cancel": "取消"}}, {"file_path": "docs/o97/NotifySpeechFragment_strings.xml", "string_count": 28, "strings": {"ssNotifySpeech": "通知播报", "ssNotifySpeechTip_ss2": "连接且佩戴眼镜后，手机锁屏时，自动播报通知。此功能依赖系统通知。", "ssNotifySpeechTip": "连接且佩戴眼镜后，手机锁屏时，自动播报通知。此功能依赖系统通知。播报过程中减小音量停止通知播报。", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态", "ssNotifySpeechTip2": "Beta功能在使用中可能会出现错误或不稳定。如果您需要进一步的帮助，请查看 通知无法正常播报？", "ssNotifyNoSpeech": "通知无法正常播报？", "ssNotifySpeechRateSetting": "播报设置", "ssNotifySpeechRateTitle": "播报语速", "ssNotifySpeechListTip": "播报下列通知", "ssNotifySpeechPermission": "通知授权", "ssNotifySpeechPermissionTip": "请在手机的“设置”中授予“APP”通知访问权限", "denyForeverContactsAllow": "被永久拒绝授权，请手动授予通讯录权限", "ssNotifySpeechContactsTip": "我们需要获取通讯录权限，用于通知播报功能开启时对播报短信进行过滤。如果关闭，将不能实现前述功能。", "ssNotifySpeechContent": "播报内容", "ssNotifySpeechRateTip": "播报语速已设置", "ssNotifySpeechRate075x": "0.75x", "ssNotifySpeechRate10x": "1.0x", "ssNotifySpeechRate125x": "1.25x", "ssNotifySpeechRate15x": "1.5x", "ssNotifySpeechRate20x": "2.0x", "ssNotifySpeechTypeAll": "所有通知", "ssNotifySpeechTypePhone": "仅联系人", "ssNotifySpeechItemMessage": "短信", "ssNotifySpeechItemPhone": "电话", "ssNotifySpeechItemMessage1": "信息", "cancel": "取消", "goOpen": "去开启", "eisClose": "关闭"}}, {"file_path": "docs/o97/RecordListFragment_strings.xml", "string_count": 30, "strings": {"ss2RecordEmptyTip": "暂无录音文件", "ss2RecordPhoneFileTip": "录音的音频文件仅存储在手机本地，如卸载应用或清理应用存储，将永久删除本地音频文件，无法恢复。", "ss2RecordShareAudio": "分享音频", "ss2RecordDelete": "删除文件", "ss2RecordGlassesFileTip": "请先将录音文件导出到手机存储。设备已存储：%1$d%%，剩余存储空间：%2$d%%", "ss2RecordNoGetListTip": "当前无法获取文件列表，请稍后重试", "ss2RecordNoConnectTip": "未连接眼镜，无法查看眼镜存储", "ss2RecordExportDoing": "正在导出", "ss2RecordExportFailed": "导出失败", "ss2RecordExportWait": "等待导出", "ss2RecordExport": "导出文件", "ss2RecordExportCancel": "全部取消导出", "ss2RecordDeleteFileTip": "永久删除音频文件", "ss2RecordDeleteFileTipDes": "即将永久删除选中的音频文件，此操作不可撤销，一旦删除，文件将无法恢复。请仔细确认是否删除？", "ss2RecordExportTip": "导出文件到手机存储", "ss2RecordExportTipDes": "录音文件将使用蓝牙传输至手机，传输过程中请不要断开蓝牙和APP。", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态", "ss2RecordTipExport": "确认停止导出？", "ss2RecordTipExportDes": "未导出的文件将停止导出，已导出文件不受影响。是否确认停止？", "ss2RecordPhoneFile": "手机存储", "ss2RecordGlassesFile": "眼镜存储", "libs_cancel": "取消", "allSelect": "全选", "cancel": "取消", "sure": "确定", "libs_retry": "重试", "recordTipToExp": "请先将录音文件导出到手机存储。", "ss2RecordCheckTip3": "正在录音，请稍后重试", "bluetoothNotConnected": "蓝牙已断开，请重连后再试", "libs_empty": ""}}, {"file_path": "docs/o97/HomeRecordFragment_strings.xml", "string_count": 23, "strings": {"ss2Record": "录音", "ss2RecordHomeTip": "眼镜录音将实时传输到App，录制期间请确保App处于活跃状态。录音遇到问题？", "ss2RecordHomeTiplight": "录音遇到问题？", "ss2RecordCall": "通话录音", "ss2RecordCallDes": "拨打系统电话或网络电话时实时录音", "ss2RecordVideo": "音视频录音", "ss2RecordVideoDes": "播放音视频文件时实时录音", "ss2RecordFaceToFace": "现场录音", "ss2RecordFaceToFaceDes": "面对面交谈时实时录音", "ss2RecordTipNoInMusic": "当前未播放音频", "ss2RecordTipDoing": "眼镜正在录音中…", "ss2RecordTipEnd": "结束录音", "ss2RecordTipStart": "录音功能说明", "ss2RecordTipStartDes": "请合理使用录音功能，在使用过程中不得侵犯他人的合法权益，包括但不限于隐私权、知识产权等。此外，不得将相关录音用于非法目的。否则，您应独立承担由此产生的所有法律责任。\n当您录制通话时，会在通话录制开始和结束时发出通知。\n录音文件仅存储在手机本地，不会备份到云端。", "ss2RecordTipStartBtRight": "我知道了", "ss2RecordTipEndDes": "眼镜录音即将终止，是否确认操作？", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态", "ss2RecordTipNoInCall": "当前没有通话", "libs_exit": "退出", "ss2RecordCheckTip1": "眼镜充电中，请断电后重试", "ss2RecordCheckTip2": "正在通话中，请稍后再试", "cancel": "取消", "sure": "确定"}}, {"file_path": "docs/o97/SettingMoreFragment_strings.xml", "string_count": 29, "strings": {"ss2HomeItemTitle": "更多功能", "ssGameMode": "游戏模式", "ssGameModeDes": "开启后可降低音频延迟，但音质会受影响", "ssDualDeviceConnection": "双设备连接", "ssDualDeviceConnectionDes": "开启后，眼镜可以同时连接两台硬件设备", "ss2VolumeMeterTitle": "隐私模式", "ss2VolumeMeterDes": "开启后将会切换调音风格，减少声音外泄", "ss2VoiceControlTitle": "语音控制", "ss2VoiceControlDes": "开启后，可直接对眼镜说小爱同学唤醒手机语音助手，眼镜续航时间将会减少", "ssAutomaticVolume": "音量自动调节", "ssAutomaticVolumeDes": "开启后，眼镜可以根据环境自动调节音量", "ssFastDial": "快捷拨号", "ssStandBy": "自动待机时间", "deviceWearDetection": "佩戴检测", "deviceWearSensitivityWarning": "提示", "deviceWearSensitivityVoiceDesc": "开启功能后，眼镜续航时间将会减少，是否开启？", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态", "ss2VolumeMeterTips": "通话中切换失败，请在通话结束后重试", "ss2RecordCheckTip2": "正在通话中，请稍后再试", "cancel": "取消", "sure": "确定", "close": "关", "open": "开", "configFailed": "设置失败", "ssStandbyImm": "立即", "ssStandby30s": "30秒", "ssStandby1m": "1分钟", "ssStandby3m": "3分钟", "ssStandbyAuto": "非工作状态下3分钟"}}, {"file_path": "docs/o97/HelpFragment_strings.xml", "string_count": 9, "strings": {"o95_pair_method": "准备配对", "settingQA": "帮助中心", "settingHelp": "问题反馈", "customer_phone": "客服电话：950816", "svDefaultName": "MIJIA眼镜相机", "ssDefaultName": "mijia智能音频眼镜", "sssDefaultName": "mijia智能音频眼镜 悦享版", "ss2DefaultName": "mijia智能音频眼镜 2", "o95DefaultName": "小米AI眼镜"}}, {"file_path": "docs/o97/UserAgreementDialog_strings.xml", "string_count": 12, "strings": {"provicyUpdateTitle": "隐私政策更新声明", "provicyTitle": "用户协议与隐私政策", "provicyUpdateDisagree": "退出", "provicyUpdateAgree": "同意", "provicyNoCheckTip": "请勾选同意下方协议", "homeTermsConditions": "《SUPERHEXA用户协议》", "homePrivacyConditions": "《SUPERHEXA隐私政策》", "productPlanning": "用户体验改进计划", "provicyTip": "欢迎使用SUPERHEXA。使用前请详细阅读《SUPERHEXA隐私政策》及《SUPERHEXA用户协议》，我们将依据以上协议和政策提供相关服务。\n\nSUPERHEXA尊重所有使用服务用户的个人隐私权，并会尽全力保护用户的个人信息安全。用户可以使用本应用导出设备中的内容，并对设备进行设置。为提供基本服务，需要联网以及调用你的如下权限或功能，以收集必要的个人信息：\n\n可选权限（用于附加功能，将在使用相关功能时申请）\n1. 访问地理位置\n用于判断是否成功连上附近的设备热点，以进行文件传输、固件升级\n2. 拍摄照片和录制视频\n用于用户反馈拍摄视频、照片", "provicyUpdateTip": "SUPERHEXA隐私政策与用户协议已经更新，前往查看。", "productPlanningTip": "加入《用户体验改进计划》", "provicyAndTerms": "请详细阅读《SUPERHEXA用户协议》与《SUPERHEXA隐私政策》后进行同意"}}, {"file_path": "docs/o97/QuestionFeedbackFragment_strings.xml", "string_count": 44, "strings": {"feedBack": "问题反馈", "feedBackSelectQuestion": "请选择你遇到的问题分类", "questionDesc": "问题描述", "questionContact": "联系方式（QQ/微信/手机号）", "roomLogTip": "上传同账号下所有可能触发协同唤醒设备的日志，用于问题排查", "submit": "提交", "feedBackDeviceNoConnect": "请先连接%s设备", "ssDefaultName": "智能眼镜", "sssDefaultName": "智能眼镜S", "ss2DefaultName": "米家智能音频眼镜", "o95DefaultName": "米家智能音频眼镜", "svDefaultName": "SuperVision", "shareto": "分享到", "questiondescCount": "%s/300", "feedbackConfirm": "你有未提交的反馈，仍退出？", "tip_feedback_low_battery": "设备电量过低", "tip_feedback_low_battery_desc": "设备电量过低，请充电后再试", "tip_feedback_high_temperature": "设备温度过高", "tip_feedback_high_temperature_desc": "设备温度过高，请稍后再试", "tip_feedback_recording": "设备正在录像", "tip_feedback_recording_desc": "设备正在录像，请停止录像后再试", "feedbackNoConnect": "请连接设备，用于获取系统日志", "feedbackConnectAndCommit": "连接设备并提交", "feedbackConnectFailedTitle": "未能连接设备", "feedbackConnectFailed": "未能连接到眼镜设备，设备可能不在连接范围内或处于关闭状态。请确保设备已开机并在附近，重新进行反馈", "feedbackFetching": "正在提交反馈…", "feedbackCreatingWifi": "正在开启设备热点…", "feedbackGetingLog": "正在下载设备日志，大约需要几分钟。请保持眼镜佩戴，避免摘下断连导致上传失败。", "getRoomLogPathFailed": "日志获取失败，请重试", "feedbackNetError": "网络连接异常，请重试", "questionTypeTip": "请选择你遇到的问题分类", "feedbackTooFewWords": "请至少输入6个汉字，以便更详尽地说明问题", "cancel": "取消", "sure": "确定", "libs_ok": "确定", "feedbackSuccess": "反馈已收到", "i_know": "我知道了", "tip_request_enable_wifi": "请连接Wi-Fi", "libs_connecting_device": "正在连接设备…", "libs_feedback_device_connecting": "正在连接设备…", "libs_retry": "重试", "hardcoded_file_not_exist": "文件不存在", "hardcoded_copy_success": "复制成功", "hardcoded_copy_fail": "复制失败"}}, {"file_path": "docs/o97/FindGlassesFragment_strings.xml", "string_count": 8, "strings": {"ssFindGlasses": "查找眼镜", "ssFindGlassesDes": "开启功能后，若眼镜仍在附近，可通过眼镜播放声音，协助您找到设备。摘下眼镜不会自动断开蓝牙，眼镜续航时间将会减少。", "ssPlayAudio": "播放声音", "ssFindGlassesDoing": "正在查找眼镜", "ssStopPlayAudio": "停止播放", "ssDeviceNotConnected": "设备未连接，请检查蓝牙状态", "ssFindGlassesAudioTitle": "音量提醒", "ssFindGlassesAudioDesc": "设备可能正在使用中，请确保已经眼镜摘下再继续。继续佩戴可能会因即将播放的强音量而感到不适。"}}, {"file_path": "docs/o97/DeviceListFragment_strings.xml", "string_count": 29, "strings": {"deviceList": "设备列表", "addDevice": "添加设备", "connected": "已连接", "unconnected": "未连接", "goBind": "去绑定", "emptySetting": "设置", "deviceNameTip": "请输入设备名称", "libs_save_failed": "保存失败", "deviceName": "设备名称", "completeConnecting": "正在连接…", "bindFailedReason": "绑定失败", "checkSolution": "<u>查看解决方案</u>", "bindFailed": "绑定失败", "libs_retry": "重试", "bindSuccess": "绑定成功", "complete": "完成", "libs_learn_basics": "学习基本操作", "completeBinding": "正在绑定…", "searchingHost": "查找设备中…", "pairingTip": "轻点触摸板确认配对", "plsConfirmCodeOnHost": "请在设备屏幕上核对以上编码，\n单击触控板进行确认", "connect_headphones": "连接眼镜", "classtic_ble_tip": "佩戴后，长按双侧触控区，听到提示音后，成功开启配对。然后去“系统-蓝牙”进行连接", "sss_classtic_ble_tip": "佩戴后，长按双侧触控区2秒，听到提示音后，成功进入配对。然后去“系统-蓝牙”进行连接", "goConnect": "已开启配对 去连接", "has_connect_go_bind": "已连接 去绑定", "tips_device_rebind": "%s已恢复出厂设置，\n是否重新绑定？", "action_rebind": "重新绑定", "action_unbind": "解绑"}}, {"file_path": "docs/o97/PermissionState_strings.xml", "string_count": 18, "strings": {"permissionCameraTitle": "访问相机权限", "permissionCameraDesc": "用于用户反馈时拍摄视频。如果关闭，将不能实现前述功能。", "permissionRecordTitle": "访问麦克风权限", "permissionRecordDesc": "用于用户反馈拍摄视频时获取声音。如果关闭，将不能实现前述功能。", "permissionNotificationTitle": "访问通知权限", "permissionNotificationDesc": "在传输文件过程中展示传输进度。如果关闭，将不能实现前述功能。", "permissionStorageTitle": "访问手机存储", "permissionStorageDesc": "用于下载设备拍摄的文件、用户反馈读写手机中的照片。如果关闭，将不能实现前述功能。", "permissionLocationTitle": "访问地理位置", "permissionLocationDesc": "用于判断是否成功连上设备热点，以进行文件传输、固件升级。如果关闭，将不能实现前述功能。", "permissionBlueToothTitle": "访问蓝牙权限", "permissionBlueToothDesc": "蓝牙权限将用于扫描、添加和使用设备。如果关闭，将不能实现前述功能。", "permissionFindNearByDeviceTitle": "访问连接附近设备权限", "permissionFindNearByDeviceDesc": "用于通过蓝牙发现和连接附近设备。如果关闭，将不能实现前述功能。", "permissionReadNotificationTitle": "通知使用权限", "permissionReadNotificationDesc": "在使用过程中，本应用需要访问通知使用权，用于通知播报相关服务。", "permissionAddressBookTitle": "读取通讯录权限", "permissionAddressBookDesc": "在通知播报功能使用过程中，需要使用读取通讯录权限，用于对短信通知进行播报过滤。"}}]}