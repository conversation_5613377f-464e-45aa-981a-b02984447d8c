#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符串资源文件分析脚本
分析 docs/o97 目录下所有 XML 字符串资源文件的结构和内容
"""

import os
import xml.etree.ElementTree as ET
from collections import defaultdict, Counter
import re
from pathlib import Path

def analyze_xml_file(file_path):
    """分析单个XML文件"""
    try:
        # 先读取文件内容并处理XML实体引用
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 替换常见的未定义实体引用
            content = content.replace('&app_name;', 'APP')

        # 使用修改后的内容解析XML
        root = ET.fromstring(content)

        if root.tag != 'resources':
            return None

        strings = {}
        comments = []

        # 解析字符串资源
        for element in root:
            if element.tag == 'string':
                name = element.get('name')
                text = element.text or ''
                strings[name] = text

        # 提取注释（简单处理）
        comment_matches = re.findall(r'<!--(.*?)-->', content, re.DOTALL)
        comments = [comment.strip() for comment in comment_matches]

        return {
            'file_path': file_path,
            'strings': strings,
            'comments': comments,
            'string_count': len(strings)
        }
    except Exception as e:
        print(f"解析文件 {file_path} 时出错: {e}")
        return None

def analyze_all_files(directory):
    """分析目录下所有XML文件"""
    results = []
    all_keys = set()
    key_conflicts = defaultdict(list)
    
    # 扫描所有XML文件
    xml_files = list(Path(directory).glob('*.xml'))
    print(f"发现 {len(xml_files)} 个XML文件")
    
    for xml_file in xml_files:
        result = analyze_xml_file(xml_file)
        if result:
            results.append(result)
            
            # 检查键冲突
            for key, value in result['strings'].items():
                all_keys.add(key)
                key_conflicts[key].append({
                    'file': xml_file.name,
                    'value': value
                })
    
    return results, key_conflicts

def generate_analysis_report(results, key_conflicts):
    """生成分析报告"""
    total_files = len(results)
    total_strings = sum(r['string_count'] for r in results)
    
    print("=" * 60)
    print("字符串资源文件分析报告")
    print("=" * 60)
    print(f"总文件数: {total_files}")
    print(f"总字符串条目数: {total_strings}")
    print()
    
    # 文件详情
    print("文件详情:")
    print("-" * 40)
    for result in sorted(results, key=lambda x: x['string_count'], reverse=True):
        filename = os.path.basename(result['file_path'])
        print(f"{filename:<35} {result['string_count']:>4} 条字符串")
    print()
    
    # 键冲突分析
    conflicts = {k: v for k, v in key_conflicts.items() if len(v) > 1}
    print(f"键冲突分析: 发现 {len(conflicts)} 个重复键")
    print("-" * 40)
    
    if conflicts:
        for key, occurrences in sorted(conflicts.items()):
            print(f"\n键: '{key}' (出现在 {len(occurrences)} 个文件中)")
            for occ in occurrences:
                print(f"  - {occ['file']}: '{occ['value']}'")
    else:
        print("未发现键冲突")
    
    print()
    
    # 常见字符串模式分析
    all_values = []
    for result in results:
        all_values.extend(result['strings'].values())
    
    print("字符串长度分布:")
    print("-" * 40)
    length_ranges = [(0, 10), (11, 30), (31, 50), (51, 100), (101, float('inf'))]
    for min_len, max_len in length_ranges:
        if max_len == float('inf'):
            count = sum(1 for v in all_values if len(v) > min_len)
            print(f"{min_len}+ 字符: {count} 条")
        else:
            count = sum(1 for v in all_values if min_len <= len(v) <= max_len)
            print(f"{min_len}-{max_len} 字符: {count} 条")
    
    return {
        'total_files': total_files,
        'total_strings': total_strings,
        'conflicts': conflicts,
        'results': results
    }

if __name__ == "__main__":
    directory = "docs/o97"
    
    if not os.path.exists(directory):
        print(f"目录 {directory} 不存在")
        exit(1)
    
    print(f"开始分析目录: {directory}")
    results, key_conflicts = analyze_all_files(directory)
    
    if not results:
        print("未找到有效的字符串资源文件")
        exit(1)
    
    analysis_data = generate_analysis_report(results, key_conflicts)
    
    # 保存分析结果到文件
    import json
    with open('analysis_results.json', 'w', encoding='utf-8') as f:
        # 转换为可序列化的格式
        serializable_data = {
            'total_files': analysis_data['total_files'],
            'total_strings': analysis_data['total_strings'],
            'conflicts': {k: v for k, v in analysis_data['conflicts'].items()},
            'file_details': [
                {
                    'file_path': str(r['file_path']),
                    'string_count': r['string_count'],
                    'strings': r['strings']
                }
                for r in analysis_data['results']
            ]
        }
        json.dump(serializable_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析结果已保存到 analysis_results.json")
