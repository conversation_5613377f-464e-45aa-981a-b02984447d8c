[{"key": "sure", "occurrences": [{"file": "WearDetectionFragment_strings.xml", "value": "确定"}, {"file": "RecordPlayFragment_strings.xml", "value": "确认"}, {"file": "GlassesSettingFragment_strings.xml", "value": "确定"}, {"file": "RecordPageFragment_strings.xml", "value": "确定"}, {"file": "RecordTranscriptionFragment_strings.xml", "value": "确定"}, {"file": "RecordListFragment_strings.xml", "value": "确定"}, {"file": "HomeRecordFragment_strings.xml", "value": "确定"}, {"file": "SettingMoreFragment_strings.xml", "value": "确定"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "确定"}], "resolution_strategy": "选择最简洁的通用值", "final_value": "确定", "renamed_keys": []}, {"key": "o95DefaultName", "occurrences": [{"file": "DeviceAddFragment_strings.xml", "value": "小米AI眼镜"}, {"file": "HelpFragment_strings.xml", "value": "小米AI眼镜"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "米家智能音频眼镜"}], "resolution_strategy": "重命名处理，保留第一个，其他添加后缀", "final_value": "小米AI眼镜", "renamed_keys": [{"original_key": "o95DefaultName", "new_key": "o95DefaultName_2", "value": "小米AI眼镜", "source_file": "HelpFragment_strings.xml"}, {"original_key": "o95DefaultName", "new_key": "o95DefaultName_3", "value": "米家智能音频眼镜", "source_file": "QuestionFeedbackFragment_strings.xml"}]}, {"key": "ss2DefaultName", "occurrences": [{"file": "DeviceAddFragment_strings.xml", "value": "mijia智能音频眼镜 2"}, {"file": "AfterSaleTestToolFragment_strings.xml", "value": "小米智能眼镜"}, {"file": "HelpFragment_strings.xml", "value": "mijia智能音频眼镜 2"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "米家智能音频眼镜"}], "resolution_strategy": "重命名处理，保留第一个，其他添加后缀", "final_value": "mijia智能音频眼镜 2", "renamed_keys": [{"original_key": "ss2DefaultName", "new_key": "ss2DefaultName_2", "value": "小米智能眼镜", "source_file": "AfterSaleTestToolFragment_strings.xml"}, {"original_key": "ss2DefaultName", "new_key": "ss2DefaultName_3", "value": "mijia智能音频眼镜 2", "source_file": "HelpFragment_strings.xml"}, {"original_key": "ss2DefaultName", "new_key": "ss2DefaultName_4", "value": "米家智能音频眼镜", "source_file": "QuestionFeedbackFragment_strings.xml"}]}, {"key": "sssDefaultName", "occurrences": [{"file": "DeviceAddFragment_strings.xml", "value": "mijia智能音频眼镜 悦享版"}, {"file": "HelpFragment_strings.xml", "value": "mijia智能音频眼镜 悦享版"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "智能眼镜S"}], "resolution_strategy": "重命名处理，保留第一个，其他添加后缀", "final_value": "mijia智能音频眼镜 悦享版", "renamed_keys": [{"original_key": "sssDefaultName", "new_key": "sssDefaultName_2", "value": "mijia智能音频眼镜 悦享版", "source_file": "HelpFragment_strings.xml"}, {"original_key": "sssDefaultName", "new_key": "sssDefaultName_3", "value": "智能眼镜S", "source_file": "QuestionFeedbackFragment_strings.xml"}]}, {"key": "ssDefaultName", "occurrences": [{"file": "DeviceAddFragment_strings.xml", "value": "mijia智能音频眼镜"}, {"file": "HelpFragment_strings.xml", "value": "mijia智能音频眼镜"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "智能眼镜"}], "resolution_strategy": "重命名处理，保留第一个，其他添加后缀", "final_value": "mijia智能音频眼镜", "renamed_keys": [{"original_key": "ssDefaultName", "new_key": "ssDefaultName_2", "value": "mijia智能音频眼镜", "source_file": "HelpFragment_strings.xml"}, {"original_key": "ssDefaultName", "new_key": "ssDefaultName_3", "value": "智能眼镜", "source_file": "QuestionFeedbackFragment_strings.xml"}]}, {"key": "svDefaultName", "occurrences": [{"file": "DeviceAddFragment_strings.xml", "value": "MIJIA眼镜相机"}, {"file": "HelpFragment_strings.xml", "value": "MIJIA眼镜相机"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "SuperVision"}], "resolution_strategy": "重命名处理，保留第一个，其他添加后缀", "final_value": "MIJIA眼镜相机", "renamed_keys": [{"original_key": "svDefaultName", "new_key": "svDefaultName_2", "value": "MIJIA眼镜相机", "source_file": "HelpFragment_strings.xml"}, {"original_key": "svDefaultName", "new_key": "svDefaultName_3", "value": "SuperVision", "source_file": "QuestionFeedbackFragment_strings.xml"}]}, {"key": "libs_connecting_device", "occurrences": [{"file": "SS2HomeFragment_strings.xml", "value": "连接中"}, {"file": "QuestionFeedbackFragment_strings.xml", "value": "正在连接设备…"}], "resolution_strategy": "默认选择第一个值", "final_value": "连接中", "renamed_keys": []}, {"key": "libs_copy", "occurrences": [{"file": "AfterSaleTestToolFragment_strings.xml", "value": "已复制到剪贴板"}, {"file": "SSDeviceInfoFragment_strings.xml", "value": "已复制"}], "resolution_strategy": "默认选择第一个值", "final_value": "已复制到剪贴板", "renamed_keys": []}]