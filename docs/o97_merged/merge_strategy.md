# 字符串资源合并策略

## 1. 合并目标
将 docs/o97 目录下的26个字符串资源文件合并为一个统一的资源文件，保持原有功能完整性。

## 2. 文件组织结构

### 2.1 合并后的文件结构
```xml
<?xml version="1.0" encoding="utf-8"?>
<!--
  O97项目统一字符串资源文件
  合并自26个Fragment字符串资源文件
  生成时间: [自动生成时间戳]
  
  文件来源统计:
  - 总文件数: 26
  - 总字符串条目数: 552
  - 重复键数量: 52
-->
<resources>
    <!-- ========== 通用字符串 ========== -->
    <!-- 通用UI操作 -->
    
    <!-- ========== 设备管理相关 ========== -->
    <!-- 设备连接、绑定、信息等 -->
    
    <!-- ========== 录音功能相关 ========== -->
    <!-- 录音、播放、转录等功能 -->
    
    <!-- ========== 设置功能相关 ========== -->
    <!-- 各种设置选项 -->
    
    <!-- ========== 其他功能模块 ========== -->
    <!-- 按功能模块分组的其他字符串 -->
    
    <!-- ========== 冲突解决记录 ========== -->
    <!-- 记录所有冲突处理的详细信息 -->
</resources>
```

## 3. 键冲突处理策略

### 3.1 冲突分类
1. **完全一致冲突**：多个文件中键名和值都相同
2. **值不同冲突**：键名相同但值不同
3. **语义冲突**：键名相同但在不同上下文中含义不同

### 3.2 处理规则

#### 规则1：完全一致冲突 - 直接合并
- 如果多个文件中的键值对完全相同，保留一份
- 在注释中记录原始来源文件

#### 规则2：值不同冲突 - 保留最通用的值
- 优先级：通用值 > 具体值
- 例如：`cancel: "取消"` 优于 `cancel: "取消操作"`

#### 规则3：语义冲突 - 重命名处理
- 为不同语义的键添加前缀或后缀
- 例如：`deviceInfo` -> `deviceInfo_glasses`, `deviceInfo_general`

### 3.3 具体冲突处理方案

#### 高频冲突键处理：
1. `cancel` (12个文件) -> 保留 "取消"
2. `sure` (9个文件) -> 保留 "确定" 
3. `ssDeviceNotConnected` (8个文件) -> 保留 "设备未连接，请检查蓝牙状态"
4. `ss2DefaultName` (4个文件) -> 需要根据上下文重命名

## 4. 文件命名和位置
- 合并文件名：`o97_merged_strings.xml`
- 保存位置：`docs/o97_merged/`
- 备份原文件：保持原目录结构不变

## 5. 质量保证
1. **完整性检查**：确保所有原始字符串都被包含
2. **冲突记录**：详细记录所有冲突处理决策
3. **验证测试**：生成验证报告确保合并正确性

## 6. 输出文件
1. `o97_merged_strings.xml` - 合并后的字符串资源文件
2. `merge_report.md` - 详细的合并报告
3. `conflicts_resolution.json` - 冲突处理记录
4. `validation_report.txt` - 验证报告
