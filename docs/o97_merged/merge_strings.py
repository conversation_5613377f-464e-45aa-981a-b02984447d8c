#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符串资源合并脚本
将 docs/o97 目录下的所有XML字符串资源文件合并为一个统一文件
"""

import os
import json
import xml.etree.ElementTree as ET
from collections import defaultdict, OrderedDict
from datetime import datetime
from pathlib import Path
import re

class StringResourceMerger:
    def __init__(self, source_dir, output_dir="docs/o97_merged"):
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.merged_strings = OrderedDict()
        self.conflicts = {}
        self.file_sources = {}  # 记录每个键的来源文件
        self.categories = {
            'common': [],      # 通用字符串
            'device': [],      # 设备相关
            'record': [],      # 录音相关
            'settings': [],    # 设置相关
            'other': []        # 其他
        }
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
    
    def load_analysis_results(self):
        """加载之前的分析结果"""
        try:
            with open('analysis_results.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print("未找到分析结果文件，请先运行 analyze_strings.py")
            return None
    
    def categorize_string(self, key, value):
        """根据键名和值对字符串进行分类"""
        key_lower = key.lower()
        
        # 通用UI字符串
        if key in ['cancel', 'sure', 'complete', 'open', 'close', 'libs_ok', 'libs_cancel']:
            return 'common'
        
        # 设备相关
        if any(keyword in key_lower for keyword in ['device', 'connect', 'bind', 'bluetooth', 'glasses']):
            return 'device'
        
        # 录音相关
        if any(keyword in key_lower for keyword in ['record', 'audio', 'play', 'transcription']):
            return 'record'
        
        # 设置相关
        if any(keyword in key_lower for keyword in ['setting', 'config', 'standby', 'volume', 'gesture']):
            return 'settings'
        
        return 'other'
    
    def resolve_conflict(self, key, occurrences):
        """解决键冲突"""
        if len(occurrences) == 1:
            return occurrences[0]['value'], []
        
        # 检查是否所有值都相同
        unique_values = list(set(occ['value'] for occ in occurrences))
        if len(unique_values) == 1:
            # 完全一致，直接合并
            return unique_values[0], []
        
        # 值不同，需要处理
        conflict_info = {
            'key': key,
            'occurrences': occurrences,
            'resolution_strategy': '',
            'final_value': '',
            'renamed_keys': []
        }
        
        # 特殊处理一些常见冲突
        if key in ['cancel', 'sure']:
            # 通用UI字符串，选择最简洁的
            values_by_length = sorted(unique_values, key=len)
            final_value = values_by_length[0]
            conflict_info['resolution_strategy'] = '选择最简洁的通用值'
            conflict_info['final_value'] = final_value
            return final_value, [conflict_info]
        
        elif 'DefaultName' in key:
            # 设备名称冲突，需要重命名
            renamed_keys = []
            for i, occ in enumerate(occurrences):
                if i == 0:
                    # 第一个保持原名
                    conflict_info['final_value'] = occ['value']
                else:
                    # 其他的重命名
                    new_key = f"{key}_{i+1}"
                    renamed_keys.append({
                        'original_key': key,
                        'new_key': new_key,
                        'value': occ['value'],
                        'source_file': occ['file']
                    })
            
            conflict_info['resolution_strategy'] = '重命名处理，保留第一个，其他添加后缀'
            conflict_info['renamed_keys'] = renamed_keys
            return occurrences[0]['value'], [conflict_info]
        
        else:
            # 默认策略：选择第一个值，记录冲突
            final_value = occurrences[0]['value']
            conflict_info['resolution_strategy'] = '默认选择第一个值'
            conflict_info['final_value'] = final_value
            return final_value, [conflict_info]
    
    def merge_files(self):
        """合并所有文件"""
        analysis_data = self.load_analysis_results()
        if not analysis_data:
            return False
        
        print("开始合并字符串资源...")
        
        # 处理每个文件的字符串
        all_conflicts = []
        for file_data in analysis_data['file_details']:
            file_path = file_data['file_path']
            filename = os.path.basename(file_path)
            
            for key, value in file_data['strings'].items():
                if key not in self.file_sources:
                    self.file_sources[key] = []
                
                self.file_sources[key].append({
                    'file': filename,
                    'value': value
                })
        
        # 解决冲突并合并
        for key, occurrences in self.file_sources.items():
            final_value, conflicts = self.resolve_conflict(key, occurrences)
            self.merged_strings[key] = final_value
            all_conflicts.extend(conflicts)
            
            # 分类
            category = self.categorize_string(key, final_value)
            self.categories[category].append(key)
        
        # 处理重命名的键
        for conflict in all_conflicts:
            for renamed in conflict.get('renamed_keys', []):
                self.merged_strings[renamed['new_key']] = renamed['value']
                category = self.categorize_string(renamed['new_key'], renamed['value'])
                self.categories[category].append(renamed['new_key'])
        
        self.conflicts = all_conflicts
        print(f"合并完成，共处理 {len(self.merged_strings)} 个字符串")
        print(f"发现并处理 {len(all_conflicts)} 个冲突")
        
        return True
    
    def generate_merged_xml(self):
        """生成合并后的XML文件"""
        output_file = os.path.join(self.output_dir, 'o97_merged_strings.xml')
        
        # 创建XML结构
        root = ET.Element('resources')
        
        # 添加文件头注释
        header_comment = f"""
  O97项目统一字符串资源文件
  合并自{len(self.file_sources)}个Fragment字符串资源文件
  生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
  
  统计信息:
  - 总字符串条目数: {len(self.merged_strings)}
  - 处理的冲突数量: {len(self.conflicts)}
  - 通用字符串: {len(self.categories['common'])}
  - 设备相关: {len(self.categories['device'])}
  - 录音相关: {len(self.categories['record'])}
  - 设置相关: {len(self.categories['settings'])}
  - 其他: {len(self.categories['other'])}
"""
        
        # 按分类添加字符串
        category_names = {
            'common': '通用字符串',
            'device': '设备管理相关',
            'record': '录音功能相关', 
            'settings': '设置功能相关',
            'other': '其他功能模块'
        }
        
        for category, keys in self.categories.items():
            if not keys:
                continue
                
            # 添加分类注释
            category_comment = ET.Comment(f' ========== {category_names[category]} ========== ')
            root.append(category_comment)
            
            # 添加该分类的字符串
            for key in sorted(keys):
                if key in self.merged_strings:
                    string_elem = ET.SubElement(root, 'string')
                    string_elem.set('name', key)
                    string_elem.text = self.merged_strings[key]
        
        # 格式化并保存XML
        self._format_xml(root)
        tree = ET.ElementTree(root)
        
        with open(output_file, 'wb') as f:
            f.write('<?xml version="1.0" encoding="utf-8"?>\n'.encode('utf-8'))
            f.write(f'<!--{header_comment}-->\n'.encode('utf-8'))
            tree.write(f, encoding='utf-8', xml_declaration=False)
        
        print(f"合并的XML文件已保存到: {output_file}")
        return output_file
    
    def _format_xml(self, elem, level=0):
        """格式化XML缩进"""
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for child in elem:
                self._format_xml(child, level + 1)
            if not child.tail or not child.tail.strip():
                child.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i

    def generate_reports(self):
        """生成详细报告"""
        # 1. 合并报告
        merge_report_file = os.path.join(self.output_dir, 'merge_report.md')
        with open(merge_report_file, 'w', encoding='utf-8') as f:
            f.write("# O97字符串资源合并报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## 合并统计\n\n")
            f.write(f"- 源文件数量: {len(set(occ['file'] for occs in self.file_sources.values() for occ in occs))}\n")
            f.write(f"- 合并后字符串总数: {len(self.merged_strings)}\n")
            f.write(f"- 处理的冲突数量: {len(self.conflicts)}\n\n")

            f.write("## 分类统计\n\n")
            for category, keys in self.categories.items():
                category_names = {
                    'common': '通用字符串',
                    'device': '设备管理相关',
                    'record': '录音功能相关',
                    'settings': '设置功能相关',
                    'other': '其他功能模块'
                }
                f.write(f"- {category_names[category]}: {len(keys)} 条\n")

            f.write("\n## 冲突处理详情\n\n")
            if self.conflicts:
                for i, conflict in enumerate(self.conflicts, 1):
                    f.write(f"### 冲突 {i}: {conflict['key']}\n\n")
                    f.write(f"**处理策略**: {conflict['resolution_strategy']}\n\n")
                    f.write(f"**最终值**: `{conflict['final_value']}`\n\n")

                    if conflict.get('renamed_keys'):
                        f.write("**重命名处理**:\n")
                        for renamed in conflict['renamed_keys']:
                            f.write(f"- `{renamed['original_key']}` -> `{renamed['new_key']}` (来源: {renamed['source_file']})\n")
                        f.write("\n")

                    f.write("**原始冲突**:\n")
                    for occ in conflict['occurrences']:
                        f.write(f"- {occ['file']}: `{occ['value']}`\n")
                    f.write("\n")
            else:
                f.write("未发现需要特殊处理的冲突。\n")

        # 2. 冲突处理记录JSON
        conflicts_file = os.path.join(self.output_dir, 'conflicts_resolution.json')
        with open(conflicts_file, 'w', encoding='utf-8') as f:
            json.dump(self.conflicts, f, ensure_ascii=False, indent=2)

        # 3. 验证报告
        validation_file = os.path.join(self.output_dir, 'validation_report.txt')
        with open(validation_file, 'w', encoding='utf-8') as f:
            f.write("O97字符串资源合并验证报告\n")
            f.write("=" * 50 + "\n\n")

            # 完整性检查
            original_count = sum(len(file_data['strings']) for file_data in self.load_analysis_results()['file_details'])
            merged_count = len(self.merged_strings)
            unique_keys = len(self.file_sources)

            f.write(f"完整性检查:\n")
            f.write(f"- 原始字符串总数: {original_count}\n")
            f.write(f"- 唯一键数量: {unique_keys}\n")
            f.write(f"- 合并后字符串数: {merged_count}\n")
            f.write(f"- 重复键合并节省: {original_count - unique_keys} 条\n")

            # 检查是否有键丢失
            expected_count = unique_keys + sum(len(c.get('renamed_keys', [])) for c in self.conflicts)
            if merged_count >= expected_count:
                f.write("✓ 完整性检查通过，所有唯一键都已保留\n\n")
            else:
                f.write("✗ 完整性检查失败，可能有字符串丢失\n\n")

            # 冲突处理检查
            f.write(f"冲突处理检查:\n")
            f.write(f"- 发现冲突: {len(self.conflicts)}\n")
            f.write(f"- 已处理冲突: {len(self.conflicts)}\n")
            f.write("✓ 所有冲突已处理\n\n")

            # 分类检查
            total_categorized = sum(len(keys) for keys in self.categories.values())
            f.write(f"分类检查:\n")
            f.write(f"- 已分类字符串: {total_categorized}\n")
            f.write(f"- 总字符串数: {merged_count}\n")
            if total_categorized == merged_count:
                f.write("✓ 所有字符串已正确分类\n")
            else:
                f.write("✗ 存在未分类的字符串\n")

        print(f"报告已生成:")
        print(f"- 合并报告: {merge_report_file}")
        print(f"- 冲突记录: {conflicts_file}")
        print(f"- 验证报告: {validation_file}")

def main():
    """主函数"""
    source_dir = "docs/o97"

    if not os.path.exists(source_dir):
        print(f"源目录 {source_dir} 不存在")
        return

    merger = StringResourceMerger(source_dir)

    # 执行合并
    if merger.merge_files():
        # 生成XML文件
        xml_file = merger.generate_merged_xml()

        # 生成报告
        merger.generate_reports()

        print("\n合并完成！输出文件:")
        print(f"- 合并的XML文件: {xml_file}")
        print(f"- 详细报告请查看 {merger.output_dir} 目录")
    else:
        print("合并失败")

if __name__ == "__main__":
    main()
