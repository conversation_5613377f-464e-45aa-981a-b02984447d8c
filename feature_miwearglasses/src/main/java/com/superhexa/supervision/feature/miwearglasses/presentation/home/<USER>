package com.superhexa.supervision.feature.miwearglasses.presentation.home

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.core.net.toUri
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95State
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.RecordStatus
import com.superhexa.supervision.feature.channel.presentation.newversion.nearby.MMANearbyManager
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.presentation.alert.AlertCameraDialogFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.alert.AlertNotEnoughFileSpaceDialogFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.collaboration.MiWearCameraUpgradeChecker
import com.superhexa.supervision.feature.miwearglasses.presentation.home.component.DeviceLogoState
import com.superhexa.supervision.feature.miwearglasses.presentation.home.component.DeviceStateView
import com.superhexa.supervision.feature.miwearglasses.presentation.home.component.FileSpaceManager
import com.superhexa.supervision.feature.miwearglasses.presentation.home.component.HomeItemFeatureManager
import com.superhexa.supervision.feature.miwearglasses.presentation.home.component.PaddedBox
import com.superhexa.supervision.feature.miwearglasses.presentation.home.component.UseTips
import com.superhexa.supervision.feature.miwearglasses.presentation.media.MediaTransManager
import com.superhexa.supervision.feature.miwearglasses.presentation.ota.MiWearOTADownloadHelper
import com.superhexa.supervision.feature.miwearglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.miwearglasses.presentation.setting.MiWearSettingFragment.Companion.Un_Support_Flag
import com.superhexa.supervision.feature.xiaoai.presentation.observer.LocationPolicy
import com.superhexa.supervision.feature.xiaoai.utils.XiaoaiMemoryNotificationHelper
import com.superhexa.supervision.library.base.basecommon.config.ConstantUrls
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_172
import com.superhexa.supervision.library.base.basecommon.theme.Dp_264
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.engine.listener.ISpeechChatListener
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2025/5/31 on 11:28
 * 作者: qintaiyuan
 */
class MiWearHomeFragment : MiWearHomeBaseFragment() {
    private val appEnvironment by instance<AppEnvironment>()
    private val memoryNotificationHelper by lazy { context?.let { XiaoaiMemoryNotificationHelper(it) } }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeDeviceState()
        reportO95Statistic()
        MiWearOTADownloadHelper.reset()
        MMANearbyManager.bindMMANearby(this, bondDevice?.model, bondDevice?.mac)
        MediaTransManager.recoverTransFragment(this)
        memoryNotificationHelper?.let {
            AiSpeechEngine.INSTANCE.addChatDataObserver(
                key = it.MEMORY_NOTIFICATION_KEY,
                lifecycle = viewLifecycleOwner.lifecycle,
                chatObserver
            )
        }
    }

    private fun observeDeviceState() {
        viewModel.deviceStateLiveData?.runCatching {
            observeState(viewLifecycleOwner, O95State::updateInfo) {
                syncDeviceUpdateState(it)
            }
            observeState(viewLifecycleOwner, O95State::alertStatus) {
                syncAlertState(it)
            }
            observeState(viewLifecycleOwner, O95State::deviceState) {
                syncDeviceConnectState(it)
            }
        }

        viewModel.notEnoughSpaceShowLiveData.observe(viewLifecycleOwner) { notEnough ->
            syncFileSpaceTipState(notEnough)
        }
    }

    private fun reportO95Statistic() {
        val pair = O95Statistic.exposeHomePair()
        O95Statistic.exposeV3Name(
            pair.first,
            pair.second,
            hasConnectStatus = true,
            connectStatus = viewModel.deviceStateLiveData?.isSuccessState() ?: false
        )

        O95Statistic.login()
    }

    override val contentView: @Composable () -> Unit = {
        val deviceState = viewModel.deviceStateLiveData?.observeAsState()
        val recordStatus = viewModel.isRecording.collectAsState()
        val mState = viewModel.mState.collectAsState()
        Column {
            // ===== 设备状态区域 =====
            DeviceStateView(mState, deviceState)
            // ===== 文件传输区域 =====
            DeviceLogoState(mState, deviceState) {
                navigateIfJointReady {
                    MediaTransManager.orderMediaTrans(this@MiWearHomeFragment, "home_page")
                    O95Statistic.clickHomePageEvent("mediafile_import")
                }
            }
            // ===== 文件空间入口 =====
            PaddedBox {
                FileSpaceManager {
                    HexaRouter.AudioGlasses.navigateToFileSpace(this@MiWearHomeFragment)
                    O95Statistic.clickHomePageEvent("photo_video")
                }
            }
            // ===== 功能项模块 =====
            Spacer(modifier = Modifier.height(Dp_12))
            PaddedBox {
                HomeFeatureItems(mState, deviceState, recordStatus)
            }
            // ===== 使用指南标题 =====
            Spacer(modifier = Modifier.height(Dp_12))
            UseTips {
                toO95Feedback()
            }
        }
    }

    @Suppress("LongMethod", "MagicNumber")
    @Composable
    private fun HomeFeatureItems(
        homeStateState: State<MiWearHomeState>,
        deviceState: State<O95State?>?,
        recordStatus: State<Int>
    ) {
        val supportRecord =
            deviceState?.value?.deviceCapability?.supportRecord
        val supportHub =
            deviceState?.value?.deviceCapability?.supportHub

        val features = mutableListOf(
            FeatureItem(
                icon = when (recordStatus.value) {
                    // TODO 等设计出图以后要替换资源
                    RecordStatus.RECORD_ING -> R.drawable.ic_o95_recoder
                    RecordStatus.RECORD_PAUSE -> R.drawable.ic_o95_recoder
                    else -> R.drawable.ic_o95_recoder
                },
                title = when (recordStatus.value) {
                    RecordStatus.RECORD_ING -> R.string.libs_device_recording
                    RecordStatus.RECORD_PAUSE -> R.string.libs_device_recorder_paused
                    else -> R.string.libs_device_recorder
                },
                onClick = {
                    if (supportRecord == false) {
                        toast(R.string.libs_please_update_device)
                    } else {
                        HexaRouter.MiSpeechHub.navigateToRecord(this)
                        O95Statistic.clickHomePageEvent("voice_recording")
                    }
                }
            ),
            FeatureItem(
                icon = R.drawable.ic_o95_translate,
                title = R.string.libs_device_translate,
                onClick = {
                    if (supportHub == false) {
                        toast(R.string.libs_please_update_device)
                    } else {
                        HexaRouter.MiSpeechHub.navigateToSpeechHub(this)
                        O95Statistic.clickHomePageEvent("voice_translate")
                    }
                }
            ),
            FeatureItem(
                icon = R.drawable.ic_o95_calorie,
                title = R.string.libs_device_calorie_tracking,
                onClick = {
                    HexaRouter.Calorie.navigateToCalorieHome(this)
                    O95Statistic.clickHomePageEvent("voice_calorie")
                }
            ),
            FeatureItem(
                icon = R.drawable.ic_xiaoai_record,
                title = R.string.libs_xiaoai_record,
                onClick = {
                    HexaRouter.Xiaoai.navigateToChatHistory(this)
                    O95Statistic.clickHomePageEvent("ai_history")
                }
            ),
            FeatureItem(
                icon = R.drawable.ic_miwear_glasses_setting,
                title = R.string.libs_glasses_setting,
                showDot = showDeviceDot.value,
                onClick = {
                    val boundDeviceId = viewModel.getBoundDeviceSid()
                    val deviceId = bondDevice?.sid.orEmpty()
                    Timber.i("enterSetting sid:$deviceId, $boundDeviceId")
                    val did = if (boundDeviceId != deviceId) {
                        boundDeviceId
                    } else {
                        deviceId
                    }
                    HexaRouter.Settinng.navigateToSettings(this, did)
                    O95Statistic.clickHomePageEvent("device_settings")
                }
            ),
            FeatureItem(
                icon = R.drawable.ic_memory,
                title = R.string.libs_xiaoai_memory,
                onClick = {
                    if (!requireActivity().isFinishing &&
                        !requireActivity().isDestroyed &&
                        !requireActivity().supportFragmentManager.isStateSaved
                    ) {
                        HexaRouter.Memory.navigateToMemoryInterface(this)
                        O95Statistic.clickHomePageEvent("ai_memory")
                    }
                }
            )
        )
        if (homeStateState.value.tempControl != Un_Support_Flag &&
            appEnvironment.isMIUI() &&
            MiWearCameraUpgradeChecker.isSupportConnectivity(requireContext())
        ) {
            features.add(
                FeatureItem(
                    icon = R.drawable.ic_o95_camera,
                    title = R.string.libs_camera_collaboration,
                    onClick = {
                        HexaRouter.Web.navigateToLegalTermsWebView(
                            fragment = this,
                            terms = LegalTermsAction.Permalink(ConstantUrls.CAMERA_COORD)
                        )
                        O95Statistic.clickHomePageEvent("voice_camera_collaboration")
                    }
                )
            )
        }
        features.add(
            FeatureItem(
                icon = R.drawable.ic_o95_alipay,
                title = R.string.libs_alipay,
                showDot = showDeviceDot.value,
                onClick = {
                    HexaRouter.Alipay.navigateToAliPayHome(this)
                    O95Statistic.clickHomePageEvent("alipay")
                }
            )
        )

        LazyVerticalGrid(
            columns = GridCells.Fixed(LINE_SPAN),
            horizontalArrangement = Arrangement.spacedBy(Dp_12),
            verticalArrangement = Arrangement.spacedBy(Dp_12),
            modifier = Modifier
                .fillMaxWidth()
                .height(if (features.size > 6) Dp_264 else Dp_172)
        ) {
            items(features) { item ->
                HomeItemFeatureManager(
                    icRes = item.icon,
                    titleRes = item.title,
                    showDevicesDot = item.showDot,
                    onContentClick = item.onClick
                )
            }
        }

        FeatureItem(
            icon = R.drawable.ic_xiaoai_record,
            title = R.string.libs_xiaoai_record,
            onClick = {
                HexaRouter.Xiaoai.navigateToChatHistory(this)
                O95Statistic.clickHomePageEvent("ai_history")
            }
        )
    }

    @Suppress("LongMethod", "ComplexMethod")
    override fun onGuideItemClick(item: GuideItem) {
        when (item.guideType) {
            GuideType.BasicMore -> {
                HexaRouter.AudioGlasses.navigateToMoreUsageGuide(this)
            }

            GuideType.ExtendMore -> {
                HexaRouter.AudioGlasses.navigateToSellingPointsGuide(this)
            }

            GuideType.XiaoAiMore -> {
                HexaRouter.AudioGlasses.navigateToAiSpeechGuide(this)
            }

            GuideType.Photo -> {
                navigateIfJointReady {
                    HexaRouter.AudioGlasses.navigateToPhotoGuide(this)
                }
            }

            GuideType.Music -> {
                navigateIfJointReady {
                    HexaRouter.AudioGlasses.navigateToMusicGuide(this)
                }
            }

            GuideType.Lenses -> {
                navigateIfJointReady {
                    HexaRouter.AudioGlasses.navigateToLensAdjustGuide(this)
                }
            }

            GuideType.Phone -> {
                navigateIfJointReady {
                    HexaRouter.AudioGlasses.navigateToCallUsageGuide(this)
                }
            }

            GuideType.Recording -> {
                immersiveStatusBar()
                HexaRouter.AudioGlasses.navigateToRecordingGuide(this)
            }

            GuideType.Voice -> {
                immersiveStatusBar()
                HexaRouter.AudioGlasses.navigateToAiSpeechAssistantItem(this)
            }

            GuideType.BaiKe -> {
                immersiveStatusBar()
                HexaRouter.AudioGlasses.navigateToAiSpeechBaikeItemFragment(this)
            }

            GuideType.XiaoAiMusic -> {
                immersiveStatusBar()
                HexaRouter.AudioGlasses.navigateToAiSpeechMusicItemFragment(this)
            }

            GuideType.XiaoAiSearch -> {
                immersiveStatusBar()
                HexaRouter.AudioGlasses.navigateToAiSpeechChatItemFragment(this)
            }

            GuideType.Translate -> {
                immersiveStatusBar()
                HexaRouter.AudioGlasses.navigateToTranslateGuide(this)
            }

            GuideType.Calories -> {
                immersiveStatusBar()
                HexaRouter.AudioGlasses.navigateToCaloriesGuide(this)
            }
        }
        item.event?.let { O95Statistic.clickHomePageEvent(it) }
    }

    private fun syncFileSpaceTipState(notEnough: Boolean) {
        if (notEnough) {
            PriorityDialogManager.showDialog(
                AlertNotEnoughFileSpaceDialogFragment(),
                childFragmentManager,
                "AlertNotEnoughSpaceDialogFragment",
                DialogPriority.MEDIUM
            )
        }
    }

    private fun syncDeviceConnectState(deviceState: DeviceState?) {
        if (deviceState == null) return
        val lastCheckTime = MMKVUtils.decodeLong(ConstsConfig.CAMERA_CHECK_UPGRADE)
        val isReCheck = System.currentTimeMillis() - lastCheckTime >= HOUR_OF_24
        val channelStatus = deviceState.isChannelSuccess()
        Timber.d("syncDeviceConnectState called $channelStatus, recheck:$isReCheck")
        val deviceInfo = viewModel.deviceStateLiveData?.value?.deviceInfo
        Timber.d("syncDeviceConnectState curBondDevice:$bondDevice, deviceInfo:$deviceInfo")
        if (channelStatus) {
            viewModel.sendEvent(MiWearHomeEvent.InitTempControlState)
            bondDevice?.let {
                O95Statistic.setCommonParams(
                    deviceSn = it.sn ?: "",
                    deviceVersion = deviceInfo?.firmwareVersion ?: "",
                    deviceModel = it.model ?: deviceInfo?.model ?: "",
                    deviceDid = it.deviceId.toString()
                )
            }
            LocationPolicy.getLocation(requireActivity()) { location ->
                Timber.d("眼镜链接成功并获取到定位否则唤醒小爱同学定位可能是空 $location")
            }
        } else {
            O95Statistic.clearCommonParams()
        }
        if (channelStatus && isReCheck) {
            MMKVUtils.encode(ConstsConfig.CAMERA_CHECK_UPGRADE, System.currentTimeMillis())
            val showUpgrade = MiWearCameraUpgradeChecker.checkConnectivityUpgrade(requireContext())
            Timber.d("syncDeviceConnectState called showUpgrade:$showUpgrade")
            if (showUpgrade) {
                PriorityDialogManager.showDialog(
                    AlertCameraDialogFragment().addCallback(
                        onAction = {
                            Timber.d("user click `upgrade` btn ")
                            jumpToBrowser()
                        }
                    ),
                    childFragmentManager,
                    "AlertCameraDialogFragment",
                    DialogPriority.MEDIUM
                )
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun jumpToBrowser() {
        Timber.d("jumpToBrowser called")
        try {
            val url = "mimarket://browse?" +
                "url=https%3A%2F%2Fapp.market.xiaomi.com" +
                "%2Fhd%2Fapm-h5-cdn%2Fcdn-eyeglasses-upgrade.html%3Fa_hide%3Dtrue"
            val intent = Intent(Intent.ACTION_VIEW, url.toUri())
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        } catch (e: Exception) {
            // 处理无浏览器的情况（例如提示用户）
            Timber.d("jumpToBrowser exception:${e.message}")
            toast("未找到对应的应用")
        }
    }

    private fun toO95Feedback() {
        val url = ConstantUrls.Q_A_URL_O95
        Timber.d("O95Feedback url :$url")
        HexaRouter.Web.navigateToWebView(
            fragment = this,
            isJavaScriptEnabled = true,
            url = url,
            extentText = getString(R.string.settingHelp)
        ) {
            HexaRouter.Profile.navigateToQuestionFeedback(
                this,
                bondDevice?.model ?: DeviceModelManager.o95cnModel
            )
        }
    }
    private val chatObserver = object : ISpeechChatListener {
        override fun onParkingCard(
            dialogId: String?,
            sessionId: String?,
            title: String,
            subTitle: String,
            url: String,
            instructionJson: String?
        ) {
            val notificationId = dialogId?.hashCode() ?: 0
            Timber.d("弹通知回调成功, dialogId = ${dialogId?.hashCode()}")
            memoryNotificationHelper?.showXiaoaiMemoryNotification(
                title = title,
                content = subTitle,
                url = url,
                notificationId = notificationId
            )
        }

        override fun onQueryRecognize(
            sessionId: String?,
            dialogId: String?,
            query: String?,
            isFinal: Boolean,
            isFromPostImageForLinkImgId: Boolean?,
            instructionJson: String?,
            streamId: String?
        ) {
            // do nothing
        }

        override fun onTextResponseSynthesizer(
            sessionId: String?,
            dialogId: String?,
            result: String?,
            isFinal: Boolean,
            instructionJson: String?,
            streamId: String?
        ) {
            // do nothing
        }

        override fun onAlipayTextResponseSynthesizer(
            sessionId: String?,
            dialogId: String?,
            result: String?,
            isFinal: Boolean,
            instructionJson: String?,
            streamId: String?
        ) {
            // do nothing
        }

        override fun addToChatHistory(
            sessionId: String?,
            dialogId: String,
            content: String,
            type: Int,
            timestamp: Long
        ) {
            // do nothing
        }
    }
}
