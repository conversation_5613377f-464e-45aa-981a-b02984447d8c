package com.superhexa.supervision.feature.miwearglasses.presentation.home.component

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.constraintlayout.compose.ChainStyle
import androidx.constraintlayout.compose.ConstraintLayout
import com.superhexa.supervision.library.base.basecommon.compose.CircleDot
import com.superhexa.supervision.library.base.basecommon.compose.model.ShapeType
import com.superhexa.supervision.library.base.basecommon.compose.tools.roundedCornerShape
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_80
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

/**
 * 类描述:
 * 创建日期: 2025/3/24 on 16:49
 * 作者: qintaiyuan
 */
@Suppress("LongMethod")
@Composable
internal fun HomeItemFeatureManager(
    showDevicesDot: Boolean = false,
    @DrawableRes icRes: Int,
    @StringRes titleRes: Int,
    onContentClick: () -> Unit = {}
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxSize()
            .height(height = Dp_80)
            .clip(roundedCornerShape(ShapeType.CustomRounded(Dp_16)))
            .clickDebounce {
                onContentClick.invoke()
            }
            .background(Color18191A)
    ) {
        val (ic, pace, name, devicesDot) = createRefs()
        createVerticalChain(ic, pace, name, chainStyle = ChainStyle.Packed)
        Image(
            painter = painterResource(id = icRes),
            contentDescription = "icLogo",
            modifier = Modifier
                .size(Dp_28)
                .constrainAs(ic) {
                    top.linkTo(parent.top)
                    bottom.linkTo(pace.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
        )
        if (showDevicesDot) {
            CircleDot(
                modifier = Modifier.constrainAs(devicesDot) {
                    top.linkTo(ic.top)
                    end.linkTo(ic.end)
                }
            )
        }
        Spacer(
            modifier = Modifier.constrainAs(pace) {
                top.linkTo(ic.bottom)
                bottom.linkTo(name.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }.height(Dp_8)
        )
        Text(
            text = stringResource(id = titleRes),
            style = TextStyle(
                color = ColorWhite40,
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                lineHeight = Sp_13
            ),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            modifier = Modifier.constrainAs(name) {
                top.linkTo(pace.bottom)
                bottom.linkTo(parent.bottom)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }
        )
    }
}
