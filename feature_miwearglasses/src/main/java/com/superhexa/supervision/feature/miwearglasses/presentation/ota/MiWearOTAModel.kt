package com.superhexa.supervision.feature.miwearglasses.presentation.ota

import androidx.annotation.Keep
import com.superhexa.supervision.library.base.basecommon.commonbean.MiWearUpgradeInfo
import com.superhexa.supervision.library.base.presentation.mvi.UiEffect
import com.superhexa.supervision.library.base.presentation.mvi.UiEvent
import com.superhexa.supervision.library.base.presentation.mvi.UiState

/**
 * 类描述:
 * 创建日期: 2024/9/29 on 11:01
 * 作者: qintaiyuan
 */
@Keep
data class MiWearOTAState(
    val deviceVersion: String = "",
    val updateDesc: String = "",
    val showLowBattery: Boolean = false,
    val minBatteryTip: String = "",
    val showLowStorage: Boolean = false,
    val showDeviceBusyTips: Boolean = false,
    val showDeviceNetErrorTips: Boolean = false,
    val deviceUpdateState: DeviceOTAState = DeviceOTAState.LatestVersion,
    val temperatureTip: String = "",
    val showTemperatureDlg: Boolean = false,
    val showUpgradeFail: Boolean = false,
    val upgradeFailTitle: String = "",
    val upgradeFailTip: String = "",
    val upgradeP2PConnectFail: Boolean = false
) : UiState

@Keep
sealed class DeviceOTAState {
    object LatestVersion : DeviceOTAState()
    object Update : DeviceOTAState()
    object PreparingUpdate : DeviceOTAState()
    data class Downloading(var progress: Int = 0) : DeviceOTAState()
    data class Installing(var progress: Int = 0) : DeviceOTAState()
    object Restarting : DeviceOTAState()
    object OTAFailed : DeviceOTAState()
    object OTASuccess : DeviceOTAState()
    data class Downloaded(val filePath: String, val md5: String) : DeviceOTAState()
    data class Uploading(val progress: Int) : DeviceOTAState()
}

@Keep
sealed class MiWearOTAEvent : UiEvent {
    data class InitData(val otaState: Int = 1, val call: () -> Unit) : MiWearOTAEvent()
    data class SwitchLowBatteryState(val show: Boolean) : MiWearOTAEvent()
    data class SwitchLowStorageState(val show: Boolean) : MiWearOTAEvent()
    data class SwitchDeviceBusyState(val show: Boolean) : MiWearOTAEvent()
    data class SwitchDeviceNetErrorState(val show: Boolean) : MiWearOTAEvent()
    data class SwitchTemperatureState(val show: Boolean) : MiWearOTAEvent()
    data class SwitchUpgradeFailState(val show: Boolean) : MiWearOTAEvent()
    data class SwitchP2PConnectFailState(val show: Boolean) : MiWearOTAEvent()
    object StartOta : MiWearOTAEvent()
    data class CheckUpdateState(val call: (MiWearUpgradeInfo?) -> Unit) : MiWearOTAEvent()
    data class UploadState(val uploadState: MiWearOTAUploadCallback) : MiWearOTAEvent()
    object OnDeviceDisConnected : MiWearOTAEvent()
    object OnDeviceChannelSuccess : MiWearOTAEvent()
    object Reset : MiWearOTAEvent()
    object OnExit : MiWearOTAEvent()
}

@Keep
sealed class MiWearOTAEffect : UiEffect {
    object DeviceDisconnected : MiWearOTAEffect()
}

@Keep
enum class OTAType {
    WIFI,
    NET
}
