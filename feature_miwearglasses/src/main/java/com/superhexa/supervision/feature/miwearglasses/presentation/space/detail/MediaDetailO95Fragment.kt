@file:Suppress("TooGenericExceptionCaught", "TooManyFunctions", "LongMethod", "ComplexMethod")

package com.superhexa.supervision.feature.miwearglasses.presentation.space.detail

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.activity.OnBackPressedCallback
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.viewpager.widget.ViewPager
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.databinding.FragmentO95MediaDetailBinding
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.FileSpaceHelper
import com.superhexa.supervision.feature.miwearglasses.presentation.space.RevisedStatusViewModel
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95FragmentViewModel.Companion.Pause
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95FragmentViewModel.Companion.Play
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95FragmentViewModel.Companion.Stop
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95DelFileEvent
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95NetDisconnectEvent
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95TranBeginAndRefreshEvent
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet3ButtonTitleDes
import com.superhexa.supervision.library.base.basecommon.compose.DisplayMode
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.event.EventBusLifecycleObserverEx
import com.superhexa.supervision.library.base.basecommon.extension.getBinderContent
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.tools.IntentUtils
import com.superhexa.supervision.library.base.basecommon.tools.StatusBarUtil
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.presentation.viewmodel.getViewModel
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.MediaType.Mp4_Type
import com.superhexa.supervision.library.db.MediaType.Video_Folder
import com.superhexa.supervision.library.db.MediaType.Video_Type
import com.superhexa.supervision.library.db.bean.MediaBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import tv.danmaku.ijk.media.exo2.Exo2PlayerManager
import java.io.File

/**
 * 类描述:视频或图片详情页
 * 创建日期:2021/7/20 on 10:41 下午
 * 作者: FengPeng
 */
@ExperimentalCoroutinesApi
@SuppressLint("BinaryOperationInTimber", "LongMethod")
class MediaDetailO95Fragment : InjectionFragment(R.layout.fragment_o95_media_detail) {
    private val viewBinding: FragmentO95MediaDetailBinding by viewBinding()
    private val isShowDelete = mutableStateOf(false)
    private var isSensorPortrait = true
    private lateinit var sfm: FragmentManager
    private lateinit var adapter: MediaDetailO95PagerAdapter
    private val revisedStatusViewModel: RevisedStatusViewModel by activityViewModels()

    private val viewModel by lazy {
        getViewModel(
            requireActivity(),
            MediaDetailO95FragmentViewModel::class.java
        )
    }

    override fun needDefaultbackground(): Boolean {
        return false
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBusLifecycleObserverEx(this)
        if (LibBaseApplication.instance.resources.configuration.isScreenWideColorGamut) {
            activity?.window?.let {
                it.colorMode = ActivityInfo.COLOR_MODE_WIDE_COLOR_GAMUT
            }
        }
    }

    @SuppressLint("SourceLockedOrientationActivity")
    override fun rotateConfig() {
        Timber.d("requestedOrientation:${requireActivity().requestedOrientation}")
        requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
    }

    @Suppress("MaxLineLength")
    @SuppressLint("SourceLockedOrientationActivity")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        registerSystemBackPressedCallback()
        sfm = childFragmentManager
        Timber.d("checkRotation now is %s", resources.configuration.orientation)
        checkRotationConfiguration()
        PlayerFactory.setPlayManager(Exo2PlayerManager::class.java)
        arguments?.let { bundle ->
            val position = bundle.getInt(BundleKey.Position, 0)
            val tmpList = bundle.getBinderContent<MutableList<MediaBean>>(BundleKey.MediaContenList)
            if (tmpList == null) {
                Timber.i("未获取到视频列表，返回上一页 tmpList:$tmpList")
                onBackPressed()
                return
            }
            tmpList.let { list ->
                viewModel.mutableList.value = list
                initViewPager(position, list)
                if (position < list.size) {
                    initListener(list[position].mimeType)
                }
            }
        }

        viewBinding.ifvSwitch.setOnClickListener {
            if (isSensorPortrait) {
                isSensorPortrait = false
                requireActivity().requestedOrientation =
                    ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
            } else {
                isSensorPortrait = true
                requireActivity().requestedOrientation =
                    ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
            }
        }
        viewBinding.composeBottomSheet.setContent {
            DeleteDialog()
        }

        // 新增替换图片按钮点击事件
        viewBinding.ivRevised.clickDebounce(viewLifecycleOwner, intervalTime) {
            displayCalibratedImage()
        }
    }

    // 注册系统返回监听器
    private fun registerSystemBackPressedCallback() {
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    // 1. 保存当前 bean
                    val currentItem = viewBinding.vp.currentItem
                    val mutableList = viewModel.mutableList.value
                    if (mutableList != null && currentItem in 0 until mutableList.size) {
                        DbHelper.updateMediaFileNameAndPath(mutableList[currentItem])
                    }
                    // 2. 通知上一层刷新
                    EventBus.getDefault().post(O95TranBeginAndRefreshEvent())
                    // 3. 真正退出
                    navigator.pop()
                }
            }
        )
    }

    // 新增校准开关点击更改图片显示
    private fun displayCalibratedImage() {
        val currentPosition = viewBinding.vp.currentItem
        val mediaList = viewModel.mutableList.value ?: return

        if (currentPosition < mediaList.size) {
            val currentBean = mediaList[currentPosition]

            changeBeanFileNameAndPath(currentBean)

            viewModel.mutableList.value = mediaList

            viewBinding.ivRevised.setImageResource(
                if (currentBean.fileName.contains("Unrevised")) {
                    R.drawable.ic_detail_unrevised
                } else {
                    R.drawable.ic_detail_revised
                }
            )
            revisedStatusViewModel.onImgRevisedChanged(true)
            adapter.notifyDataSetChanged()
        }
    }

    // 新增方法：根据当前bean生成需要替换的bean
    private fun changeBeanFileNameAndPath(mediaBean: MediaBean) {
        val currentImageFileName = mediaBean.fileName
        var targetFileName = ""
        var targetImageParentPath = ""
        if (currentImageFileName.startsWith(FileSpaceHelper.IMG_UNREVISED)) {
            targetFileName = currentImageFileName.replaceFirst(FileSpaceHelper.IMG_UNREVISED, FileSpaceHelper.IMG)
            targetImageParentPath = mediaBean.path.replaceFirst(FileSpaceHelper.IMG_UNREVISED, FileSpaceHelper.IMG)
        } else {
            targetFileName = currentImageFileName.replaceFirst(FileSpaceHelper.IMG, FileSpaceHelper.IMG_UNREVISED)
            targetImageParentPath = mediaBean.path.replaceFirst(FileSpaceHelper.IMG, FileSpaceHelper.IMG_UNREVISED)
        }

        mediaBean.path = targetImageParentPath
        mediaBean.fileName = targetFileName
    }

    private var setBottomBarJob: Job? = null
    private fun subscribeUI(bean: MediaBean) {
        // 通知底部栏是否展示播放 暂停按钮, 并调整间距
        viewModel.muteToggleState.observe(viewLifecycleOwner) { bean ->
            viewBinding.ivMuteToggle2.visibleOrgone(bean.state == 1)
        }
        viewModel.playLiveData.observe(viewLifecycleOwner) { bean ->

            Timber.e("viewModel.playLiveData ${bean.state}")
            // 防止快速的抖动
            setBottomBarJob?.cancel()
            setBottomBarJob = launch {
                // 不加延迟 有时候会无法响应， 很奇怪
                delay(dealyTime)
                Timber.e("bean.state ${bean.state}")
                when (bean.state) {
                    Stop -> {
                        viewBinding.ivPlayToggle.visibility = View.GONE
                        viewBinding.ivPlayToggle.isSelected = false
                    }

                    Pause -> {
                        viewBinding.ivPlayToggle.visibility = View.VISIBLE
                        viewBinding.ivPlayToggle.isSelected = false
                    }

                    Play -> {
                        viewBinding.ivPlayToggle.visibility = View.VISIBLE
                        viewBinding.ivPlayToggle.isSelected = true
                    }
                }
            }
        }

        // 更新外层fragment 播放还是暂停的状态
        viewModel.notifyPlayOrPauseLiveDate.observe(viewLifecycleOwner) {
            viewBinding.ivPlayToggle.isSelected = it
        }
        // 更新外层fragment 播放还是暂停的状态
        viewModel.muteToggleState.observe(viewLifecycleOwner) {
            viewBinding.ivMuteToggle2.isSelected = it.isOpen
        }
        viewModel.barFold.observe(viewLifecycleOwner) { unfold ->
            if (isLandScope()) {
                viewBinding.contnet.transitionToState(
                    if (unfold) {
                        R.id.media_detail_landscope_unfold
                    } else {
                        R.id.media_detail_landscope_fold
                    }
                )
            }
        }
    }

    private fun initListener(mimeType: String) {
        // 触发内层播放还是暂停的动作
        viewBinding.ivPlayToggle.clickDebounce(viewLifecycleOwner, intervalTime) {
            val isPlayingOrPause = viewModel.notifyPlayOrPauseLiveDate.value
            val currentMimeType = viewModel.curMediaBean.value?.mimeType
            val fragment =
                if (currentMimeType == null || currentMimeType.contains("image")) {
                    null
                } else {
                    adapter.getCurrentFragment() as? ContentVideoO95Fragment
                }
            if (isPlayingOrPause != null && isPlayingOrPause) {
                viewModel.notifyPlayOrPauseLiveDate.value = false
                when (fragment) {
                    is ContentVideoO95Fragment -> fragment.pauseVideo()
                }
            } else {
                Timber.e("currentPosition father")
                viewModel.notifyPlayOrPauseLiveDate.value = true
                when (fragment) {
                    is ContentVideoO95Fragment -> fragment.playVideo()
                }
            }
        }
        viewBinding.ivMuteToggle2.clickDebounce(viewLifecycleOwner, intervalTime) {
            val isOpen = viewModel.muteToggleState.value?.isOpen ?: true
            Timber.d("mute clicked isOpen:$isOpen")
            val currentMimeType = viewModel.curMediaBean.value?.mimeType
            val fragment = when (currentMimeType) {
                Mp4_Type, Video_Type, Video_Folder -> adapter.getCurrentFragment() as? ContentVideoO95Fragment
                else -> null
            }
            isSelected = !isSelected
            if (fragment == null) return@clickDebounce
            viewModel.muteToggleState.value = MuteToggleState(1, !isOpen)
            fragment.toSetMute(!isOpen)
        }

        viewBinding.ivLeft.clickDebounce(viewLifecycleOwner) {
            onBackPressed()
        }

        // viewpager内部的fragment 下载完成之后，操作栏可见
        viewModel.avaiableControllbar.observe(viewLifecycleOwner) { visible ->
            viewBinding.ivMask.visibleOrgone(visible)
        }
    }

    private fun initViewPager(position: Int, list: MutableList<MediaBean>) {
        adapter = MediaDetailO95PagerAdapter(childFragmentManager, list) { curBean ->
            viewModel.curMediaBean.value = curBean
            initListenerWithBean(curBean)
            subscribeUI(curBean)
        }
        // 为ViewPager添加滑动监听
        val pageChangeListener = object : ViewPager.SimpleOnPageChangeListener() {
            private var lastPosition = -1
            override fun onPageSelected(position: Int) {
                // 更新当前页面索引
                viewModel.currentPageIndex.value = position
                // 如果不是首次加载，则更新上一个页面索引
                if (lastPosition != -1) {
                    viewModel.previousPageIndex.value = lastPosition
                }
                lastPosition = position
            }
        }
        viewBinding.vp.addOnPageChangeListener(pageChangeListener)
        viewBinding.vp.offscreenPageLimit = 1
        viewBinding.vp.adapter = adapter
        if (position < list.size) {
            viewBinding.vp.currentItem = position
            // 手动触发 onPageSelected 以确保 currentPageIndex 被正确设置
            pageChangeListener.onPageSelected(position)
        }
    }
    private var setRevisedJob: Job? = null
    private fun initListenerWithBean(bean: MediaBean) {
        // 设置底部控制栏蒙版是否显示
        Timber.e("viewBinding.ivMask , ${bean.downloadState}")
        viewBinding.ivMask.visibleOrgone(bean.downloadState != MediaBean.Complete)
        // 防抖处理
        setRevisedJob?.cancel()
        setRevisedJob = launch {
            if (bean.hasRevised) {
                delay(stateInterval)
                viewBinding.ivRevised.visibleOrgone(true)
                viewBinding.ivRevised.setImageResource(
                    if (bean.fileName.contains("Unrevised")) {
                        R.drawable.ic_detail_unrevised
                    } else {
                        R.drawable.ic_detail_revised
                    }
                )
            } else {
                viewBinding.ivRevised.setImageDrawable(null)
                viewBinding.ivRevised.visibleOrgone(false)
            }
        }

        viewBinding.containerShare.clickDebounce(viewLifecycleOwner) {
            if (bean.downloadState == MediaBean.Complete) {
                val isVideo = bean.duration > 0
                var shareType = ""
                var sharePath = ""
                if (isVideo) {
                    sharePath = if (bean.processState == MediaBean.Process_Complete) {
                        bean.path
                    } else {
                        bean.vidoTempPath
                    }
                    shareType = IntentUtils.TYPE_VIDEO
                } else {
                    sharePath = bean.path
                    shareType = IntentUtils.TYPE_IMAGE
                }
                if (TextUtils.isEmpty(sharePath) || !File(sharePath).exists()) {
                    Timber.d("file is not exists! $sharePath")
                    toast(R.string.text_cannot_share_this_file_tip)
                    return@clickDebounce
                }
                sharePath?.let {
                    IntentUtils.shareFile(
                        requireContext(),
                        File(it),
                        shareType,
                        getString(R.string.shareto)
                    )
                }
            }
        }
        // 保存文件
        viewBinding.ivSave2album.clickDebounce(viewLifecycleOwner, intervalTime) {
            FileSpaceHelper.withPermissionCheck(this@MediaDetailO95Fragment) {
                saveMediaFiles2(mutableListOf(bean))
            }
        }
        // 删除文件
        viewBinding.ivDelbackup.clickDebounce(viewLifecycleOwner, intervalTime) {
            FileSpaceHelper.withPermissionCheck(this@MediaDetailO95Fragment) {
                isShowDelete.value = true
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Timber.d("onConfigurationChanged newConfig.orientation %s", newConfig.orientation)
        checkRotationConfiguration()
    }

    private fun checkRotationConfiguration() {
        if (isLandScope()) {
            Timber.e("transitionToState(R.id.media_detail_landscope_fold)")
            viewBinding.tvShare.visibleOrgone(false)
            viewBinding.tvSave2album.visibleOrgone(false)
            viewBinding.tvDelbackup.visibleOrgone(false)
            viewBinding.containerPlayToggle.visibleOrgone(true)
            setMarginStartAndEnd(viewBinding.containerShare, R.dimen.dp_20)
            setMarginStartAndEnd(viewBinding.containerSave2album, R.dimen.dp_20)
            setMarginStartAndEnd(viewBinding.containerDelbackup, R.dimen.dp_20)
            setMarginStartAndEnd(viewBinding.containerPlayToggle, R.dimen.dp_20)
            viewBinding.ifvSwitch.crossfade = 0f
            changeIvSwitchMargin(viewBinding.ifvSwitch, R.dimen.dp_40)
            viewBinding.clBottom.setBackgroundResource(R.drawable.rectangle_transparent)
            viewBinding.ivMask.setBackgroundResource(R.drawable.rectangle_40_black43)
            viewBinding.topBg.setBackgroundResource(R.color.black_60)
            viewBinding.contnet.transitionToState(R.id.media_detail_landscope_fold)
            viewBinding.contnet.postDelayed(
                {
                    if (isAdded) {
                        StatusBarUtil.hideStatusBar(this@MediaDetailO95Fragment)
                    }
                },
                stateInterval
            )
        } else {
            Timber.e("transitionToState(R.id.media_detail_portrait)")
            viewBinding.tvSave2album.visibleOrgone(true)
            viewBinding.tvDelbackup.visibleOrgone(true)
            viewBinding.tvShare.visibleOrgone(true)
            viewBinding.containerPlayToggle.visibleOrgone(false)
            evenlyDistributeChildren(viewBinding.flow)
            viewBinding.ifvSwitch.crossfade = 1f
            changeIvSwitchMargin(viewBinding.ifvSwitch, R.dimen.dp_28)
            // viewBinding.clBottom.setBackgroundResource(R.drawable.bg_rounnd_rectangle_40_blackf1)
            viewBinding.ivMask.setBackgroundResource(R.drawable.rectangle_40_black43)
            viewBinding.topBg.setBackgroundResource(R.color.pageBackground)
            viewBinding.contnet.transitionToState(R.id.media_detail_portrait)
            viewBinding.contnet.postDelayed(
                {
                    if (isAdded) {
                        StatusBarUtil.showStatusBar(this@MediaDetailO95Fragment)
                    }
                },
                stateInterval
            )
        }
    }

    private fun setMarginStartAndEnd(view: LinearLayout, id: Int) {
        val layoutParams = view.layoutParams as ViewGroup.MarginLayoutParams
        layoutParams.marginStart = resources.getDimensionPixelSize(id)
        layoutParams.marginEnd = resources.getDimensionPixelSize(id)
        view.layoutParams = layoutParams
    }

    private fun changeIvSwitchMargin(switchView: View, marginDp: Int) {
        val params = switchView.layoutParams as ViewGroup.MarginLayoutParams
        params.marginStart = resources.getDimensionPixelSize(marginDp)
        switchView.layoutParams = params
    }

    private fun evenlyDistributeChildren(linearLayout: LinearLayout) {
        val childCount = linearLayout.childCount
        for (i in 0 until childCount) {
            val child = linearLayout.getChildAt(i)
            val layoutParams = LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT)
            layoutParams.weight = 1f
            layoutParams.marginStart = 0
            layoutParams.marginEnd = 0
            child.layoutParams = layoutParams
        }
    }

    private fun dataPoint() {
//        val status = getTransStatus()
//        StatisticHelper
//            .addEventProperty(PropertyKeyCons.Property_TRANSMISSION_STATE, status) // 传输状态
//            .addEventProperty(PropertyKeyCons.Property_SELECTED_FILES_AMOUNT, 1) // 选中的素材数量
//            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, getPageName()) // 页面名称
//            .doEvent(EventCons.EventKey_SV1_SAVE_TO_ALBUM)
    }

    private fun delDataPoint() {
//        val status = getTransStatus()
//        StatisticHelper
//            .addEventProperty(PropertyKeyCons.Property_TRANSMISSION_STATE, status) // 传输状态
//            .addEventProperty(PropertyKeyCons.Property_SELECTED_FILES_AMOUNT, 1) // 选中的素材数量
//            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, getPageName()) // 页面名称
//            .doEvent(EventCons.EventKey_SV1_DELTE_FILES)
    }

    @SuppressLint("SourceLockedOrientationActivity")
    override fun onBackPressed(): Boolean {
        val currentItem = viewBinding.vp.currentItem
        val mutableList = viewModel.mutableList.value
        if (mutableList != null && currentItem in 0 until mutableList.size) {
            DbHelper.updateMediaFileNameAndPath(mutableList[currentItem])
        } else {
            Timber.d("current value is null")
        }
        requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
        val flag = navigator.pop()
        return flag
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: O95NetDisconnectEvent) {
        // 重置下载状态不是完成的为异常，以便显示撕裂图片
        viewModel.mutableList.value?.let {
            it.forEach {
                if (it.downloadState != MediaBean.Complete) {
                    it.downloadState = MediaBean.Error
                }
            }
        }
        Timber.e("O95NetDisconnectEvent downloadState = MediaBean.Error")
    }

    override fun getPageName(): String {
        return "ScreenCons.ScreenName_SV1_PLAYER"
    }

    /**
     * 保存文件列表到系统相册
     * @param mediaBeans 要保存的媒体文件列表
     */
    private fun saveMediaFiles2(mediaBeans: List<MediaBean>) = lifecycleScope.launch {
        FileSpaceHelper.saveMediaFiles(mediaBeans)
    }

    @Composable
    fun DeleteDialog() {
        BottomSheet3ButtonTitleDes(
            title = stringResource(
                R.string.title_file_Space_delete_files,
                1
            ),
            des = stringResource(R.string.tip_file_Space_delete_files),
            visible = isShowDelete.value,
            displayMode = DisplayMode.View(),
            buttonConfig = ButtonConfig.ThreeButton(
                ButtonParams(
                    textColor = ColorBlack,
                    text = stringResource(R.string.label_file_Space_delete_all),
                    enableColors = listOf(Color26EAD9, Color17CBFF),
                    disableColors = listOf(Color26EAD9_30, Color17CBFF_30)
                ) {
                    toDeleteMediaFile(true)
                },
                ButtonParams(text = stringResource(R.string.label_file_Space_delete_app)) {
                    toDeleteMediaFile(false)
                },
                ButtonParams(text = stringResource(id = R.string.cancel))
            ),
            onDismiss = { isShowDelete.value = false }
        )
    }

    private fun toDeleteMediaFile(isDeleteGallery: Boolean = false) = lifecycleScope.launch {
        val curMediaBean = viewModel.curMediaBean.value ?: return@launch
        FileSpaceHelper.deleteMediaFile(
            bean = curMediaBean,
            isDeleteGallery = isDeleteGallery,
            onSuccess = {
                launch(Dispatchers.Main) {
                    adapter.removeItem(curMediaBean)
                    DbHelper.delMediaO95(curMediaBean)
                    EventBus.getDefault().post(O95DelFileEvent(curMediaBean))
                    checkItemCount()
                    toast(R.string.tip_file_Space_delete_success)
                }
            },
            onFailed = {
                toast(R.string.tip_file_Space_delete_failed)
            }
        )
    }

    private fun checkItemCount() {
        if (adapter.count == 0) {
            onBackPressed()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 清空 ViewPager 页面索引值，防止内存泄漏和状态混乱
        viewModel.currentPageIndex.value = null
        viewModel.previousPageIndex.value = null
        Timber.i("清空页面索引值：currentPageIndex 和 previousPageIndex")
        Timber.i("页面退出，释放播放器资源")
        GSYVideoManager.instance().releaseMediaPlayer()
    }

    companion object {
        const val dealyTime = 25L
        const val intervalTime = 300L
        private const val stateInterval = 200L
    }
}
