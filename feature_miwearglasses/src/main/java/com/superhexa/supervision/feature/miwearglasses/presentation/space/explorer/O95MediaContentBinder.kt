package com.superhexa.supervision.feature.miwearglasses.presentation.space.explorer

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.navigation.NavController
import com.chad.library.adapter.base.binder.QuickItemBinder
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.github.fragivity.applySlideInOut
import com.github.fragivity.push
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95Fragment
import com.superhexa.supervision.filetrans.handler.MediaSpaceHandler
import com.superhexa.supervision.library.base.basecommon.commonbean.BinderWrapperBean
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.setVisibleState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
import com.superhexa.supervision.library.base.customviews.CircleProgressView
import com.superhexa.supervision.library.db.MediaType.Mp4_Type
import com.superhexa.supervision.library.db.MediaType.Video_Folder
import com.superhexa.supervision.library.db.MediaType.Video_Type
import com.superhexa.supervision.library.db.bean.MediaBean
import kotlinx.coroutines.ExperimentalCoroutinesApi
import timber.log.Timber

/**
 * 类描述:图片视频Item的Binder
 * 创建日期:2021/7/19 on 2:26 下午
 * 作者: FengPeng
 */
@Suppress("LongMethod", "ComplexMethod", "NestedBlockDepth")
class O95MediaContentBinder(
    val navigator: NavController,
    val onItemMultiSelectModeClick: (selected: Boolean, item: MediaBean) -> Unit,
    val onItemLongClick: () -> Unit
) : QuickItemBinder<MediaBean>() {
    private val videoTypeList by lazy { listOf(Video_Type, Video_Folder, Mp4_Type) }
    override fun getLayoutId() = R.layout.item_view_o95_media_content

    private var isMultiSelectMode: Boolean = false

    fun updateEditMode(value: Boolean) {
        isMultiSelectMode = value
    }

    @Suppress("MaxLineLength")
    override fun convert(holder: BaseViewHolder, data: MediaBean) {
        val checkPhoto = holder.getView<ImageView>(R.id.checkPhoto)
        val photoView = holder.getView<ImageView>(R.id.commonPhoto)
        val progressBar = holder.getView<CircleProgressView>(R.id.progressBar)
        val icError = holder.getView<View>(R.id.icError)
        val icCycle = holder.getView<View>(R.id.icCycle)
        val maskWaitDownload = holder.getView<View>(R.id.maskWaitDownload)
//        val videoProcess = holder.getView<TextView>(R.id.tvProcess)
        val maskSelected = holder.getView<View>(R.id.maskSelected)
        val tvDuration = holder.getView<TextView>(R.id.tvDuration)

        val isRevised = holder.getView<ImageView>(R.id.ivRevised)
        if (data.hasRevised) {
            isRevised.visibleOrgone(true)
            isRevised.setImageResource(
                if (data.fileName.contains("Unrevised")) {
                    R.drawable.ic_explorer_unrevised
                } else {
                    R.drawable.ic_explorer_revised
                }
            )
        } else {
            isRevised.visibleOrgone(false)
        }

        val isVideo = data.mimeType in videoTypeList
        val isProcessing = data.processState == MediaBean.Process_Processing
        val isProcessError = data.processState == MediaBean.Process_Error
        val showVideoProcess = isVideo && (isProcessing || isProcessError)
        val mainUrl = if (isVideo && isProcessing) {
            data.vidoTempPath ?: data.thumbnaiLocalPath ?: data.thumbnailUrl
        } else {
            data.path ?: data.thumbnaiLocalPath ?: data.thumbnailUrl
        }
        val fallbackUrl = data.thumbnaiLocalPath ?: data.thumbnailUrl ?: ""

        resetStatusViews(icError, icCycle, progressBar, maskWaitDownload)

        // 多选 UI
        checkPhoto.setVisibleState(isMultiSelectMode)
        checkPhoto.isSelected = isMultiSelectMode && data.isSelected
        maskSelected.setVisibleState(isMultiSelectMode && data.isSelected)

        when (data.downloadState) {
            MediaBean.Complete -> loadWithFallbackIfNeeded(context, isVideo, isProcessing, mainUrl, fallbackUrl, photoView)

            MediaBean.Error -> {
//                GlideUtils.loadUrl(context, R.drawable.ic_o95_not_found_icon, photoView)
                GlideUtils.loadUrl(context, fallbackUrl, photoView, isVideo)
                icError.setVisibleState(true)
            }

            MediaBean.Wait -> {
                if (!isMultiSelectMode) icCycle.setVisibleState(true)
                GlideUtils.loadUrl(context, fallbackUrl, photoView, isVideo)
                maskWaitDownload.setVisibleState(true)
            }

            else -> {
                GlideUtils.loadUrl(context, fallbackUrl, photoView, isVideo)
                maskWaitDownload.setVisibleState(true)
            }
        }

//        if (showVideoProcess) {
//            val (txtRes, colorRes) = if (isProcessing) {
//                R.string.libs_video_prcessering to R.color.white_40
//            } else {
//                R.string.libs_video_prcesser_failed to R.color.color_ff0050
//            }
//
//            videoProcess.apply {
//                setVisibleState(true)
//                text = context.getString(txtRes)
//                setTextColor(context.getColor(colorRes))
//            }
//        }

        if (!isMultiSelectMode) {
            progressBar.visibleOrgone(data.downloadState == MediaBean.Downloading)
            if (data.downloadState == MediaBean.Downloading) {
                progressBar.setProgress(data.downloadProgress)
            }
            icError.setVisibleState(data.downloadState == MediaBean.Error)
        }

        tvDuration.setVisibleState(isVideo)
        tvDuration.text = when {
            data.durationMs > 0 -> DateTimeUtils.videoDuration(data.durationMs)
            data.duration > 0 -> DateTimeUtils.videoDuration(data.duration * thousand)
            else -> "00:00"
        }
    }

    @Suppress("LongParameterList")
    private fun resetStatusViews(
        icError: View,
        icCycle: View,
//        videoProcess: TextView,
        progressBar: CircleProgressView,
        maskWaitDownload: View
    ) {
        icError.setVisibleState(false)
        icCycle.setVisibleState(false)
//        videoProcess.setVisibleState(false)
        progressBar.visibleOrgone(false)
        maskWaitDownload.setVisibleState(false)
    }

    @Suppress("LongParameterList")
    private fun loadWithFallbackIfNeeded(
        context: Context,
        isVideo: Boolean,
        isProcessing: Boolean,
        mainUrl: String?,
        fallbackUrl: String,
        imageView: ImageView
    ) {
        val finalUrl = if (isVideo && isProcessing) mainUrl else mainUrl ?: fallbackUrl
        GlideUtils.loadUrlFormat(
            context,
            finalUrl.orEmpty(),
            imageView,
            successAction = { _, _, _, _, _ -> false },
            failAction = { _, _, _, _ ->
                imageView.post {
                    GlideUtils.loadUrlFormat(context, fallbackUrl, imageView, isVideo)
                }
                true
            },
            isVideo = isVideo
        )
    }

    override fun onChildLongClick(holder: BaseViewHolder, view: View, data: MediaBean, position: Int): Boolean {
        if (isMultiSelectMode || MediaSpaceHandler.isDownloading()) {
            return true
        }
        updateEditMode(true)
        onItemLongClick.invoke()
        onChildClick(holder, view, data, position)
        return true
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun onChildClick(holder: BaseViewHolder, view: View, data: MediaBean, position: Int) {
        Timber.e("view.id ${view.id} $isMultiSelectMode")
        if (isMultiSelectMode) {
            val maskWaitDownload = holder.getView<View>(R.id.maskWaitDownload)

            when (view.id) {
                // 每个图片点击时
                R.id.root -> {
                    Timber.e("onClick R.id.root data.isSelected ${data.isSelected}")
                    // 已经下载完毕的可选,因为进度多选模式，可能有些item会出现下载完成的情况，，所以再加一层
                    if (data.allowSelected && maskWaitDownload.visibility != View.VISIBLE) {
                        data.isSelected = !data.isSelected
                        if (data.isSelected) {
                            onItemMultiSelectModeClick.invoke(true, data)
                        } else {
                            onItemMultiSelectModeClick.invoke(false, data)
                        }
                        // 选出所有已经选择的
                        val selectNum =
                            data.parentBean?.sublist?.filter { it.isSelected }?.toList()?.count()
                                ?: 0
                        // 通知组标题是否已经全选还是取消, 已被
                        data.parentBean?.selectAll =
                            (data.parentBean?.allowSelectedMaxNum == selectNum)
                        data.parentBean?.let { titleBean ->
                            if (adapter.data.contains(titleBean)) {
                                val groupItemPosition = adapter.data.indexOf(titleBean)
                                adapter.notifyItemChanged(groupItemPosition)
                            }
                        }
                        adapter.notifyItemChanged(position)
                    } else {
                        context.toast("未传输完成的文件无法选中")
                    }
                }
            }
        } else {
            // 非多选模式下
            when (view.id) {
                // 每个图片点击时
                R.id.root -> {
                    Timber.e("checkPhoto is visiable ${data.isSelected} ${data.downloadState} ")
                    if (data.downloadState != MediaBean.Complete) return
                    val list = adapter.data.filterIsInstance<MediaBean>().toMutableList()
                    val bundle = Bundle()
                    bundle.putInt(BundleKey.Position, list.indexOf(data))
                    bundle.putBinder(
                        BundleKey.MediaContenList,
                        BinderWrapperBean(list)
                    )
                    navigator.push(MediaDetailO95Fragment::class) {
                        arguments = bundle
                        applySlideInOut()
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        val viewHolder = super.onCreateViewHolder(parent, viewType)
        viewHolder.setIsRecyclable(false)
        return viewHolder
    }

    companion object {
        const val thousand = 1000
    }
}
