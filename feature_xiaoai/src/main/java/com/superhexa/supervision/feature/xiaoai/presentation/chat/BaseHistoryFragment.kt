package com.superhexa.supervision.feature.xiaoai.presentation.chat

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListItemInfo
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Popup
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.lifecycleScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.gyf.immersionbar.ktx.statusBarHeight
import com.superhexa.supervision.component.TextCommon
import com.superhexa.supervision.feature.xiaoai.BuildConfig
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.feature.xiaoai.presentation.component.FullScreenImagePreview
import com.superhexa.supervision.feature.xiaoai.presentation.component.ImageState
import com.superhexa.supervision.feature.xiaoai.presentation.component.ItemContentChat
import com.superhexa.supervision.feature.xiaoai.router.HexaRouter
import com.superhexa.supervision.feature.xiaoai.utils.AppHelper
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetSingleSelect
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.ColorChatPageBg
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_17
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_200
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_45
import com.superhexa.supervision.library.base.basecommon.theme.Dp_48
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_80
import com.superhexa.supervision.library.base.basecommon.theme.Sp_14
import com.superhexa.supervision.library.base.basecommon.theme.Sp_20
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.base.webviewhelper.PageType
import com.superhexa.supervision.library.base.webviewhelper.StreamType
import com.superhexa.supervision.library.base.webviewhelper.WebAppInterfaceListener
import com.superhexa.supervision.library.base.webviewhelper.WebPageHelper
import com.superhexa.supervision.library.base.webviewhelper.WebViewState
import com.superhexa.supervision.library.db.bean.ChatRecord
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.ai.core.AivsConfig
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.record.AudioRecorder
import com.xiaomi.aivs.utils.SpeechEngineHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import me.drakeet.support.toast.ToastCompat
import org.kodein.di.generic.instance
import timber.log.Timber
import kotlin.math.roundToInt

abstract class BaseHistoryFragment : BaseComposeFragment() {
    protected val viewModel by instance<BaseHistoryViewModel>()

    private val audioRecorder = AudioRecorder()
    private var titleClickCount = 0
    private val visibleDomainDialog = mutableStateOf(false)

    private var requestId = ""
    private val recorderTxt = mutableStateOf("开始录音")
    private var enterTime = 0L

    open fun pageTitle(): String = getString(R.string.libs_xiaoai_record)
    open fun supportPagination() = true

    override val contentView: @Composable () -> Unit = {
        val mState = viewModel.mState.collectAsState()
        val loading = mState.value.isLoading
        setCancelable(true)
        ConstraintLayout(
            modifier = Modifier
                .fillMaxSize()
                .background(color = ColorChatPageBg)
        ) {
            val (titleBar, action, list) = createRefs()

            CommonTitleBar(
                pageTitle(),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true,
                titleModifier = Modifier
                    .clickable {
                        titleClickCount++
                        if (titleClickCount == ENV_SWITCH_COUNT) {
                            visibleDomainDialog.value = true
                        }
                    }
            ) { navigator.pop() }
            if (BuildConfig.DEBUG) {
                Row(
                    modifier = Modifier
                        .height(Dp_50)
                        .fillMaxWidth()
                        .constrainAs(action) {
                            top.linkTo(titleBar.bottom, Dp_20)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                ) {
                    SubmitButton(
                        subTitle = recorderTxt.value,
                        modifier = Modifier.width(Dp_80),
                        enable = true
                    ) {
                        if (audioRecorder.isRecording()) {
                            audioRecorder.stopRecording(requireContext()) {
                                AiSpeechEngine.INSTANCE.finishSession(
                                    requestId = requestId,
                                    reason = "button"
                                )
                            }
                            recorderTxt.value = "开始录音"
                        } else {
                            audioRecorder.startRecording(requireActivity()) { pcmData ->
                                AiSpeechEngine.INSTANCE.postSpeechData(
                                    pcmData,
                                    0,
                                    pcmData.size,
                                    false
                                )
                            }
                            requestId = AiSpeechEngine.INSTANCE.postSpeechBegin()
                            recorderTxt.value = "停止录音"
                        }
                    }
                }
            }
            NetworkAwareComponent(
                content = {
                    WebViewScreen(
                        modifier = Modifier.constrainAs(list) {
                            top.linkTo(
                                if (BuildConfig.DEBUG) action.bottom else titleBar.bottom,
                                Dp_12
                            )
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            bottom.linkTo(parent.bottom)
                            height = Dimension.fillToConstraints
                        }
                    )
                },
                offlineContent = {
                    OfflineScreen()
                }
            )
        }
        EnvSettingDialog()
        Timber.d("loading:$loading")
        if (loading) {
            showLoading()
        } else {
            hideLoading()
        }
        LaunchedEffect(viewModel.mEffect) {
            viewModel.mEffect.collect { effect ->
                handelEffect(effect)
            }
        }
    }

    @Composable
    fun NetworkAwareComponent(
        content: @Composable () -> Unit,
        offlineContent: @Composable () -> Unit = { OfflineScreen() }
    ) {
        val connectionState by connectivityState()
        val isOnline = connectionState === ConnectionState.Available

        if (isOnline) {
            content()
        } else {
            viewModel.hideLoading()
            offlineContent()
        }
    }

    enum class ConnectionState {
        Available, Unavailable
    }

    @Composable
    fun connectivityState(): State<ConnectionState> {
        return produceState(
            initialValue = if (NetworkMonitor.isNetworkValidated()) {
                ConnectionState.Available
            } else {
                ConnectionState.Unavailable
            }
        ) {
            val networkListener: (Boolean, Boolean) -> Unit = { isConnected, isValid ->
                Timber.d("onNetworkState:$isConnected,$isValid")
                if (!isValid) {
                    value = ConnectionState.Unavailable
                } else {
                    // 网络恢复时的处理
                    value = ConnectionState.Available
                }
            }

            NetworkMonitor.addNetworkStateListener(
                <EMAIL>,
                networkListener
            )

            awaitDispose {
                NetworkMonitor.removeNetworkStateListener(networkListener)
            }
        }
    }

    @Suppress("MagicNumber")
    @Composable
    fun OfflineScreen() {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(R.drawable.icon_network_error),
                contentDescription = "network error",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier.size(size = Dp_45)
            )
            Spacer(modifier = Modifier.height(Dp_8))
            Text(
                text = stringResource(R.string.libs_xiaoai_record_error_tips),
                style = TextStyle(
                    color = ColorWhite60,
                    textAlign = TextAlign.Center,
                    fontFamily = FontFamily.SansSerif,
                    fontSize = Sp_14,
                    fontStyle = FontStyle.Normal,
                    fontWeight = FontWeight(330),
                    lineHeight = 22.4.sp
                ),
                modifier = Modifier.fillMaxWidth()
            )
        }
    }

    @SuppressLint("SetJavaScriptEnabled", "ClickableViewAccessibility")
    @Suppress("LongMethod")
    @Composable
    fun WebViewScreen(modifier: Modifier) {
        // 使用AndroidView来嵌入WebView
        val currentImageState by viewModel.imageState // 自动观察状态变化
        var showFullScreenImage by remember { mutableStateOf(false) }
        var mergedInsJson = ""
        val context = LocalContext.current
        // 状态管理
        val webViewState = remember { viewModel.webViewState }
        val (webView, isInitialized) = webViewState.value ?: run {
            val newWebView = WebView(context).apply {
                setBackgroundColor(0x00000000)
                settings.javaScriptEnabled = true
                settings.domStorageEnabled = true
                settings.allowFileAccess = true
                settings.allowContentAccess = true
                settings.allowFileAccessFromFileURLs = true
                settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                isScrollContainer = true // 关键触摸事件配置
                requestFocusFromTouch() // 允许接收触摸事件
                webChromeClient = WebChromeClient()
                isFocusable = true // 允许获取焦点
                isFocusable = true // 允许获取焦点
                setFocusableInTouchMode(true) // 允许在触摸模式下获取焦点
                setOnTouchListener { v, event ->
                    v.parent.requestDisallowInterceptTouchEvent(true)
                    false // 返回false表示不消费事件，继续传递
                }
            }
            WebViewState(newWebView, false).also {
                webViewState.value = it
            }
        }

        val webPageHelper = remember(webView) {
            WebPageHelper(context, webView).apply {
                if (!isInitialized) {
                    initWebView(
                        false,
                        object : WebAppInterfaceListener() {
                            override fun getAllRecords(): String {
                                // 处理加载聊天记录
                                Timber.d("getAllRecords $mergedInsJson")
                                viewModel.hideLoading()
                                return mergedInsJson
                            }

                            override fun removeRecord(dialogId: String) {
                                super.removeRecord(dialogId)
                                Timber.d("removeRecord:dialogId $dialogId")
                                viewModel.removeRecord(dialogId)
                                viewModel.queryAndRemoveRecord(dialogId)
                            }

                            override fun navigateToMemoryDetail(pageType: String, url: String) {
                                super.navigateToMemoryDetail(pageType, url)
                                Timber.d("pageType = $pageType, url = $url")
                                if (PageType.MEMORY_PAGE.value == pageType &&
                                    NetworkMonitor.isNetworkValidated()
                                ) {
                                    lifecycleScope.launch(Dispatchers.Main) {
                                        HexaRouter.navigateToMemoryDetail(
                                            fragment = this@BaseHistoryFragment,
                                            url = url
                                        )
                                    }
                                }
                            }

                            override fun navigateToPage(
                                pageType: String,
                                transactionId: String,
                                title: String
                            ) {
                                super.navigateToPage(pageType, transactionId, title)
                                if (PageType.RECORD_PAGE.value == pageType &&
                                    NetworkMonitor.isNetworkValidated()
                                ) {
                                    if (TextUtils.equals(title, "支付宝看一下支付") &&
                                        AiSpeechEngine.INSTANCE.getAlipayStatus()
                                    ) {
                                        requireContext().toast("支付过程中暂不支持查看")
                                        return
                                    }
                                    lifecycleScope.launch(Dispatchers.Main) {
                                        HexaRouter.navigateToStreamHistory(
                                            fragment = this@BaseHistoryFragment,
                                            sessionId = transactionId,
                                            title = title
                                        )
                                    }
                                }
                            }

                            override fun navigateToImagePage(
                                pageType: String,
                                dialogId: String,
                                imageId: String
                            ) {
                                super.navigateToImagePage(pageType, dialogId, imageId)
                                viewModel.fetchLargePicture(imageId, dialogId)
                                showFullScreenImage = true
                            }
                        },
                        onPageFinished = {
                            recordChanged(
                                StreamType.RECORD_STREAM,
                                mergedInsJson,
                                System.currentTimeMillis().toString()
                            )
                        }
                    )
                    webViewState.value = webViewState.value?.copy(isInitialized = true)
                }
            }
        }
        LaunchedEffect(
            key1 = viewModel.records.size,
            key2 = viewModel.records.lastOrNull()?.responseContent?.value,
            key3 = viewModel.records.lastOrNull()?.instructionListResponse?.value
        ) {
            val mergedList = (viewModel.history + viewModel.records).flatMap {
                it.instructionList ?: emptyList()
            }
            Timber.i("LaunchedEffect $mergedList")
            mergedInsJson = mergedList.toMutableList().toString()
            webPageHelper?.recordChanged(
                StreamType.RECORD_STREAM,
                mergedInsJson,
                System.currentTimeMillis().toString()
            )
        }

        // 初始化 WebView
        Box(modifier = modifier) {
            AndroidView(
                factory = { webView },
                modifier = modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
            )
        }

        // 图片全屏预览
        if (showFullScreenImage) {
            FullScreenImagePreview(
                imageState = currentImageState,
                onDismiss = {
                    showFullScreenImage = false
                    viewModel.resetImageState()
                },
                onSaveToGallery = {
                    if (currentImageState is ImageState.Success) {
                        viewModel.saveToGallery(
                            this,
                            (currentImageState as ImageState.Success).imageFile
                        )
                    }
                }
            )
        }
    }

    @Suppress("CoroutineCreationDuringComposition", "LongMethod", "ComplexMethod")
    @Composable
    protected fun ListScreen(
        modifier: Modifier,
        state: State<ChatHistoryState>
    ) {
        var firstLoad by remember { mutableStateOf(true) }
        val listState = rememberLazyListState()
        val coroutineScope = rememberCoroutineScope()
        val showPopup = remember { mutableStateOf(false) }
        var listHeightPx by remember { mutableStateOf(0) }
        var isQueryItem by remember { mutableStateOf(true) }
        var itemOffsetY by remember { mutableStateOf(0) }
        var itemHeight by remember { mutableStateOf(0) }
        val clickItem: MutableState<ChatRecord?> = remember { mutableStateOf(null) }
        val clickItemInfo: MutableState<LazyListItemInfo?> = remember { mutableStateOf(null) }
        var isSupportCopy by remember { mutableStateOf(true) }

        LaunchedEffect(listState) {
            snapshotFlow { listState.firstVisibleItemIndex }
                .map { index -> index == 0 }
                .distinctUntilChanged()
                .filter { listHeightPx > 0 }
                .collect {
                    if (firstLoad) {
                        firstLoad = false
                    } else {
                        if (supportPagination()) {
                            sendEvent(ChatHistoryEvent.LoadChatRecord)
                        }
                    }
                }
        }

        LaunchedEffect(viewModel.history) {
            if (viewModel.history.isNotEmpty()) {
                if (state.value.pageNo == DEFAULT_PAGE_NO) {
                    coroutineScope.launch {
                        listState.animateScrollToItem(viewModel.history.size)
                    }
                }
            }
        }

        LaunchedEffect(
            key1 = viewModel.records.size,
            key2 = viewModel.records.lastOrNull()?.responseContent?.value,
            key3 = viewModel.records.lastOrNull()?.imageResponse?.value
        ) {
            val lastIndex =
                viewModel.history.lastIndex + viewModel.records.lastIndex + 1
            if (lastIndex > 0) {
                listState.animateScrollToItem(lastIndex)
            }
        }

        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .onGloballyPositioned {
                    listHeightPx = it.size.height
                },
            state = listState,
            verticalArrangement = Arrangement.spacedBy(Dp_20),
            contentPadding = PaddingValues(horizontal = Dp_28, vertical = Dp_20)
        ) {
            itemsIndexed(
                items = viewModel.history,
                key = { _, item -> item.objId }
            ) { index, item ->
                val isSatisfiedMaxTimeInterval = isSatisfiedMaxTimeInterval(
                    preItem = viewModel.history.getOrNull(index - 1),
                    current = item
                )
                ItemContentChat(
                    context = requireContext(),
                    record = item,
                    showTime = isSatisfiedMaxTimeInterval,
                    onLongPress = { isQuery, offsetY, height, copySupport ->
                        clickItem.value = item
                        clickItemInfo.value = listState.layoutInfo.visibleItemsInfo
                            .firstOrNull { it.index == index }
                        isQueryItem = isQuery
                        itemOffsetY = offsetY
                        itemHeight = height
                        isSupportCopy = copySupport
                        showPopup.value = true
                    },
                    onCardClick = {
                        item.sessionId?.takeIf { it.isNotEmpty() }?.let {
                            HexaRouter.navigateToStreamHistory(
                                fragment = this@BaseHistoryFragment,
                                sessionId = it,
                                title = item.title
                            )
                        }
                    }
                )
            }
            itemsIndexed(viewModel.records) { index, item ->
                val isSatisfiedMaxTimeInterval = isSatisfiedMaxTimeInterval(
                    preItem = viewModel.records.getOrNull(index - 1)
                        ?: viewModel.history.firstOrNull(),
                    current = item
                )
                ItemContentChat(
                    context = requireContext(),
                    record = item,
                    showTime = isSatisfiedMaxTimeInterval,
                    onLongPress = { isQuery, offsetY, height, copySupport ->
                        clickItem.value = item
                        clickItemInfo.value = listState.layoutInfo.visibleItemsInfo
                            .firstOrNull { it.index - viewModel.history.size == index }
                        isQueryItem = isQuery
                        itemOffsetY = offsetY
                        itemHeight = height
                        isSupportCopy = copySupport
                        showPopup.value = true
                    },
                    onCardClick = {
                        item.sessionId?.takeIf { it.isNotEmpty() }?.let {
                            HexaRouter.navigateToStreamHistory(
                                fragment = this@BaseHistoryFragment,
                                sessionId = it,
                                title = item.title
                            )
                        }
                    }
                )
            }
            item { Spacer(modifier = Modifier.height(Dp_200)) }
        }

        ChatItemMenu(
            visible = showPopup,
            listHeight = listHeightPx,
            isQueryItem = isQueryItem,
            itemOffsetY = itemOffsetY,
            itemHeight = itemHeight,
            verticalPadding = Dp_10,
            clickedItem = clickItem.value,
            clickedItemInfo = clickItemInfo.value,
            isSupportCopy = isSupportCopy
        )
    }

    @Suppress("LongParameterList", "LongMethod")
    @Composable
    private fun ChatItemMenu(
        visible: MutableState<Boolean>,
        listHeight: Int,
        isQueryItem: Boolean,
        itemOffsetY: Int,
        itemHeight: Int,
        verticalPadding: Dp,
        clickedItem: ChatRecord?,
        clickedItemInfo: LazyListItemInfo?,
        isSupportCopy: Boolean = true
    ) {
        if (!visible.value) return
        val verticalPx = with(LocalDensity.current) { verticalPadding.toPx().roundToInt() }
        val menuHeight = with(LocalDensity.current) { Dp_48.toPx().roundToInt() }
        val offsetY = with(LocalDensity.current) { Dp_45.toPx().roundToInt() }
        clickedItemInfo?.let {
            val spaceBelow = listHeight - (it.size + itemOffsetY)
            val popupOffset = when {
                itemOffsetY > (menuHeight + verticalPx + offsetY) -> IntOffset(
                    0,
                    itemOffsetY - menuHeight - offsetY - verticalPx
                )

                spaceBelow > (menuHeight + verticalPx) -> IntOffset(
                    0,
                    itemOffsetY + itemHeight + verticalPx - statusBarHeight
                )

                else -> IntOffset(0, listHeight / 2)
            }

            Popup(
                alignment = Alignment.TopStart,
                offset = popupOffset,
                onDismissRequest = { visible.value = false }
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Dp_28),
                    horizontalAlignment = if (isQueryItem) Alignment.End else Alignment.Start
                ) {
                    Row(
                        modifier = Modifier
                            .height(Dp_48)
                            .background(
                                color = Color222425,
                                shape = RoundedCornerShape(Dp_12)
                            ),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (isSupportCopy) {
                            TextCommon(
                                modifier = Modifier
                                    .padding(horizontal = Dp_18)
                                    .clickable {
                                        visible.value = false
                                        val text = if (isQueryItem) {
                                            clickedItem?.query
                                        } else {
                                            clickedItem?.responseContent?.value
                                        }
                                        copyText(text)
                                    },
                                titleStr = "复制",
                                style = TextStyle(
                                    color = ColorWhite,
                                    fontSize = Sp_14,
                                    fontWeight = FontWeight.W400,
                                    lineHeight = Sp_20
                                )
                            )
                            Image(
                                modifier = Modifier
                                    .width(Dp_1)
                                    .height(Dp_17),
                                painter = painterResource(id = R.drawable.ic_chat_item_line),
                                contentDescription = ""
                            )
                        }
                        TextCommon(
                            modifier = Modifier
                                .padding(horizontal = Dp_18)
                                .clickable {
                                    visible.value = false
                                    sendEvent(ChatHistoryEvent.Remove(clickedItem))
                                },
                            titleStr = "删除",
                            style = TextStyle(
                                color = ColorWhite,
                                fontSize = Sp_14,
                                fontWeight = FontWeight.W400,
                                lineHeight = Sp_20
                            )
                        )
                    }
                }
            }
        }
    }

    private fun copyText(string: String?) {
        string?.let {
            InputUtil.copy2ClipBoard(requireContext(), string)
            toastImpl(getString(R.string.tips_copy_success))
        }
    }

    fun sendEvent(event: ChatHistoryEvent) {
        viewModel.sendEvent(event)
    }

    private fun handelEffect(effect: ChatHistoryEffect) {
        when (effect) {
            is ChatHistoryEffect.ShowTips -> toastImpl(effect.msg)
            is ChatHistoryEffect.Toast -> toastImpl(getString(effect.stringResId))
        }
    }

    private fun isSatisfiedMaxTimeInterval(
        preItem: ChatRecord?,
        current: ChatRecord
    ): Boolean {
        val preTime = preItem?.timestamp ?: 0
        return current.timestamp - preTime >= MAX_TIME_INTERVAL
    }

    @Composable
    private fun EnvSettingDialog() {
        // 新增ptr环境后更新env获取方法
        val env = ConfigCache.rawEnvDomain()
        val domainItems = listOf(
            SelectItem.EnvSelectItem(
                "ENV_PRODUCTION",
                AivsConfig.ENV_PRODUCTION == env,
                AivsConfig.ENV_PRODUCTION
            ),
            SelectItem.EnvSelectItem(
                "ENV_PREVIEW",
                AivsConfig.ENV_PREVIEW == env,
                AivsConfig.ENV_PREVIEW
            ),
            SelectItem.EnvSelectItem(
                "ENV_PREVIEW4TEST",
                AivsConfig.ENV_PREVIEW4TEST == env,
                AivsConfig.ENV_PREVIEW4TEST
            ),
            SelectItem.EnvSelectItem(
                "ENV_PREVIEW_TO_RELEASE",
                SpeechEngineHelper.ENV_PREVIEW_TO_RELEASE == env,
                SpeechEngineHelper.ENV_PREVIEW_TO_RELEASE
            )
        )
        BottomSheetSingleSelect(
            titleRes = R.string.title_env_switch,
            cancelRes = R.string.cancel,
            items = domainItems,
            visible = visibleDomainDialog.value,
            onItemSelected = { it, _ ->
                val item = it as? SelectItem.EnvSelectItem ?: return@BottomSheetSingleSelect
                ConfigCache.setEnvDomain(item.env)
                AppHelper.reStartApp(requireContext())
            },
            onDismiss = {
                visibleDomainDialog.value = false
            },
            onCancel = {
                visibleDomainDialog.value = false
            }
        )
    }

    private fun toastImpl(message: String) {
        ToastCompat.makeText(
            requireContext(),
            message,
            Toast.LENGTH_SHORT
        ).show()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        enterTime = System.currentTimeMillis()
    }

    override fun onStop() {
        super.onStop()
        O95Statistic.cpaEvent(
            "XiaoAi_Chat_Record",
            tip = "1676.0.0.0.43015",
            hasDurationSec = true,
            durationSec = enterTime
        )
    }

    companion object {
        // 相邻消息的最大时间间隔.
        private const val MAX_TIME_INTERVAL = 3 * 60 * 1000L

        private const val ENV_SWITCH_COUNT = 10
    }
}
