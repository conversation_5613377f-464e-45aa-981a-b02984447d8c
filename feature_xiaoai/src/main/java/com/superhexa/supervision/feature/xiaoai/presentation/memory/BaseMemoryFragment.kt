package com.superhexa.supervision.feature.xiaoai.presentation.memory

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.feature.xiaoai.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor
import com.superhexa.supervision.library.base.basecommon.theme.ColorChatPageBg
import com.superhexa.supervision.library.base.webviewhelper.PageType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

@Route(path = RouterKey.Memory_MemoryInterface)
class BaseMemoryFragment : ShowLoadingFragment() {

    // 网页是否加载完成的标志
    private var isWebViewLoaded = mutableStateOf(false)

    // 网络是否可用的标志
    private var isNetworkAvailable = mutableStateOf(false)

    // 保存WebView对象
    private var webView: WebView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 定义网络状态监听器
        val networkListener: (Boolean, Boolean) -> Unit = { isConnected, isValid ->
            isNetworkAvailable.value = isConnected && isValid
        }

        // 监听生命周期
        viewLifecycleOwner.lifecycle.addObserver(
            LifecycleEventObserver { source, event ->
                when (event) {
                    Lifecycle.Event.ON_START -> {
                        NetworkMonitor.addNetworkStateListener(
                            viewLifecycleOwner.lifecycle,
                            networkListener
                        )
                    }

                    Lifecycle.Event.ON_STOP -> {
                        NetworkMonitor.removeNetworkStateListener(networkListener)
                    }

                    else -> {
                        // do nothing
                    }
                }
            }
        )
        return ComposeView(requireContext()).apply {
            setContent {
                ContentView {
                    findNavController().popBackStack()
                }
            }
        }
    }

    @Suppress("MagicNumber")
    @Composable
    fun ContentView(onCloseClick: () -> Unit) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxSize()
                .background(color = ColorChatPageBg)
        ) {
            val (titleBar, content) = createRefs()

            // 标题栏显示
            CommonTitleBar(
                getString(R.string.libs_xiaoai_memory),
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                true
            ) {
                onCloseClick.invoke()
            }

            NetworkAwareComponent(
                content = {
                    WebViewScreen(
                        modifier = Modifier.constrainAs(content) {
                            top.linkTo(titleBar.bottom, 12.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            bottom.linkTo(parent.bottom)
                            height = Dimension.fillToConstraints
                        }
                    )
                },
                offlineContent = {
                    NoNetWorkView()
                }
            )
        }
        if (isWebViewLoaded.value) {
            hideLoading()
        } else {
            showLoading()
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    @Composable
    fun WebViewScreen(modifier: Modifier) {
        val context = LocalContext.current

        webView = remember {
            WebView(context).apply {
                settings.javaScriptEnabled = true
                settings.domStorageEnabled = true
                settings.allowFileAccess = true
                settings.allowContentAccess = true
                settings.allowFileAccessFromFileURLs = true
                settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                isScrollContainer = true // 关键触摸事件配置
                requestFocusFromTouch() // 允许接收触摸事件
                webChromeClient = WebChromeClient()
                isFocusable = true // 允许获取焦点
                isFocusable = true // 允许获取焦点
                setFocusableInTouchMode(true) // 允许在触摸模式下获取焦点

                webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        Timber.d("onPageFinished")
                        isWebViewLoaded.value = true
                        hideLoading()
                    }
                }
            }
        }
        webView?.let {
            val memoryWebPageHelper = remember(webView) {
                MemoryWebPageHelper(webView!!, context, null).apply {
                    initWebView(
                        object : MemoryWebInterfaceListener() {
                            override fun nativeToPage(url: String, pageType: String) {
                                if (NetworkMonitor.isNetworkValidated()) {
                                    when (pageType) {
                                        PageType.MEMORY_PAGE.value ->
                                            lifecycleScope.launch(Dispatchers.Main) {
                                                HexaRouter.navigateToMemoryDetail(
                                                    this@BaseMemoryFragment,
                                                    url
                                                )
                                            }

                                        PageType.MEMORY_IMAGE_PAGE.value ->
                                            lifecycleScope.launch(Dispatchers.Main) {
                                                HexaRouter.navigateToMemoryImage(
                                                    this@BaseMemoryFragment,
                                                    url
                                                )
                                            }
                                    }
                                }
                            }

                            override fun onContentLoadFinish() {
                                super.onContentLoadFinish()
                                isWebViewLoaded.value = true
                            }
                        }
                    )
                }
            }

            Box(modifier = modifier) {
                if (isWebViewLoaded.value) {
                    AndroidView(
                        factory = { webView!! },
                        modifier = modifier
                            .fillMaxWidth()
                            .fillMaxHeight()
                    )
                }
            }
        }
    }

    @Composable
    @Suppress("MagicNumber")
    fun NoNetWorkView() {
        Column(
            modifier = Modifier
                .fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(R.drawable.ic_o95_no_internet),
                contentDescription = null,
                modifier = Modifier.size(88.dp)
            )
            Spacer(Modifier.height(4.dp)) // 图片和文字间距
            Text(
                text = stringResource(R.string.no_internet_try_again),
                fontSize = 13.sp,
                color = colorResource(R.color.white_50)
            )
        }
    }

    @Composable
    fun NetworkAwareComponent(
        content: @Composable () -> Unit,
        offlineContent: @Composable () -> Unit = { NoNetWorkView() }
    ) {
        if (isNetworkAvailable.value) {
            content()
        } else {
            hideLoading()
            offlineContent()
        }
    }
}
