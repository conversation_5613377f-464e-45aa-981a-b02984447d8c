package com.superhexa.supervision.feature.xiaoai.presentation.memory

import android.Manifest
import android.content.ContentValues
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.net.URL

/*
* 用来展示记忆停车位图片界面：展示图片由web端提供一个链接，客户端需要实现可以将此图片保存到本地
*/
class MemoryImageDetailFragment : Fragment() {
    private lateinit var imageUrl: String

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_memory_img_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val imageView = view.findViewById<ImageView>(R.id.imageView)
        val closeButton = view.findViewById<ImageView>(R.id.close_button)
        val downloadButton = view.findViewById<ImageView>(R.id.download_button)

        // 获取传入的图片 URL
        val imageUrl = arguments?.getString(BundleKey.SHOW_URL)
        Timber.d("imageUrl = $imageUrl")

        // 使用Glide加载在线网络图片
        Glide.with(this).load(imageUrl).into(imageView)

        // 设置关闭按钮的点击事件
        closeButton.setOnClickListener {
            findNavController().popBackStack()
        }

        // 设置右下角按钮的点击事件
        downloadButton.setOnClickListener {
            // 处理右下角按钮的点击事件
            // 检查是否有存入文件的权限
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
                != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    requireActivity(),
                    arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    1
                )
            } else {
                if (imageUrl != null) {
                    saveInternetImage(imageUrl)
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        if (requestCode == 1) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                saveInternetImage(imageUrl)
            } else {
                Toast.makeText(context, "没有保存权限，无法保存图片", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun saveInternetImage(imageUrl: String) {
        Glide.with(this)
            .asBitmap()
            .load(imageUrl)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    saveBitmapToGallery(resource, imageUrl)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    Timber.d("权限被拒绝,图片保存失败")
                    Toast.makeText(requireContext(), "权限被拒绝，无法保存图片", Toast.LENGTH_SHORT)
                        .show()
                }
            })
    }

    @Suppress("TooGenericExceptionCaught")
    private fun saveBitmapToGallery(bitmap: Bitmap, imageUrl: String) {
        val fileName = BitmapUtils.getFileNameFromUrl(imageUrl)
        Timber.d("saveInternetImage, fileName = $fileName")
        val packName = context?.packageName

        val contentValues = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
            put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
            put(
                MediaStore.Images.Media.RELATIVE_PATH,
                Environment.DIRECTORY_PICTURES + "/" + packName
            )
        }

        val uri = context?.contentResolver?.insert(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            contentValues
        )

        if (uri != null) {
            try {
                context?.contentResolver?.openOutputStream(uri)?.use { outputStream ->
                    outputStream.write(BitmapUtils.getBytes(bitmap))
                }
                Toast.makeText(requireContext(), "已保存到系统相册", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Timber.d("saveBitmapToGallery error ${e.printStackTrace()}")
                e.printStackTrace()
                Toast.makeText(requireContext(), "图片保存失败", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(requireContext(), "图片保存失败", Toast.LENGTH_SHORT).show()
        }
    }
}

@Suppress("MagicNumber")
object BitmapUtils {
    fun getBytes(bitmap: Bitmap): ByteArray {
        val stream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream)
        return stream.toByteArray()
    }

    fun getFileNameFromUrl(url: String): String {
        val uri = URL(url).toURI()
        val path = uri.path
        return path.substring(path.lastIndexOf('/') + 1)
    }
}
