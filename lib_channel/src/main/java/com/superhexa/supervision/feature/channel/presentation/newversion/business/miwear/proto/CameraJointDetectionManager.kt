package com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto

import com.superhexa.lib.channel.R
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.toast

/**
 * 类描述:
 * 创建日期: 2024/12/2 on 14:19
 * 作者: qintaiyuan
 */
object CameraJointDetectionManager {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator
        get() = run {
            if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
                DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice)
            } else {
                null
            }
        }

    fun checkIsJointState(call: () -> Unit) {
        if (decorator?.isChannelSuccess() == true && decorator?.liveData?.value?.cameraJoint == true) {
            LibBaseApplication.instance.toast(R.string.libs_camera_joint)
        } else {
            call.invoke()
        }
    }

    fun checkIsChannelSuccess(call: () -> Unit) {
        if (decorator?.isChannelSuccess() == true) {
            call.invoke()
        } else {
            LibBaseApplication.instance.toast(R.string.libs_guide_device_not_connected)
        }
    }
}
