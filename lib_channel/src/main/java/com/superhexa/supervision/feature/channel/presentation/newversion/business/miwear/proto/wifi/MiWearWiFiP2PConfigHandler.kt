package com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi

import android.annotation.SuppressLint
import android.content.Context
import android.content.Context.WIFI_P2P_SERVICE
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.wifi.WpsInfo
import android.net.wifi.p2p.WifiP2pConfig
import android.net.wifi.p2p.WifiP2pManager
import android.os.Looper.getMainLooper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.EnableWiFiP2PGC
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import com.xiaomi.fitness.device.contact.export.IDeviceSyncCallback
import com.xiaomi.fitness.device.contact.export.SyncResult
import com.xiaomi.wear.protobuf.nano.SystemProtos
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import java.util.UUID
import kotlin.coroutines.resume

/**
 * 类描述:
 * 创建日期: 2025/2/10 on 09:54
 * 作者: qintaiyuan
 */
object MiWearWiFiP2PConfigHandler {
    const val WIFI_P2P = "wifi_p2p"
    const val TAG = "Wi-Fi P2P"
    private const val Delay_Time = 2_000L
    private const val Start_Go_Delay_Time = 10_000L
    private val wifiP2pManager: WifiP2pManager by lazy {
        LibBaseApplication.instance.getSystemService(WIFI_P2P_SERVICE) as WifiP2pManager
    }
    private val wifiP2pChannel: WifiP2pManager.Channel by lazy {
        wifiP2pManager.initialize(LibBaseApplication.instance, getMainLooper(), null)
    }

    private const val CHANNEL_36_FREQUENCY: Int = 5745 // Channel 36 Frequency in MHz
    private var netWorkName = "DIRECT-test1234"
    private var password = "test4321"
    private var frequency = CHANNEL_36_FREQUENCY

    @Suppress("MagicNumber")
    private fun generateRandomNetworkName(): String {
        // 使用UUID生成唯一的网络名称
        return "DIRECT-${UUID.randomUUID().toString().take(8)}"
    }

    @Suppress("MagicNumber")
    private fun getRandomFrequency(): Int {
        val mFrequencyList = listOf(5745, 5765, 5785, 5805)
        return mFrequencyList.random()
    }

    @Suppress("MagicNumber")
    private fun generateRandomPassword(): String {
        val characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_-"
        val passwordLength = 8 // 密码长度
        return (1..passwordLength)
            .map { characters.random() }
            .joinToString("")
    }

    suspend fun startAutoGO(): Boolean {
        MMKVUtils.removeKey(WIFI_P2P)
        return suspendCancellableCoroutine { continuation ->
            wifiP2pManager.requestGroupInfo(wifiP2pChannel) { group ->
                val job = CoroutineScope(Dispatchers.IO).launch {
                    if (group == null) {
                        Timber.tag(TAG).d("P2P group not exists")
                        val result = startGo()
                        if (continuation.isActive) continuation.resume(result)
                    } else {
                        Timber.tag(TAG).d("P2P group already exists")
                        val removed = removeGroupIfNeed()
                        Timber.tag(TAG).d("startAutoGO, removed=$removed")
                        if (removed) {
                            delay(Delay_Time) // 等待一段时间，确保组已清除
                        }
                        val result = startGo()
                        if (continuation.isActive) continuation.resume(result)
                    }
                }

                continuation.invokeOnCancellation { job.cancel() } // 协程取消时取消 job
            }
        }
    }

    private suspend fun startGo(): Boolean {
        return suspendCancellableCoroutine { continuation ->
            netWorkName = generateRandomNetworkName()
            password = generateRandomPassword()
            // 在雷达信道上建立GO 会出现异常，建立失败会导致GC扫描不到GO，连接失败， 现在默认指定特定 信道
            frequency = getRandomFrequency() // 获取一个随机频率
            Timber.tag(TAG).d("netWorkName:$netWorkName, password:$password, frequency: $frequency")

            val builder = WifiP2pConfig.Builder().apply {
                setNetworkName(netWorkName)
                setPassphrase(password)
                setGroupOperatingFrequency(frequency)
            }
            val wifiP2pConfig = builder.build()

            wifiP2pConfig.wps.setup = WpsInfo.PBC

            // 启动超时任务（10秒后触发）
            val timeoutJob = CoroutineScope(Dispatchers.IO).launch {
                delay(Start_Go_Delay_Time)
                if (continuation.isActive) {
                    Timber.tag(TAG).e("AutoGO failed: Timeout waiting for callback.")
                    continuation.resume(false)
                }
            }

            wifiP2pManager.createGroup(
                wifiP2pChannel,
                wifiP2pConfig,
                object : WifiP2pManager.ActionListener {
                    override fun onSuccess() {
                        Timber.tag(TAG).d("Successfully started AutoGO.")
                        timeoutJob.cancel()
                        if (continuation.isActive) continuation.resume(true)
                    }

                    override fun onFailure(reason: Int) {
                        Timber.tag(TAG).d("Failed to start AutoGO with reason $reason.")
                        timeoutJob.cancel()
                        when (reason) {
                            WifiP2pManager.ERROR -> {
                                Timber.tag(TAG).e("AutoGO failed: System error.")
                            }

                            WifiP2pManager.P2P_UNSUPPORTED -> {
                                Timber.tag(TAG).e("AutoGO failed: Device does not support Wi-Fi P2P.")
                            }

                            WifiP2pManager.BUSY -> {
                                Timber.tag(TAG).e("AutoGO failed: P2P is busy, retrying in 5s...")
                            }

                            else -> {
                                Timber.tag(TAG).e("AutoGO failed: Unknown reason ($reason).")
                            }
                        }
                        if (continuation.isActive) continuation.resume(false)
                    }
                }
            )

            continuation.invokeOnCancellation {
                Timber.tag(TAG).d("startGo() cancelled.")
                timeoutJob.cancel()
            }
        }
    }

    suspend fun enableWiFiP2P(client: IDeviceOperator<O95StateLiveData>): SystemProtos.WiFiP2P.Result? {
        Timber.tag(TAG).d("enableWiFiP2P = start, client=$client")
        return suspendCancellableCoroutine { con ->
            con.invokeOnCancellation {
                Timber.i("invokeOnCancellation----enableWiFi")
                con.cancel()
            }
            client.sendMiWearCommand(
                EnableWiFiP2PGC(
                    ssid = netWorkName,
                    password = password,
                    frequency = frequency
                ),
                object : IDeviceSyncCallback.Stub() {
                    override fun onSyncSuccess(did: String?, type: Int, result: SyncResult?) {
                        val wifiP2PResult = result?.packet?.system?.wifiP2PResult
                        val ipAddress = wifiP2PResult?.ipAddress
                        if (ipAddress.isNotNullOrEmpty()) {
                            MMKVUtils.encode(WIFI_P2P, ipAddress)
                        }
                        Timber.tag(TAG).d("enableWiFiP2P----wifiP2PResult=$wifiP2PResult code=${wifiP2PResult?.code}")
                        con.resumeCheckIsCompleted(wifiP2PResult, null)
                    }

                    override fun onSyncError(did: String?, type: Int, code: Int) {
                        Timber.tag(TAG).d("enableWiFi----type=$type,code=$code")
                        MMKVUtils.removeKey(WIFI_P2P)
                        con.resumeCheckIsCompleted(null, null)
                    }
                }
            )
        }
    }

    @Suppress("MaxLineLength")
    @SuppressLint("MissingPermission")
    suspend fun removeGroupIfNeed(): Boolean {
        Timber.tag(TAG).d("removeGroup start")
        MMKVUtils.removeKey(WIFI_P2P)

        return suspendCancellableCoroutine { continuation ->
            wifiP2pManager.requestGroupInfo(wifiP2pChannel) { group ->
                if (group != null) {
                    // 只有当存在 P2P 组时，才执行 removeGroup
                    Timber.tag(TAG).d("P2P group exists, attempting to remove")
                    removeGroup { success ->
                        continuation.resumeCheckIsCompleted(success, null)
                    }
                } else {
                    Timber.tag(TAG).d("No P2P group exists, skipping removeGroup()")
                    removeGroup { success ->
                        continuation.resumeCheckIsCompleted(success, null)
                    }
                }
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun removeGroup(callBack: (Boolean) -> Unit) {
        try {
            wifiP2pManager.removeGroup(
                wifiP2pChannel,
                object : WifiP2pManager.ActionListener {
                    override fun onSuccess() {
                        Timber.tag(TAG).d("removeGroup onSuccess")
                        callBack.invoke(true)
                    }

                    override fun onFailure(reason: Int) {
                        Timber.tag(TAG).d("removeGroup onFailure, reason: $reason")
                        when (reason) {
                            WifiP2pManager.ERROR -> {
                                Timber.tag(TAG).e("Failed to remove P2P group: System error")
                            }

                            WifiP2pManager.P2P_UNSUPPORTED -> {
                                Timber.tag(TAG).e("Failed to remove P2P group: P2P not supported on this device")
                            }

                            WifiP2pManager.BUSY -> {
                                Timber.tag(TAG).e("Failed to remove P2P group: System is busy, retrying...")
                            }

                            else -> {
                                Timber.tag(TAG).e("Failed to remove P2P group: Unknown reason ($reason)")
                            }
                        }
                        callBack.invoke(false)
                    }
                }
            )
        } catch (e: Exception) {
            Timber.tag(TAG).d("removeGroup exception:${e.printDetail()}")
            callBack.invoke(false)
        }
    }

    private var curNetWork: Network? = null
    fun bindProcessToNetwork() {
        val cm = LibBaseApplication.instance.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networks = cm.allNetworks
        for (network in networks) {
            val capabilities = cm.getNetworkCapabilities(network)
            if (capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                curNetWork = network
                cm.bindProcessToNetwork(network) // 让当前进程走 Wi-Fi
                Timber.tag(TAG).d("让当前进程走 Wi-Fi")
                break
            }
        }
    }

    fun unBindProcessToNetwork() {
        ConnectivityManager.setProcessDefaultNetwork(curNetWork)
        curNetWork = null
    }
}
