@file:Suppress("MagicNumber")

package com.superhexa.supervision.library.base.basecommon.extension

import android.annotation.SuppressLint
import android.text.format.DateUtils
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import org.apache.commons.lang3.time.DurationFormatUtils
import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.time.temporal.WeekFields
import java.util.Calendar
import java.util.Date
import java.util.Formatter
import java.util.Locale
import java.util.TimeZone
import java.util.concurrent.TimeUnit

/**
 * 类描述:时间格式化的类
 * 创建日期:2021/7/13 on 9:50 下午
 * 作者: FengPeng
 */
@Suppress("TooManyFunctions")
object DateTimeUtils {
    private const val yyyyMMdd = "yyyy年M月d日"
    private const val yyyyMMddHHmmss = "yyyy-MM-dd-HH-mm-ss"
    private const val HHmmss = "HH:mm:ss"
    private const val HHmmssfff = "H:m:ss:SSS"
    private const val tenMinute = 10 * 60 * 1000
    private const val multiFactor = 600
    private const val oneSecond = 1000
    private const val oneMinutes = 60
    private const val oneHours = 3600
    private const val TIME_FORMAT = "%02d:%02d.%02d"
    private const val TIME_FORMAT_SINGLE_LAST = "%02d:%02d:%02d"

    private const val MONDAY = 301 // 周的第一天 周一

    /**
     * 为时间分组
     * 对齐时间为该天的0点0分0秒
     * @param modifyTime Long
     * @return Long
     */
    fun getTimeGroup(modifyTime: Long): Long {
        val c = Calendar.getInstance()
        //        c.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        c.timeInMillis = modifyTime
        c[Calendar.HOUR_OF_DAY] = 0
        c[Calendar.MINUTE] = 0
        c[Calendar.SECOND] = 0
        c[Calendar.MILLISECOND] = 0
        return c.timeInMillis
    }

    /**
     * 格式化为年月日时分秒
     * @param date Long
     * @return String
     */
    fun formatyMdHs(date: Long): String {
        return SimpleDateFormat(yyyyMMddHHmmss, Locale.getDefault()).format(date)
    }

    /**
     * 格式化为年月日
     * @param date Long
     * @return String
     */
    fun formatyMd(date: Long): String {
        return SimpleDateFormat(yyyyMMdd, Locale.getDefault()).format(date)
    }

    /**
     * 格式化为年月日
     * @param date Long
     * @return String
     */
    fun formatyMd(format: String, date: Long): String {
        return SimpleDateFormat(format, Locale.getDefault()).format(date)
    }

    /**
     * 格式化为时分秒
     * @param date Long
     * @return String
     */
    fun formatHms(date: Long): String {
        return SimpleDateFormat(HHmmss, Locale.getDefault()).format(date)
    }

    /**
     * 是否是今天
     *
     * @param date
     */
    @JvmStatic
    fun isToday(date: LocalDate): Boolean {
        return LocalDate.now() == date
    }

    fun isToday(timestamp: Long): Boolean {
        val timestampDate = Instant.ofEpochMilli(timestamp)
            .atZone(ZoneId.systemDefault())
            .toLocalDate()

        // 获取今天的日期
        val today = LocalDate.now()

        // 判断是否为今天
        return timestampDate.isEqual(today)
    }

    fun isYesterday(timestamp: Long): Boolean {
        val timestampDate = Instant.ofEpochMilli(timestamp)
            .atZone(ZoneId.systemDefault())
            .toLocalDate()

        // 获取昨天的日期
        val yesterday = LocalDate.now().minus(1, ChronoUnit.DAYS)

        // 判断是否为昨天
        return timestampDate.isEqual(yesterday)
    }

    /**
     * 毫秒数转换为时间格式化字符串 支持是否显示小时
     */
    // 字符串
    private val sBuilder: StringBuilder = StringBuilder()

    // 格式
    private val sFormatter: Formatter = Formatter(sBuilder)
    fun stringForTime(timeSecondes: Long): String {
        val totalSeconds = (timeSecondes / oneSecond).toInt()
        val seconds = totalSeconds % oneMinutes
        val minutes = totalSeconds / oneMinutes % oneMinutes
        val hours = totalSeconds / oneHours
        synchronized(sBuilder) {
            sBuilder.setLength(0)
            return kotlin.runCatching {
                sFormatter.format(
                    "%s%02d:%02d:%02d",
                    "",
                    hours,
                    minutes,
                    seconds
                ).toString()
            }.getOrElse {
                ""
            }
        }
    }

    /**
     * 格式化为时分秒毫秒
     * @param date Long
     * @return String
     */
    fun formatHmsSSS(date: Long): String {
        return SimpleDateFormat(HHmmssfff, Locale.CHINA).format(date)
    }

    /**
     * 格式化视频时长
     * @param time Long
     * @return String
     */
    fun videoDuration(time: Long): String {
        return when {
            time <= 0 -> "0:00"
            // 10：00 小于10分钟
            time < tenMinute -> DurationFormatUtils.formatDuration(time, "m:ss")
            // 10分钟 到 10小时
            tenMinute <= time && time < multiFactor * tenMinute ->
                DurationFormatUtils.formatDuration(time, "H:mm:ss")

            else -> DurationFormatUtils.formatDuration(time, "d:HH:mm:ss")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun convertToTimestamp(dateString: String): Long {
        val dateFormat = SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH)
        dateFormat.timeZone = TimeZone.getTimeZone("GMT") // 确保解析为 GMT 时区

        return try {
            val date = dateFormat.parse(dateString)
            date?.time ?: 0 // 返回时间戳
        } catch (e: Exception) {
            e.printStackTrace()
            0
        }
    }

    fun formatDuration(durationMs: Long): String {
        val totalSeconds = durationMs / 1000
        val hours = totalSeconds / 3600
        val remainingSeconds = totalSeconds % 3600
        val minutes = remainingSeconds / 60
        val seconds = remainingSeconds % 60

        return buildString {
            if (hours > 0) append("${hours}h")
            if (minutes > 0 || hours > 0) append("${minutes}m")
            append("${seconds}s")
        }.ifEmpty { "0s" } // 处理 durationMs=0 的特殊情况
    }

    fun convertTimeStampToDateString(timeStamp: Long): String {
        val sdf = SimpleDateFormat("yyyy/MM/dd HH:mm", Locale.getDefault())
        val date = Date(timeStamp)
        return sdf.format(date)
    }

    fun convertTimeStampToString(timeStamp: Long): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        val date = Date(timeStamp)
        return sdf.format(date)
    }

    // 过去，昨前天等
    @JvmStatic
    fun isBeforeToday(localDate: LocalDate): Boolean {
        return localDate.isBefore(LocalDate.now())
    }

    // 未来，明后天等
    @JvmStatic
    fun isAfterToday(localDate: LocalDate): Boolean {
        return localDate.isAfter(LocalDate.now())
    }

    /**
     * 判断是否是同一天
     *
     * @param date
     * @param compareDate
     */
    @JvmStatic
    fun isSameLocalDate(date: LocalDate, compareDate: LocalDate): Boolean {
        return date.year == compareDate.year &&
            date.monthValue == compareDate.monthValue &&
            date.dayOfMonth == compareDate.dayOfMonth
    }

    /**
     * 判断传入的 LocalDate 是否与当前日期在同一周
     *
     * @param localDate 需要比较的日期
     * @return 如果在同一周返回 true，否则返回 false
     */
    @JvmStatic
    fun isSameWeekWithToday(localDate: LocalDate): Boolean {
        val today = LocalDate.now()

        // 获取本周的起始日期和结束日期
        val startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
        val endOfWeek = startOfWeek.plusDays(6)

        // 判断传入的日期是否在本周范围内
        return !localDate.isBefore(startOfWeek) && !localDate.isAfter(endOfWeek)
    }

    /**
     * 获取指定日期所在的周
     *
     * @param localDate 指定日期
     * @return 返回该日期所在的周
     */
    @JvmStatic
    fun getDayWeek(localDate: LocalDate): Int {
        // 使用系统默认的区域设置来获取周数
        val weekFields = WeekFields.of(Locale.getDefault())

        // 获取指定日期所在的周数
        return localDate.get(weekFields.weekOfWeekBasedYear())
    }

    /**
     * 获取传入日期所在周的周一日期
     *
     * @param localDate 传入的日期，如果为 null 则使用当前日期
     * @return 所在周的周一日期
     */
    @JvmStatic
    fun getWeekMonday(localDate: LocalDate?): LocalDate {
        val date = localDate ?: LocalDate.now() // 如果传入的日期为 null，使用当前日期
        return date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)) // 获取本周的周一
    }

    /**
     * 获得两个日期距离几周
     *
     * @param type 一周
     */
    @JvmStatic
    fun getIntervalWeek(startDate: LocalDate, endDate: LocalDate, type: Int? = MONDAY): Int {
        val startOfWeek =
            if (type == MONDAY) {
                startDate.with(DayOfWeek.MONDAY)
            } else {
                startDate.with(DayOfWeek.SUNDAY)
            }
        val endOfWeek =
            if (type == MONDAY) {
                endDate.with(DayOfWeek.MONDAY)
            } else {
                endDate.with(DayOfWeek.SUNDAY)
            }

        // 计算两个日期之间的周数
        return ChronoUnit.WEEKS.between(startOfWeek, endOfWeek).toInt()
    }

    /**
     * 第一个是不是第二个的上一个月,只在此处有效
     *
     * @param currentMonth
     * @param lastMonth
     */
    @JvmStatic
    fun isLastMonth(currentMonth: LocalDate, lastMonth: LocalDate): Boolean {
        val date = lastMonth.plusMonths(1)
        return date.monthValue == currentMonth.monthValue
    }

    /**
     * 第一个是不是第二个的下一个月，只在此处有效
     *
     * @param currentMonth
     * @param nextMonth
     */
    @JvmStatic
    fun isNextMonth(currentMonth: LocalDate, nextMonth: LocalDate): Boolean {
        val date = nextMonth.minusMonths(1)
        return date.monthValue == currentMonth.monthValue
    }

    /**
     * 判断是不是同一个月
     *
     * @param localDate
     * @param localDateCompare
     */
    @JvmStatic
    fun isSameMonth(localDate: LocalDate, localDateCompare: LocalDate): Boolean {
        return (
            localDate.year == localDateCompare.year &&
                localDate.monthValue == localDateCompare.monthValue
            )
    }

    @JvmStatic
    fun getIntervalMonths(date1: LocalDate, date2: LocalDate): Int {
        val startDate = date1.withDayOfMonth(1)
        val endDate = date2.withDayOfMonth(1)
        return ChronoUnit.MONTHS.between(startDate, endDate).toInt()
    }

    /**
     * 获取本月的1号对应的LocalDate，用来请求月的参数，返回LocalDate
     *
     * @param localDate
     */
    @JvmStatic
    fun getFirstDayOfMonth(localDate: LocalDate?): LocalDate {
        return if (localDate == null) {
            timestampToLocalDate(getFirstDayOfMonthTime(LocalDate.now()))
        } else {
            timestampToLocalDate(getFirstDayOfMonthTime(localDate))
        }
    }

    /**
     * 获取本月的1号对应的LocalDate，用来请求月的参数，返回 timestamp 秒 10位 s unit
     *
     * @param localDateParam
     */
    @JvmStatic
    fun getFirstDayOfMonthTime(localDateParam: LocalDate): Long {
        val distance = localDateParam.dayOfMonth
        val firstDayOfMonth = localDateParam.minusDays((distance - 1).toLong())
        return changeZeroOfTheDay(firstDayOfMonth)
    }

    /**
     * 获取下个月1号对应的LocalDate，用来请求月的参数，返回 LocalDate
     *
     * @param localDate
     */
    @JvmStatic
    fun getFirstDayOfNextMonth(localDate: LocalDate?): LocalDate {
        val timestamp = getNextMonthFirstDayOfTime(localDate ?: LocalDate.now())
        return timestampToLocalDate(timestamp)
    }

    /**
     * 获取下个月1号对应的LocalDate，用来请求月的参数，返回 timestamp 秒 10位 s unit
     *
     * @param localDate
     */
    @JvmStatic
    fun getNextMonthFirstDayOfTime(localDate: LocalDate?): Long {
        val tempLocalDate = localDate ?: LocalDate.now().plusMonths(1)
        return changeZeroOfTheDay(getFirstDayOfMonth(tempLocalDate))
    }

    @JvmStatic
    fun getFirstMonthOfTheYear(localDate: LocalDate): LocalDate {
        val monthOfTheYear = localDate.monthValue
        val firstMonthOfYear = localDate.minusMonths((monthOfTheYear - 1).toLong())
        return getFirstDayOfMonth(firstMonthOfYear)
    }

    @JvmStatic
    fun getMonthLocalDateCalendar(localDate: LocalDate): List<LocalDate> {
        return getMonthLocalDateCalendar(localDate, MONDAY)
    }

    private fun getMonthLocalDateCalendar(localDate: LocalDate, type: Int): List<LocalDate> {
        val dateList = mutableListOf<LocalDate>()
        val firstDayOfMonth = localDate.withDayOfMonth(1)
        val lastDayOfMonth = localDate.with(TemporalAdjusters.lastDayOfMonth())

        // 1. 补充上个月的日期（补满第一行）
        var startDate = firstDayOfMonth
        val startDayOfWeek = if (type == MONDAY) DayOfWeek.MONDAY else DayOfWeek.SUNDAY
        while (startDate.dayOfWeek != startDayOfWeek) {
            startDate = startDate.minusDays(1)
        }

        // 2. 补充下个月的日期（补满最后一行）
        var endDate = lastDayOfMonth
        val endDayOfWeek = if (type == MONDAY) DayOfWeek.SUNDAY else DayOfWeek.SATURDAY
        while (endDate.dayOfWeek != endDayOfWeek) {
            endDate = endDate.plusDays(1)
        }

        // 3. 生成日期区间（startDate 到 endDate，确保是完整的周数）
        var date = startDate
        while (!date.isAfter(endDate)) {
            dateList.add(date)
            date = date.plusDays(1)
        }

        return dateList
    }

    /**
     * 时间戳转成 LocalDate
     *
     * @param timestamp 13位 ms毫秒
     * @return LocalDate
     */
    @JvmStatic
    fun timestampToLocalDateInner(timestamp: Long): LocalDate {
        return Instant.ofEpochMilli(timestamp) // 将时间戳转换为 Instant
            .atZone(ZoneId.systemDefault()) // 转换为本地时区的 ZonedDateTime
            .toLocalDate() // 提取 LocalDate
    }

    /**
     * 时间戳转成 LocalDate
     *
     * @param timestamp 10位 秒 s
     */
    @JvmStatic
    fun timestampToLocalDate(timestamp: Long): LocalDate {
        return timestampToLocalDateInner(timestamp * 1000)
    }

    /**
     * LocalDate 转成 时间戳 10位 s 秒
     *
     * @param localDate
     * @return timestamp 10位 s 秒
     */
    @JvmStatic
    fun localDateToTimestamp(localDate: LocalDate): Long {
        return localDateToTimestampInner(localDate) / 1000
    }

    /**
     * LocalDate 转成 时间戳 13位 ms 毫秒
     *
     * @param localDate
     * @return timestamp 13位 ms 毫秒
     */
    private fun localDateToTimestampInner(localDate: LocalDate): Long {
        return localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }

    /**
     * 将时间戳转换为当天的 0 点时间戳（基于本地时区）
     *
     * @param mills 时间戳（毫秒）
     * @return 当天的 0 点时间戳（毫秒）
     */
    fun atStartOfDay(mills: Long): Long {
        return Instant.ofEpochMilli(mills) // 将时间戳转换为 Instant
            .atZone(ZoneId.systemDefault()) // 转换为本地时区的 ZonedDateTime
            .toLocalDate() // 提取 LocalDate
            .atStartOfDay(ZoneId.systemDefault()) // 获取当天的 0 点
            .toInstant() // 转换为 Instant
            .toEpochMilli() // 转换为时间戳
    }

    /**
     * 将 LocalDate 转换为当天的 0 点时间戳
     *
     * @param date 需要转换的 LocalDate
     * @return 当天的 0 点时间戳（毫秒）
     */
    fun atStartOfDay(date: LocalDate): Long {
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }

    fun getStartAndEndOfDay(timestamp: Long): Pair<Long, Long> {
        val instant = Instant.ofEpochSecond(timestamp) // 转换为 Instant 对象
        val zoneId = ZoneId.of("Asia/Shanghai") // 设置时区
        val zonedDateTime = ZonedDateTime.ofInstant(instant, zoneId) // 转换为 ZonedDateTime

        // 获取当天的 00:00:00
        val startOfDay = zonedDateTime
            .toLocalDate()
            .atStartOfDay(zoneId)
            .toEpochSecond()

        // 获取当天的 23:59:59
        val endOfDay = zonedDateTime
            .toLocalDate()
            .atTime(23, 59, 59).atZone(zoneId)
            .toEpochSecond()

        return Pair(startOfDay, endOfDay)
    }

    /**
     * 时间取整，取到当天的0点对应的事件 例如 2019-03-23 00：00：00 (13位)
     *
     * @param localDate
     * @return timeStamp 13位 ms 毫秒
     */
    @Suppress("TooGenericExceptionCaught", "TooGenericExceptionThrown")
    fun changeZeroOfTheDayInner(localDate: LocalDate): Long {
        val time = localDateToTimestampInner(localDate)
        val sdf = SimpleDateFormat("yyyy-MM-dd 00:00:00", Locale.getDefault())
        val date = Date(time)
        val dateStr = sdf.format(date)
        return try {
            Timestamp.valueOf(dateStr).time
        } catch (e: Exception) {
            throw RuntimeException("changZeroOfTheDayInner $dateStr", e)
        }
    }

    @Suppress("TooGenericExceptionCaught", "TooGenericExceptionThrown")
    fun changeZeroOfTheDayInner(time: Long): Long {
        val sdf = SimpleDateFormat("yyyy-MM-dd 00:00:00", Locale.getDefault())
        val date = Date(time)
        val dateStr = sdf.format(date)
        return try {
            Timestamp.valueOf(dateStr).time
        } catch (e: Exception) {
            throw RuntimeException("changZeroOfTheDayInner param time $dateStr", e)
        }
    }

    /**
     * 获取当日的0点， 返回timestamp 10位 秒 unit s
     *
     * @param time
     */
    @JvmStatic
    fun changeZeroOfTheDay(time: Long): Long {
        return changeZeroOfTheDayInner(time * 1000) / 1000
    }

    /**
     * 时间取整，取到当天的0点对应的事件 例如 2019-03-23 00：00：00 (10位)
     *
     * @param localDate
     * @return timeStamp 10位 s 秒
     */
    @JvmStatic
    fun changeZeroOfTheDay(localDate: LocalDate): Long {
        return atStartOfDay(localDate) / 1000
    }

    /**
     * 根据 patternStr显示不同的 时间样式
     *
     * @param timestamp
     * @param patternStr
     */
    @JvmStatic
    fun getDateStr(timestamp: Long, patternStr: String?): String {
        return getDateStr(timestamp, patternStr, Locale.getDefault())
    }

    @SuppressLint("SimpleDateFormat")
    @JvmStatic
    fun getDateStr(timeZone: TimeZone?, patternStr: String?): String {
        val simpleDateFormat = SimpleDateFormat(patternStr)
        val date = Date()
        simpleDateFormat.timeZone = timeZone
        return simpleDateFormat.format(date)
    }

    @SuppressLint("SimpleDateFormat")
    @JvmStatic
    fun getDate24HStr(timestamp: Long, patternStr: String?): String {
        val simpleDateFormat = SimpleDateFormat(patternStr)
        val date = Date(timestamp * 1000)
        return simpleDateFormat.format(date)
    }

    @JvmStatic
    fun getDateStr(timestamp: Long, patternStr: String?, locale: Locale?): String {
        val simpleDateFormat = SimpleDateFormat(patternStr, locale)
        val date = Date(timestamp * 1000)
        return simpleDateFormat.format(date)
    }

    @JvmStatic
    fun getDateStr(timestamp: Long, patternStr: String?, timeZone: TimeZone?): String {
        val simpleDateFormat = SimpleDateFormat(patternStr, Locale.getDefault())
        val date = Date(timestamp * 1000)
        timeZone?.let {
            simpleDateFormat.timeZone = it
        }
        return simpleDateFormat.format(date)
    }

    /**
     * 根据 patternStr显示不同的 时间样式
     *
     * @param localDate
     * @param patternStr
     */
    @JvmStatic
    fun getDateStr(localDate: LocalDate, patternStr: String?): String {
        return getDateStr(localDateToTimestamp(localDate), patternStr)
    }

    /**
     * 显示本地时间 月的 日期格式 简写
     * 英文 eg： Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec
     * 中文、香港、台湾 eg：  01月 2月...12月
     *
     * @param localDate
     */
    @JvmStatic
    fun getDateMMSimpleFormat(localDate: LocalDate): String {
        return getDateStr(localDate, "MMM")
    }

    @JvmStatic
    fun getDateMMSimpleFormat(mills: Long): String {
        return getDateMMSimpleFormat(
            Instant.ofEpochMilli(mills)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        )
    }

    /**
     * 显示本地时间 月的日期格式
     * 英文 eg： January February March April May June July August September October November December
     * 中文、香港、台湾 eg：  1月 2月...12月
     *
     * @param localDate
     */
    @JvmStatic
    fun getDateMMFormat(localDate: LocalDate): String {
        return DateUtils.formatDateTime(
            LibBaseApplication.instance,
            localDateToTimestampInner(localDate),
            DateUtils.FORMAT_ABBREV_MONTH
                or DateUtils.FORMAT_NO_MONTH_DAY
                or DateUtils.FORMAT_NO_YEAR
        )
    }

    /**
     * 显示本地时间 年/月/日
     * 英文 eg：03/05/2020
     * 中文/台湾/香港 2020/03/05
     *
     * @param localDate
     */
    @JvmStatic
    fun getDateSimpleLocalFormat(localDate: LocalDate): String {
        return DateUtils.formatDateTime(
            LibBaseApplication.instance,
            localDateToTimestampInner(localDate),
            DateUtils.FORMAT_SHOW_YEAR or DateUtils.FORMAT_NUMERIC_DATE
        )
    }

    @JvmStatic
    fun getDateMDFormat(localDate: LocalDate): String {
        val formatter = DateTimeFormatter.ofPattern("M/d")
        return localDate.format(formatter)
    }

    /**
     * 显示本地时间 年/月/日
     * 英文 eg：03/05/2020
     * 中文/台湾/香港 2020/03/05
     *
     * @param timeStamp 单位 秒
     */
    @JvmStatic
    fun getDateSimpleLocalFormat(timeStamp: Long): String {
        return DateUtils.formatDateTime(
            LibBaseApplication.instance,
            timeStamp * 1000,
            DateUtils.FORMAT_SHOW_YEAR or DateUtils.FORMAT_NUMERIC_DATE
        )
    }

    /**
     * 显示本地时间 年/月 日期格式
     * 英文 eg： February 2020
     * 中文、香港、台湾 eg：  2020年2月
     *
     * @param localDate
     */
    @JvmStatic
    fun getDateYYYYMMLocalFormat(localDate: LocalDate?): String {
        localDate?.let {
            return DateUtils.formatDateTime(
                LibBaseApplication.instance,
                localDateToTimestampInner(it),
                DateUtils.FORMAT_SHOW_YEAR or DateUtils.FORMAT_NO_MONTH_DAY
            )
        }
        return ""
    }

    /**
     * 显示本地时间 年/月/日 日期格式
     * 英文 eg： February, 26th 2020
     * 中文、香港、台湾 eg：  2020年2月26日
     *
     * @param localDate
     * @return
     */
    @JvmStatic
    fun getDateYYYYMMddLocalFormat(localDate: LocalDate?): String {
        localDate?.let {
            return DateUtils.formatDateTime(
                LibBaseApplication.instance,
                localDateToTimestampInner(localDate),
                DateUtils.FORMAT_SHOW_YEAR
            )
        }
        return ""
    }

    @JvmStatic
    fun getDateYYYYMMddLocalFormat(mills: Long): String {
        return getDateYYYYMMddLocalFormat(
            Instant.ofEpochMilli(mills)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        )
    }

    @SuppressLint("DefaultLocale")
    fun formatTime(millis: Long): String {
        return with(TimeUnit.MILLISECONDS) {
            val hour = toHours(millis)
            val minutes = toMinutes(millis) % 60
            val seconds = toSeconds(millis) % 60
//            val cents = (millis % 100) / 10
            String.format(TIME_FORMAT_SINGLE_LAST, hour, minutes, seconds)
        }
    }

    fun getDateYYYYMMddHHmmssLocalFormat(timestamp: Long): String {
        val formatter = SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault())
        return formatter.format(Date(timestamp))
    }
}
