package com.xiaomi.aivs

import android.annotation.SuppressLint
import android.content.Context
import android.location.Location
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.ExoPlayer
import com.google.gson.Gson
import com.superhexa.music.MusicApiService
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.event.UnLoginEvent
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.xiaomi.ai.android.core.Engine
import com.xiaomi.ai.api.AIApiConstants
import com.xiaomi.ai.api.FullScreenTemplate
import com.xiaomi.ai.api.General
import com.xiaomi.ai.api.MultiModal
import com.xiaomi.ai.api.Nlp
import com.xiaomi.ai.api.SpeechRecognizer
import com.xiaomi.ai.api.SpeechRecognizer.RecognizeStreamFinished
import com.xiaomi.ai.api.SpeechWakeup
import com.xiaomi.ai.api.Sys
import com.xiaomi.ai.api.Template
import com.xiaomi.ai.api.common.APIUtils
import com.xiaomi.ai.api.common.Event
import com.xiaomi.ai.api.common.EventPayload
import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.ai.api.common.InstructionHeader
import com.xiaomi.ai.api.common.InstructionPayload
import com.xiaomi.aivs.data.KeyWorld
import com.xiaomi.aivs.data.StreamType
import com.xiaomi.aivs.data.TtsState
import com.xiaomi.aivs.data.model.AccountConfig
import com.xiaomi.aivs.data.model.AuthConfig
import com.xiaomi.aivs.engine.EngineHelper
import com.xiaomi.aivs.engine.EngineHelper.createWakeUpEvent
import com.xiaomi.aivs.engine.ISpeechEngine
import com.xiaomi.aivs.engine.event.DeviceEvent
import com.xiaomi.aivs.engine.helper.CpHelper
import com.xiaomi.aivs.engine.helper.ImageEventProcess
import com.xiaomi.aivs.engine.helper.ImageFileHandler
import com.xiaomi.aivs.engine.helper.ToneHelper
import com.xiaomi.aivs.engine.listener.IAlipayListener
import com.xiaomi.aivs.engine.listener.IAlipayResultListener
import com.xiaomi.aivs.engine.listener.IExpandAbility
import com.xiaomi.aivs.engine.listener.ISpeechChatListener
import com.xiaomi.aivs.engine.listener.ISpeechEngineListener
import com.xiaomi.aivs.engine.policy.ITimeOutPolicy
import com.xiaomi.aivs.engine.policy.TimeOutPolicyImpl
import com.xiaomi.aivs.engine.proxy.ISpeechEngineProxy
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl
import com.xiaomi.aivs.engine.state.EngineStateMachine
import com.xiaomi.aivs.monitor.IMonitorEvent
import com.xiaomi.aivs.monitor.MonitorId
import com.xiaomi.aivs.player.SoundPlayer
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.track.EventTrackKv
import com.xiaomi.aivs.track.UnResponseInfo
import com.xiaomi.aivs.utils.FileUtils
import com.xiaomi.aivs.utils.PackageHelper
import com.xiaomi.aivs.wearable.IWearableFunc
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

/**
 * 小爱SDK-引擎调用入口.
 */
@Suppress("TooManyFunctions", "LargeClass")
class AiSpeechEngine private constructor() :
    ISpeechEngine,
    ITimeOutPolicy,
    IExpandAbility,
    IWearableFunc,
    IMonitorEvent,
    ISpeechEngineListener,
    ISpeechChatListener,
    IAlipayListener,
    IAlipayResultListener {

    lateinit var appContext: Context

    private var isFullDuplex = true
    private var speechEngineProxy: ISpeechEngineProxy? = null
    private var iWearableFunc: IWearableFunc? = null

    private val timeOutPolicy = TimeOutPolicyImpl()
    private val imageEventProcess = ImageEventProcess(this)
    private val engineObserverMaps = ConcurrentHashMap<String, ISpeechEngineListener>()
    private val chatObserverMaps = ConcurrentHashMap<String, ISpeechChatListener>()
    private val alipayObserverMaps = ConcurrentHashMap<String, IAlipayListener>()
    private val alipayResultsObserverMaps = ConcurrentHashMap<String, IAlipayResultListener>()

    private var pingTimer: Timer? = null
    private val initialValue = 0
    private var getPongNum = initialValue
    private var pingNum = initialValue
    private var curPingId: String? = null
    private var currentMediaType: SpeechEngineProxyImpl.Companion.AudioType? = null

    // 眼镜小核发送过来的requestId.
    private var wakeupRequestId: String? = null
    private var wakeupTransactionId: String? = null

    // 是否进入支付宝支付技能
    private var isAlipayStatus = false

    // 是否是支付宝新手引导演示模式
    private var isAlipayDemoMode = false

    override fun init(context: Context, config: AuthConfig) {
        appContext = context.applicationContext
        speechEngineProxy = SpeechEngineProxyImpl(context)
        speechEngineProxy?.init(context, config)
        Timber.d("Signatures:\n ${PackageHelper.getSignatures(context, context.packageName)}")
    }

    override fun startup(accountConfig: AccountConfig) {
        if (accountConfig.serviceToken.isEmpty()) {
            Timber.w("serviceToken为空，需要重新登录.")
            EventBus.getDefault().post(UnLoginEvent())
            return
        }
        speechEngineProxy?.startup(accountConfig)
    }

    override fun onReceiveRequestId(requestId: String, transactionId: String) {
        Timber.tag(TAG).d("onReceiveRequestId:$requestId,$transactionId,${dialogState()}")
        if (!EngineStateMachine.isIdle()) return
        wakeupRequestId = requestId
        wakeupTransactionId = transactionId
    }

    override fun postEvent(
        payload: EventPayload,
        requestId: String?,
        params: Map<String, String>?,
        preRunnable: Runnable?,
        withContext: Boolean
    ): String {
        return speechEngineProxy
            ?.postEvent(payload, requestId, params, preRunnable, withContext) ?: ""
    }

    override fun postEvent(event: Event<*>) {
        speechEngineProxy?.postEvent(event)
    }

    override fun postImageEvent(
        payload: EventPayload,
        requestId: String?,
        isFetchDeviceInfo: Boolean,
        params: Map<String, String>?
    ): String {
        return speechEngineProxy
            ?.postImageEvent(payload, requestId, isFetchDeviceInfo, params) ?: ""
    }

    override fun postWakeupBegin(bufferSize: Int) {
        Timber.tag(TAG).d("postWakeupBegin:$bufferSize,$wakeupRequestId")
        EventTrack.onEventTrackTime(key = EventTrackKv.WAKEUP_RECEIVED_EVENT)
        val payload = createWakeUpEvent(bufferSize)
        postEvent(
            payload = payload,
            requestId = wakeupRequestId,
            withContext = false
        )
    }

    override fun postSpeechBegin(isDuplexMode: Boolean): String {
        Timber.tag(TAG).d("postSpeechBegin:${dialogState()},$wakeupRequestId,$wakeupTransactionId")
        EventTrack.onEventTrackTime(key = EventTrackKv.ASR_FIRST_PACK_SENT)
        this.isFullDuplex = isDuplexMode
        val payload = if (isDuplexMode) {
            SpeechRecognizer.DuplexRecognizeStarted().apply {
                setIsAsyncTtsAudio(true)
                wakeupRequestId?.let { setFirstRoundRid(it) }
            }
        } else {
            SpeechRecognizer.Recognize()
        }
        val requestId = postEvent(
            payload = payload,
            requestId = wakeupTransactionId,
            preRunnable = {
                wakeupRequestId = null
                wakeupTransactionId = null
                if (!EngineStateMachine.isIdle()) {
                    interrupt(
                        reason = "!EngineStateMachine.isIdle()",
                        stopOptions = SpeechEngineProxyImpl.Companion.TtsStopOptions(
                            calledFrom = "!EngineStateMachine.isIdle()",
                            needResumeMediaPlayer = null,
                            needStopMediaPlayer = null,
                            stopReason = null
                        )
                    )
                }
            }
        )
        if (isDuplexMode) {
            initTimer(requestId, "初始化.")
        }
        MMKVUtils.encode(ConstsConfig.LAST_RID, requestId)
        Timber.tag(TAG).d("postSpeechBegin:$requestId,$isDuplexMode,${speechEngineProxy != null}")
        sendPing()
        return requestId
    }

    override fun postWakeupData(bytes: ByteArray?, offset: Int, length: Int) {
        Timber.d("postWakeupData:$length")
        speechEngineProxy?.postSpeechData(bytes, offset, length, false)
    }

    override fun postWakeupEnd() {
        Timber.tag(TAG).d("postWakeupEnd")
        postEvent(
            payload = SpeechWakeup.WakeupStreamFinished(),
            requestId = wakeupRequestId,
            withContext = false
        )
    }

    @OptIn(DelicateCoroutinesApi::class)
    override fun postSpeechData(bytes: ByteArray?, offset: Int, length: Int, isFinal: Boolean) {
        bytes?.let { sendVoiceData(bytes) }
        speechEngineProxy?.postSpeechData(bytes, offset, length, isFinal)
        // VoiceFeedBack模式会保存音频数据到pcm文件.
        Timber.tag(TAG)
            .d("postSpeechData isVoiceFeedBack= ${EngineStateMachine.isVoiceFeedBack()}, bytes= $bytes")
        if (EngineStateMachine.isVoiceFeedBack() && bytes != null) {
            GlobalScope.launch(Dispatchers.IO) {
                FileUtils.saveFileToCacheDirAsPcm(appContext, "VoiceFeedBack_" + requestId(), bytes)
            }
        }
    }

    override fun postSpeechEnd(requestId: String, isDuplexMode: Boolean) {
        Timber.tag(TAG).d("postSpeechEnd:$requestId,$isDuplexMode")
        if (requestId.isEmpty()) return

        val payload = if (isDuplexMode) {
            SpeechRecognizer.DuplexRecognizeFinished()
        } else {
            RecognizeStreamFinished()
        }
        postEvent(payload, requestId)
    }

    override fun onUploadMultiModalEvent(
        transactionId: String?,
        dialogId: String,
        payload: String?,
        isPush: Boolean
    ) {
        Timber.tag(TAG).d("onUploadMultiModalEvent:$transactionId,$dialogId,$isPush")
        imageEventProcess.onUploadMultiModalEvent(transactionId, dialogId, payload, isPush)
    }

    override fun postImageBegin(
        totalSize: Int,
        chunkSize: Int,
        type: ImageFileHandler.ImageType,
        requestId: String
    ): String {
        Timber.tag(TAG).d("postImageBegin:$totalSize,$chunkSize")
        return imageEventProcess.postImageBegin(totalSize, chunkSize, type, requestId)
    }

    override fun postImageData(
        requestId: String,
        format: String,
        size: Pair<Int, Int>,
        chunk: Pair<Int, Int>,
        bytes: ByteArray?
    ) {
        speechEngineProxy?.postImageData(requestId, format, size, chunk, bytes)
//        GlobalScope.launch {
//            bytes?.let {
//                FileUtils.saveFileToCacheDirAsImage(appContext, requestId, bytes)
//            }
//        }
    }

    override fun postImageData(
        requestId: String,
        bytes: ByteArray?,
        format: String,
        size: Pair<Int, Int>,
        type: ImageFileHandler.ImageType,
        postEnd: (() -> Unit)?
    ) {
        Timber.d("post Image type is $type")
        imageEventProcess.postImageData(requestId, bytes, format, size, type, postEnd)
    }

    // 眼镜图片上传完成
    override fun postImageEnd(imageRequestId: String, dialogId: String?) {
        Timber.tag(TAG).d("postImageEnd:$imageRequestId,$dialogId")
        val payload = MultiModal.ImageStreamFinished()
        postImageEvent(payload, imageRequestId)
        sendEventToDevice(requestId(), dialogId, DeviceEvent.IMAGE_TRANS_STOP)
        restartTimer(reason = "上传图片完成", countdownTime())
    }

    override fun postImageOcrData(totalSize: Int, data: ByteArray?) {
        Timber.tag(TAG).d("postImageOcrData:$totalSize,${data?.size}")
        imageEventProcess.postImageOcrData(totalSize, data)
    }

    override fun onImageFileId(dialogId: String?, imageId: String?) {
        Timber.tag(TAG).d("onImageFileId:$dialogId,$imageId")
        imageEventProcess.onImageFileId(dialogId, imageId)
    }

    override fun postNlpEvent(query: String) {
//        Timber.tag(TAG).d("postNlpEvent:$query")
        postEvent(Nlp.Request(query))
    }

    override fun postFeedBackEvent() {
        Timber.tag(TAG).d("postFeedBackEvent")
        speechEngineProxy?.postFeedBackEvent()
    }

    override fun enterStandby(streamId: String?) {
        Timber.tag(TAG).d("enterStandby:$streamId")
        EngineStateMachine.onStandbyEnter(streamId)
        onMonitorEvent(MonitorId.STANDBY)
        restartTimer("enterStandby", countdownTime())
    }

    override fun exitStandby(reason: String?) {
        Timber.tag(TAG).d("exitStandby:$reason")
        EngineStateMachine.onStandbyExit()
        finishSession(reason = "exitStandby")
    }

    override fun enterAlipay(streamId: String?) {
        Timber.tag(TAG).d("enterAlipay:$streamId")
        EngineStateMachine.onAlipayEnter(streamId)
        restartTimer("enterAlipay", countdownTime())
    }

    override fun exitAlipay(reason: String?) {
        Timber.tag(TAG).d("exitAlipay:$reason")
        EngineStateMachine.onAlipayExit()
    }

    override fun enterVoiceFeedBack(streamId: String?) {
        Timber.tag(TAG).d("enterVoiceFeedBack:$streamId")
        EngineStateMachine.onVoiceFeedBackEnter(streamId)
        restartFeedBackTimer("enterVoiceFeedBack", countdownTime())
    }

    override fun saveFeedBackAsr(asr: String, requestId: String) {
        speechEngineProxy?.saveFeedBackAsr(asr, requestId)
    }

    override fun sendTaskBroadcast(requestId: String, requestType: String) {
        speechEngineProxy?.sendTaskBroadcast(requestId, requestType)
    }

    override fun finishSession(
        requestId: String?,
        byCloud: Boolean,
        reason: String?,
        playSound: Boolean,
        isNeedAudioResume: Boolean?
    ) {
        val finishReason = "finishSession"
        Timber.tag(TAG).d("$finishReason:$byCloud,$finishReason,$playSound,$isNeedAudioResume")
        if (EngineStateMachine.isIdle()) {
            Timber.tag(TAG).w("it is already idle!!!")
            return
        }
        clearVoiceData()
        alipayInterrupt(reason)
        interrupt(
            reason = reason,
            stopOptions = SpeechEngineProxyImpl.Companion.TtsStopOptions(
                calledFrom = reason ?: finishReason,
                needResumeMediaPlayer = isNeedAudioResume,
                needStopMediaPlayer = when (reason) {
                    "收到云端退出:UIController.NavigateOp.EXIT.",
                    "releaseEngine" -> true

                    else -> null
                },
                stopReason = null
            )
        )
        EngineStateMachine.onDialogExit(playSound)
        imageEventProcess.cleanImageQueryResMap()
        cancelTimer(finishReason, true)
        cancelFeedBackJobs()
        sendEventToDevice(event = DeviceEvent.VOICE_STOP_CAPTURE)
        if (!byCloud) { // 非云端结束,需要给云端发送结束事件.
            postSpeechEnd(requestId ?: timeOutPolicy.requestId())
        }
        closePingPong(finishReason)
        EventTrack.doEventTrack()
        ImageFileHandler.closeAll()
    }

    override fun engineState(): Int {
        return EngineStateMachine.engineState()
    }

    override fun dialogState(): Int {
        return EngineStateMachine.dialogState()
    }

    override fun ttsState(): Int {
        return EngineStateMachine.ttsState()
    }

    override fun streamDialogType(): String {
        val streamId = EngineStateMachine.streamId()
        return when (streamId) {
            FullScreenTemplate.StreamDialogType.AGENT_SKILL.name -> StreamType.AGENT
            FullScreenTemplate.StreamDialogType.ROLE_PLAY_SKILL.name -> StreamType.ROLE_PLAY
            else -> StreamType.STANDBY
        }
    }

    override fun isTtsSpeaking(): Boolean {
        val isTtsSpeaking = speechEngineProxy?.isTtsSpeaking() ?: false
        Timber.tag(TAG).i("isTtsSpeaking:$isTtsSpeaking")
        return isTtsSpeaking
    }

    /**
     * 组装generateToastInstruction
     */
    fun generateToastInstruction(
        tts: String,
        instructionId: String,
        dialogId: String
    ): Instruction<Template.Toast> {
        val toastInstruction = Instruction<Template.Toast>()
        val toastHeader =
            InstructionHeader(AIApiConstants.Template.NAME, "Toast")
        toastHeader.id = instructionId
        toastHeader.setDialogId(dialogId)
        toastInstruction.header = toastHeader
        toastInstruction.payload = Template.Toast(tts)
        return toastInstruction
    }

    /**
     * 组装generateQueryInstruction
     */
    fun generateQueryInstruction(
        tts: String,
        instructionId: String,
        dialogId: String
    ): Instruction<Template.Query> {
        val toastInstruction = Instruction<Template.Query>()
        val toastHeader =
            InstructionHeader(AIApiConstants.Template.NAME, "Query")
        toastHeader.id = instructionId
        toastHeader.setDialogId(dialogId)
        toastInstruction.header = toastHeader
        toastInstruction.payload = Template.Query(tts)
        return toastInstruction
    }

    /**
     * 构造Sys.Exception指令
     */
    fun generateSysExceptionInstruction(
        code: Int,
        errMsg: String,
        dialogId: String
    ): Instruction<Sys.Exception> {
        val toastInstruction = Instruction<Sys.Exception>()
        val toastHeader =
            InstructionHeader(AIApiConstants.System.NAME, "Exception")
        toastHeader.id = UUID.randomUUID().toString()
        toastHeader.setDialogId(dialogId)
        toastInstruction.header = toastHeader
        toastInstruction.payload = Sys.Exception(code, errMsg, 0)
        return toastInstruction
    }

    override fun startTts(text: String, params: Map<String, String>?): String? {
        val dialogId = params?.get(KeyWorld.REQUEST_ID)
        dialogId?.let {
            val toastInstruction = generateToastInstruction(text, "mockId", dialogId)
            onTextResponseSynthesizer(
                "mockSessionId",
                dialogId,
                it,
                true,
                instructionJson = toastInstruction.toString()
            )
        }
        return speechEngineProxy?.startTts(text, params)
    }

    override fun stopTts(
        dialogId: String?,
        stopOptions: SpeechEngineProxyImpl.Companion.TtsStopOptions
    ) {
        speechEngineProxy?.stopTts(dialogId, stopOptions)
    }

    override fun playTipSound(resourceId: Int, complete: (() -> Unit)?) {
        speechEngineProxy?.playTipSound(resourceId, complete)
    }

    override fun resumeMediaPlayer(reason: String?) {
        speechEngineProxy?.resumeMediaPlayer(reason)
    }

    override fun stopMediaPlayer(reason: String?) {
        speechEngineProxy?.stopMediaPlayer(reason)
    }

    override fun pauseMediaPlayer(reason: String?) {
        speechEngineProxy?.pauseMediaPlayer(reason)
    }

    override fun isLongAudioPlaying(): Boolean? {
        return speechEngineProxy?.isLongAudioPlaying()
    }

    override fun isLongAudioPausing(): Boolean? {
        return speechEngineProxy?.isLongAudioPausing()
    }

    override fun handleMediaControl(payload: InstructionPayload) {
        speechEngineProxy?.handleMediaControl(payload)
    }

    fun getCurrentMediaType(): SpeechEngineProxyImpl.Companion.AudioType? {
        Timber.d("getCurrentMediaType:$currentMediaType")
        return currentMediaType
    }

    fun saveCurrentMediaType(type: SpeechEngineProxyImpl.Companion.AudioType?) {
        currentMediaType = type
        if (type == SpeechEngineProxyImpl.Companion.AudioType.MUSIC) {
            pauseMediaPlayer("saveCurrentMediaType $type ")
        } else if (MusicApiService.INSTANCE.isPlaying() &&
            type == SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO
        ) {
            MusicApiService.INSTANCE.stop()
        }
        Timber.d("saveCurrentMediaType:$currentMediaType")
    }

    override fun interrupt(
        stopTts: Boolean,
        reason: String?,
        stopOptions: SpeechEngineProxyImpl.Companion.TtsStopOptions
    ) {
        Timber.d("interrupt:$stopTts , reason: $reason ,isNeedResume: ${stopOptions.needResumeMediaPlayer}")
        speechEngineProxy?.interrupt(stopTts, reason, stopOptions)
        if (stopTts) {
            stopTts(stopOptions = stopOptions)
            SoundPlayer.INSTANCE.stop()
        }
        reason?.let {
            EventTrack.onEventTrack(
                key = EventTrackKv.UNRESPONSE_INFO,
                value = Gson().toJson(UnResponseInfo(System.currentTimeMillis(), it))
            )
            EventTrack.doEventTrack()
        }
    }

    override fun releaseEngine() {
        Timber.tag(TAG).d("releaseEngine")
        finishSession(reason = "releaseEngine")
        speechEngineProxy?.releaseEngine()
        speechEngineProxy = null
    }

    override fun destroy() {
        Timber.tag(TAG).d("destroy")
        releaseEngine()
        engineObserverMaps.clear()
        chatObserverMaps.clear()
    }

    override fun getTtsFont(): Int {
        return ToneHelper.getTtsFont()
    }

    override fun getAiEngine(): Engine? {
        return speechEngineProxy?.getAiEngine()
    }

    override fun setTtsFont(font: Int) {
//        Timber.tag(TAG).d("setTtsFont:$font")
        ToneHelper.setTtsFont(font)
    }

    override fun fontList(): List<Int> {
        return ToneHelper.fontList()
    }

    override fun fullDuplexTimeout(): Long {
        val timeout = EngineHelper.getFullDuplexTimeout()
        Timber.d("fullDuplexTimeout:$timeout")
        return timeout
    }

    override fun setFullDuplexTimeout(second: Long) {
        Timber.tag(TAG).d("setFullDuplexTimeout:$second")
        EngineHelper.setFullDuplexTimeout(second)
    }

    override fun getMusicSource(): Int {
        return CpHelper.getMusicSource()
    }

    override fun setMusicSource(source: Int) {
        CpHelper.setMusicSource(source)
    }

    override fun getRadioStationSource(): Int {
        return CpHelper.getRadioStationSource()
    }

    override fun setRadioStationSource(source: Int) {
        CpHelper.setRadioStationSource(source)
    }

    override fun musicSources(): List<Pair<Int, String>> {
        return CpHelper.musicSources()
    }

    override fun radioStationSources(): List<Pair<Int, String>> {
        return CpHelper.radioStationSources()
    }

    override fun addEngineObserver(
        key: String,
        lifecycle: Lifecycle?,
        listener: ISpeechEngineListener
    ) {
        Timber.tag(TAG).d("addEngineObserver:$key")
        engineObserverMaps[key] = listener
        lifecycle?.let {
            lifecycle.addObserver(
                LifecycleEventObserver { _, event ->
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        removeEngineObserver(key)
                    }
                }
            )
        }
    }

    override fun removeEngineObserver(key: String) {
        Timber.tag(TAG).d("removeEngineObserver:$key")
        engineObserverMaps.remove(key)
    }

    override fun addChatDataObserver(
        key: String,
        lifecycle: Lifecycle?,
        listener: ISpeechChatListener
    ) {
        Timber.tag(TAG).d("addChatDataObserver:$key")
        chatObserverMaps[key] = listener
        lifecycle?.let {
            lifecycle.addObserver(
                LifecycleEventObserver { _, event ->
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        removeChatDataObserver(key)
                    }
                }
            )
        }
    }

    override fun removeChatDataObserver(key: String) {
        Timber.tag(TAG).d("removeChatDataObserver:$key")
        chatObserverMaps.remove(key)
    }

    override fun addAlipayObserver(
        key: String,
        lifecycle: Lifecycle?,
        listener: IAlipayListener
    ) {
        Timber.tag(TAG).d("addAlipayObserver:$key")
        alipayObserverMaps[key] = listener
        lifecycle?.let {
            lifecycle.addObserver(
                LifecycleEventObserver { _, event ->
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        removeAlipayObserver(key)
                    }
                }
            )
        }
    }

    override fun removeAlipayObserver(key: String) {
        Timber.tag(TAG).d("removeAlipayObserver:$key")
        alipayObserverMaps.remove(key)
    }

    override fun addAlipayResultsObserver(
        key: String,
        lifecycle: Lifecycle?,
        listener: IAlipayResultListener
    ) {
        Timber.tag(TAG).d("addAlipayResultsObserver:$key")
        alipayResultsObserverMaps[key] = listener
        lifecycle?.let {
            lifecycle.addObserver(
                LifecycleEventObserver { _, event ->
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        removeAlipayResultsObserver(key)
                    }
                }
            )
        }
    }

    override fun removeAlipayResultsObserver(key: String) {
        Timber.tag(TAG).d("removeAlipayResultsObserver:$key")
        alipayResultsObserverMaps.remove(key)
    }

    override fun requestId(): String {
        return timeOutPolicy.requestId()
    }

    override fun isFirstDialogRound(): Boolean {
        return timeOutPolicy.isFirstDialogRound()
    }

    override fun countdownTime(): Long {
        return timeOutPolicy.countdownTime()
    }

    override fun initTimer(requestId: String, reason: String?) {
        timeOutPolicy.initTimer(requestId, reason)
    }

    override fun restartTimer(
        reason: String?,
        countdownTime: Long,
        isUserInputWait: Boolean,
        isFirstTime: Boolean
    ) {
        timeOutPolicy.restartTimer(reason, countdownTime, isUserInputWait)
    }

    override fun restartFeedBackTimer(reason: String?, countdownTime: Long) {
        timeOutPolicy.restartFeedBackTimer(reason, countdownTime)
    }

    override fun cancelTimer(reason: String?, withCmd: Boolean) {
        timeOutPolicy.cancelTimer(reason, withCmd)
    }

    override fun cancelFeedBackJobs() {
        timeOutPolicy.cancelFeedBackJobs()
    }

    override fun onAsrInput(dialogId: String?, input: String?, isFinal: Boolean) {
        timeOutPolicy.onAsrInput(dialogId, input, isFinal)
    }

    override fun onNlpEnter(
        dialogId: String?,
        cmd: String?,
        isStream: Boolean,
        reCountDown: Boolean
    ) {
        closePingPong("onNlpEnter")
        timeOutPolicy.onNlpEnter(dialogId, cmd, isStream, reCountDown)
    }

    override fun onNlpExit(dialogId: String?, cmd: String?, isStream: Boolean) {
        timeOutPolicy.onNlpExit(dialogId, cmd, isStream)
    }

    override fun onDialogReject(dialogId: String?) {
        timeOutPolicy.onDialogReject(dialogId)
    }

    override fun onStreamInstruction(instruction: Instruction<*>?) {
        timeOutPolicy.onStreamInstruction(instruction)
    }

    override fun isStreamDialogSupport(): Boolean {
        return timeOutPolicy.isStreamDialogSupport()
    }

    override fun setStreamDialogSupport(support: Boolean) {
        timeOutPolicy.setStreamDialogSupport(support)
    }

    override fun addGlassFunc(iWearableFunc: IWearableFunc) {
        this.iWearableFunc = iWearableFunc
    }

    override fun execute(directive: String) {
        iWearableFunc?.execute(directive)
    }

    override fun action(name: String, payload: InstructionPayload) {
        iWearableFunc?.action(name, payload)
        handleMediaControl(payload)
    }

    override fun setProperty(name: String, value: String) {
        iWearableFunc?.setProperty(name, value)
    }

    override fun getGlassColorAbility(): Triple<Int, Int, IntArray>? {
        return iWearableFunc?.getGlassColorAbility()
    }

    override fun getGlassElectricity(): Int {
        return iWearableFunc?.getGlassElectricity() ?: 0
    }

    override fun queryUserQuery(dialogId: String?, onQuery: (String?) -> Unit) {
        iWearableFunc?.queryUserQuery(dialogId, onQuery)
    }

    override fun curLocation(): Location? {
        return iWearableFunc?.curLocation()
    }

    override fun startRecord() {
        iWearableFunc?.startRecord()
    }

    override fun stopRecord() {
        iWearableFunc?.stopRecord()
    }

    override fun startRecordTranslate() {
        iWearableFunc?.startRecordTranslate()
    }

    override fun startF2fTranslate() {
        iWearableFunc?.startF2fTranslate()
    }

    override fun onMonitorEvent(id: String) {
        Timber.d("onMonitorEvent:$id,${dialogState()}")
        if (EngineStateMachine.isIdle()) return

        val payload = General.ContextUpdate()
        val params = mapOf(KeyWorld.MONITOR_ID to id)
        postEvent(
            payload = payload,
            requestId = requestId(),
            params = params
        )
    }

    override fun onConnectState(isConnected: Boolean) {
//        Timber.tag(TAG).d("onConnectState:$isConnected")
        MainScope().launch {
            engineObserverMaps.forEach { (_, observer) ->
                observer.onConnectState(isConnected)
            }
        }
//        if (isConnected && IS_DEBUG) {
        // postNlpQuery("拍照看一下这是什么")
//             postNlpEvent("北京有什么好吃的")
//             postNlpEvent("给我讲个笑话")
//            setTtsFont(FONT.PAO_FU)
//            val params = mapOf(KeyWorld.PARAM_FONT to "${FONT.PAO_FU}")
//            startTts("你给我讲个故事呗~", params)
//        }
    }

    override fun onDialogState(state: Int) {
//        Timber.tag(TAG).d("onDialogState:$state")
        MainScope().launch {
            engineObserverMaps.forEach { (_, observer) -> observer.onDialogState(state) }
        }
    }

    override fun sendEventToDevice(
        transactionId: String?,
        dialogId: String?,
        payload: String?,
        @DeviceEvent event: Int
    ) {
//        Timber.tag(TAG).d("sendEventToDevice:$transactionId,$requestId,$event")
        MainScope().launch {
            engineObserverMaps.forEach { (_, observer) ->
                observer.sendEventToDevice(transactionId ?: requestId(), dialogId, payload, event)
            }
        }
    }

    override fun sendEventToDevice(
        transactionId: String?,
        dialogId: String?,
        @DeviceEvent event: Int
    ) {
//        Timber.tag(TAG).d("sendEventToDevice:$transactionId,$requestId,$event")
        MainScope().launch {
            engineObserverMaps.forEach { (_, observer) ->
                observer.sendEventToDevice(transactionId ?: requestId(), dialogId, event)
            }
        }
    }

    override fun syncStateToDevice(dialogState: Int, engineState: Int, ttsState: Int) {
//        Timber.tag(TAG).d("syncStateToDevice:$dialogState,$engineState")
        MainScope().launch {
            engineObserverMaps.forEach { (_, observer) ->
                observer.syncStateToDevice(dialogState, engineState, ttsState)
            }
        }
    }

    override fun onSpeechEventTrack(
        eventName: String,
        params: Map<String, Any>?
    ) {
//        Timber.tag(TAG).d("onSpeechEventTrack:$dialogId,$name,$value,$isFinal")
        MainScope().launch {
            engineObserverMaps.forEach { (_, observer) ->
                observer.onSpeechEventTrack(eventName, params)
            }
        }
    }

    override fun onQueryRecognize(
        sessionId: String?,
        dialogId: String?,
        query: String?,
        isFinal: Boolean,
        isFromPostImageForLinkImgId: Boolean?,
        instructionJson: String?,
        streamId: String?
    ) {
        updateDialogId(dialogId)
        MainScope().launch {
            chatObserverMaps.forEach { (_, observer) ->
                observer.onQueryRecognize(
                    sessionId,
                    dialogId,
                    query,
                    isFinal,
                    isFromPostImageForLinkImgId,
                    instructionJson,
                    streamId
                )
            }
        }
    }

    override fun onImageQAContent(
        dialogId: String?,
        sessionId: String?,
        imgInstruction: Instruction<*>?
    ) {
        MainScope().launch {
            chatObserverMaps.forEach { (_, observer) ->
                observer.onImageQAContent(dialogId, sessionId, imgInstruction)
            }
        }
    }

    override fun onParkingCard(
        dialogId: String?,
        sessionId: String?,
        title: String,
        subTitle: String,
        url: String,
        instructionJson: String?
    ) {
        MainScope().launch {
            chatObserverMaps.forEach { (_, observer) ->
                observer.onParkingCard(dialogId, sessionId, title, subTitle, url, instructionJson)
            }
        }
    }

    override fun onImageQuery(dialogId: String?, requestId: String, instructionJson: String?) {
        MainScope().launch {
            chatObserverMaps.forEach { (_, observer) ->
                observer.onImageQuery(dialogId, requestId)
            }
        }
    }

    override fun onTextResponseSynthesizer(
        sessionId: String?,
        dialogId: String?,
        result: String?,
        isFinal: Boolean,
        instructionJson: String?,
        streamId: String?
    ) {
        MainScope().launch {
            val transDialogId = imageEventProcess.imageQueryDialogId(dialogId)
            chatObserverMaps.forEach { (_, observer) ->
                observer.onTextResponseSynthesizer(
                    sessionId = sessionId,
                    dialogId = transDialogId,
                    result = result,
                    isFinal = isFinal,
                    streamId = streamId,
                    instructionJson = instructionJson
                )
            }
        }
    }

    override fun onAlipayTextResponseSynthesizer(
        sessionId: String?,
        dialogId: String?,
        result: String?,
        isFinal: Boolean,
        instructionJson: String?,
        streamId: String?
    ) {
        MainScope().launch {
            val transDialogId = imageEventProcess.imageQueryDialogId(dialogId)
            chatObserverMaps.forEach { (_, observer) ->
                observer.onAlipayTextResponseSynthesizer(
                    sessionId = sessionId,
                    dialogId = transDialogId,
                    result = result,
                    isFinal = isFinal,
                    streamId = streamId,
                    instructionJson = instructionJson
                )
            }
        }
    }

    override fun onResponseBottomExplain(
        sessionId: String?,
        dialogId: String?,
        bottomExplain: String?,
        instructionJson: String?
    ) {
        MainScope().launch {
            val transDialogId = imageEventProcess.imageQueryDialogId(dialogId)
            chatObserverMaps.forEach { (_, observer) ->
                observer.onResponseBottomExplain(
                    sessionId,
                    transDialogId,
                    bottomExplain,
                    instructionJson
                )
            }
        }
    }

    override fun onDialogIllegal(sessionId: String?, dialogId: String?) {
        Timber.d("onDialogIllegal:$sessionId,$dialogId")
        MainScope().launch {
            chatObserverMaps.forEach { (_, observer) ->
                observer.onDialogIllegal(sessionId, dialogId)
            }
        }
    }

    override fun addToChatHistory(
        sessionId: String?,
        dialogId: String,
        content: String,
        type: Int,
        timestamp: Long
    ) {
        Timber.d("addToChatHistory:$sessionId,$dialogId,$content,$type")
        MainScope().launch {
            chatObserverMaps.forEach { (_, observer) ->
                observer.addToChatHistory(sessionId, dialogId, content, type, timestamp)
            }
        }
    }

    override fun onStreamDialogEnter(
        sessionId: String?,
        dialogId: String?,
        streamType: String,
        streamId: String,
        cardConfig: Triple<String, String, String>?,
        pageConfig: Triple<String, String, String?>?,
        instructionJson: String?
    ) {
        MainScope().launch {
            if (isStreamDialogSupport()) {
                chatObserverMaps.forEach { (_, observer) ->
                    observer.onStreamDialogEnter(
                        sessionId = sessionId,
                        dialogId = dialogId,
                        streamType = streamType,
                        streamId = streamId,
                        cardConfig = cardConfig,
                        pageConfig = pageConfig,
                        instructionJson = instructionJson
                    )
                }
            }
        }
    }

    override fun onUtteranceStart(utteranceId: String?, isUrl: Boolean, isLocalCorpus: Boolean) {
        Timber.tag(TAG).i("onUtteranceStart $utteranceId")
        MainScope().launch {
            EngineStateMachine.onTtsStateChange(TtsState.TTS_PLAYING)
            chatObserverMaps.forEach { (_, observer) ->
                observer.onUtteranceStart(utteranceId, isUrl, isLocalCorpus)
            }
        }
    }

    override fun onUtteranceDone(utteranceId: String?, isUrl: Boolean, isLocalCorpus: Boolean) {
        Timber.tag(TAG).i("onUtteranceDone $utteranceId")
        MainScope().launch {
            EngineStateMachine.onTtsStateChange(TtsState.TTS_IDLE)
            chatObserverMaps.forEach { (_, observer) ->
                observer.onUtteranceDone(utteranceId, isUrl, isLocalCorpus)
            }
        }
    }

    override fun onUtteranceStop(utteranceId: String?, isUrl: Boolean) {
        Timber.tag(TAG).i("onUtteranceStop $utteranceId")
        MainScope().launch {
            EngineStateMachine.onTtsStateChange(TtsState.TTS_IDLE)
            chatObserverMaps.forEach { (_, observer) ->
                observer.onUtteranceStop(utteranceId, isUrl)
            }
        }
    }

    override fun pongNum(id: String) {
        Timber.tag(TAG).d("pongNum  $curPingId ,getPongId==$id")
        if (id != curPingId) {
            Timber.tag(TAG).d("pongNum not same ID")
            return
        }
        getPongNum++
    }

    @SuppressLint("ImplicitSamInstance")
    private fun tipsNetWork() {
        closePingPong("tipsNetWork")
        cancelTimer("tipsWeakNetWork", true)
        stopTts(
            stopOptions = SpeechEngineProxyImpl.Companion.TtsStopOptions(
                calledFrom = "tipsNetWork",
                needResumeMediaPlayer = true,
                needStopMediaPlayer = null,
                stopReason = null
            )
        )
        playTipSound(resourceId = R.raw.network_error) {
            finishSession(reason = "tipsWeakNetWork")
            Timber.tag(TAG).d("tipsNetWork play complete ")
        }
    }

    fun judgeThirdTimeWeakNetwork(): Boolean {
        val isWeakNetwork = (pingNum == THE_THIRD_TIME) && (getPongNum <= FIRST_TIME)
        Timber.tag(TAG).d("judgeThirdTimeWeakNetwork $isWeakNetwork ")
        return isWeakNetwork
    }

    /**
     * 发送ping消息
     */
    private fun sendPing() {
        pingTimer?.cancel()
        pingTimer = Timer()
        pingNum = initialValue
        getPongNum = initialValue
        val task = object : TimerTask() {
            override fun run() {
                Timber.tag(TAG).d("pingNum=== $pingNum ======= getPongNum==$getPongNum")
                // 第4秒
                if (pingNum == SECOND_TIME) {
                    if (getPongNum == initialValue) {
                        tipsNetWork()
                    } else if (getPongNum == SECOND_TIME) {
                        closePingPong("getPongNum == secondTime")
                        return
                    }
                }

                // 无须拦截取消 首轮6S超时后会取消timer 不会发第四次
                pingTimer?.let {
                    pingNum++
                    if (pingNum == THE_THIRD_TIME) {
                        return
                    }
                    val clientPing = Sys.ClientPing()
                    val clientPingEvent = APIUtils.buildEvent(clientPing)
                    curPingId = clientPingEvent.id
                    Timber.tag(TAG).d("sendPing $curPingId")
                    getAiEngine()?.let { engine ->
                        engine.postEvent(clientPingEvent)
                    }
                }
            }
        }
        pingTimer?.schedule(task, 0, TIMES)
    }

    fun getAuthorization(): String {
        Timber.d("getAuthorization called")
        return getAiEngine()?.authorization ?: ""
    }

    override fun closePingPong(reason: String) {
        Timber.tag(TAG).d("closePingPong $reason")
        pingTimer?.cancel()
    }

    @androidx.annotation.OptIn(UnstableApi::class)
    fun getDeFaultExoPlayer(): ExoPlayer {
        return ExoPlayer.Builder(LibBaseApplication.instance.applicationContext)
            .setLoadControl(
                DefaultLoadControl.Builder()
                    .setBufferDurationsMs(
                        MIN_BUFFER_TIMES, // 最小缓冲时间
                        MAX_BUFFER_TIMES, // 最大缓冲时间
                        MIN_BUFFER_TIMES, // 播放缓冲时间
                        MIN_BUFFER_TIMES // 重新缓冲时间
                    )
                    .setPrioritizeTimeOverSizeThresholds(true)
                    .build()
            )
            .setWakeMode(androidx.media3.common.C.WAKE_MODE_NETWORK)
            .build().apply {
                setSeekParameters(androidx.media3.exoplayer.SeekParameters.CLOSEST_SYNC)
            }
    }

    companion object {
        private const val TAG = "AiSpeechEngine"

        const val IMAGE_FORMAT = "PNG"

        const val IMAGE_WIDTH = 600
        const val IMAGE_HEIGHT = 600
        private const val TIMES = 2000L
        private const val FIRST_TIME = 1
        private const val SECOND_TIME = 2
        private const val THE_THIRD_TIME = 3
        private const val MIN_BUFFER_TIMES = 100
        private const val MAX_BUFFER_TIMES = 300

        // 是否调试模式.
        private const val IS_DEBUG = true

        val INSTANCE: AiSpeechEngine by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            AiSpeechEngine()
        }
    }

    override fun offlineCloudStop(startTime: Long) {
        Timber.tag(TAG).d("offlineCloudStop $startTime")
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.offlineCloudStop(startTime)
            }
        }
    }

    override fun scanPay(sessionId: String?, dialogId: String, asrResult: String) {
        Timber.tag(TAG).d("scanPay $alipayObserverMaps")
        changeAlipayStatus(true, "scanPay")
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.scanPay(sessionId, dialogId, asrResult)
            }
        }
    }

    override fun scanPayWithDemoMode(sessionId: String?, dialogId: String, asrResult: String) {
        Timber.tag(TAG).d("scanPayWithDemoMode")
        changeAlipayStatus(true, "scanPayWithDemoMode")
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.scanPayWithDemoMode(sessionId, dialogId, asrResult)
            }
        }
    }

    override fun updateDialogId(dialogId: String?) {
        Timber.tag(TAG).d("updateDialogId")
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.updateDialogId(dialogId)
            }
        }
    }

    override fun alipayExi(reason: String?) {
        Timber.tag(TAG).d("alipayExi $reason")
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.alipayExi(reason)
            }
        }
        changeAlipayStatus(false, "alipayExi")
    }

    override fun alipayInterrupt(reason: String?) {
        Timber.tag(TAG).d("alipayInterrupt $reason")
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.alipayInterrupt(reason)
            }
        }
        changeAlipayStatus(false, "alipayInterrupt")
    }

    override fun sendVoiceData(bytes: ByteArray) {
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.sendVoiceData(bytes)
            }
        }
    }

    override fun clearVoiceData() {
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.clearVoiceData()
            }
        }
    }

    override fun startFirstVoice() {
        MainScope().launch {
            alipayObserverMaps.forEach { (_, observer) ->
                observer.startFirstVoice()
            }
        }
    }

    fun getAlipayStatus(): Boolean {
        Timber.tag(TAG).d("getAlipayStatus:$isAlipayStatus")
        return isAlipayStatus
    }

    fun enterAlipayDemoMode() {
        Timber.tag(TAG).d("enterAlipayDemoMode")
        isAlipayDemoMode = true
    }

    fun exitAlipayDemoMode() {
        Timber.tag(TAG).d("exitAlipayDemoMode")
        isAlipayDemoMode = false
    }

    fun isAlipayDemoMode(): Boolean {
        Timber.tag(TAG).d("isAlipayDemoMode $isAlipayDemoMode")
        return isAlipayDemoMode
    }

    fun alipayErrorEvent(code: String?, message: String??) {
        Timber.tag(TAG).d("alipayErrorEvent $code,$message")
        changeAlipayStatus(false, "alipayErrorEvent $code,$message")
    }

    private fun changeAlipayStatus(isPaying: Boolean, reason: String?) {
        Timber.tag(TAG).d("changeAlipayStatus $isPaying,$reason")
        isAlipayStatus = isPaying
    }

    override fun scanPayResult(code: Int, msg: String?) {
        Timber.tag(TAG).d("scanPayResult:$code $msg")
        MainScope().launch {
            alipayResultsObserverMaps.forEach { (_, observer) ->
                observer.scanPayResult(code, msg)
            }
        }
    }

    override fun onPayResult(code: String?, msg: String?) {
        Timber.tag(TAG).d("onPayResult:$code $msg")
        MainScope().launch {
            alipayResultsObserverMaps.forEach { (_, observer) ->
                observer.onPayResult(code, msg)
            }
        }
    }
}
