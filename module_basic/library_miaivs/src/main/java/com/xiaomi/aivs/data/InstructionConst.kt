package com.xiaomi.aivs.data

object InstructionConst {

    object NameSpace {

        // 语音识别.
        const val SPEECH_RECOGNIZER = "SpeechRecognizer"

        // 语音合成.
        const val SPEECH_SYNTHESIZER = "SpeechSynthesizer"

        // 指令执行.
        const val EXECUTION = "Execution"

        // 多模态.
        const val MULTI_MODAL = "MultiModal"

        // 播放.
        const val AUDIO_PLAYER = "AudioPlayer"
        const val AUDIO_SELECTOR = "Selector"

        // 播控.
        const val PLAY_CONTROLLER = "PlaybackController"

        const val DIALOG = "Dialog"
    }

    object EventName {

        // 识别结果.
        const val RECOGNIZE_RESULT = "RecognizeResult"

        // 停止识别.
        const val STOP_CAPTURE = "StopCapture"

        // TTS播报.
        const val TTS_SPEAK = "Speak"

        // 完成TTS推流.
        const val FINISH_SPEAK_STREAM = "FinishSpeakStream"
    }

    object SysExceptionCode {

        // 小爱正常退出无TTS播报.
        const val EXIT_WITH_NO_TOAST = 0

        // 眼镜和app连接弱或未连接.
        const val EXIT_WITH_DEVICE_DISCONNECTED = 2

        // 媒体音量为0.
        const val EXIT_WITH_MEDIA_VOLUME_ZERO = 3

        // 佩戴取消.
        const val EXIT_WITH_WEAR_CANCEL = 4

        // A2DP和HFP同时断开.
        const val EXIT_WITH_BLUETOOTH_PROFILES_DISCONNECTED = 5

        // 小爱被打断.
        const val EXIT_WITH_VOICE_INTERRUPT = 6
    }
}
