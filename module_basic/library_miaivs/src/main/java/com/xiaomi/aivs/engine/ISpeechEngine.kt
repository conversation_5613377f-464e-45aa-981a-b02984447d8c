@file:Suppress("LongParameterList")

package com.xiaomi.aivs.engine

import androidx.lifecycle.Lifecycle
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.DialogState
import com.xiaomi.aivs.data.EngineState
import com.xiaomi.aivs.data.StreamType
import com.xiaomi.aivs.engine.helper.FONT
import com.xiaomi.aivs.engine.helper.ImageFileHandler
import com.xiaomi.aivs.engine.helper.MusicSource
import com.xiaomi.aivs.engine.listener.IAlipayListener
import com.xiaomi.aivs.engine.listener.IAlipayResultListener
import com.xiaomi.aivs.engine.listener.ISpeechChatListener
import com.xiaomi.aivs.engine.listener.ISpeechEngineListener
import com.xiaomi.aivs.engine.proxy.ISpeechEngineProxy

@Suppress("TooManyFunctions")
interface ISpeechEngine : ISpeechEngineProxy {

    /**
     * 唤醒的ID和云端关联.
     */
    fun onReceiveRequestId(requestId: String, transactionId: String)

    /**
     * 发送唤醒音频首包.
     */
    fun postWakeupBegin(bufferSize: Int)

    /**
     * 发送唤醒音频数据.
     */
    fun postWakeupData(bytes: ByteArray?, offset: Int, length: Int)

    /**
     * 发送唤醒音频结束.
     */
    fun postWakeupEnd()

    /**
     * 开始上传语音数据event.
     * @param isDuplexMode 是否全双工,默认为true.
     */
    fun postSpeechBegin(isDuplexMode: Boolean = true): String

    /**
     * 结束上传语音数据event.
     * @param requestId 上传语音开始的eventId,配对使用.
     * @param isDuplexMode 是否全双工,默认为true.
     */
    fun postSpeechEnd(requestId: String, isDuplexMode: Boolean = true)

    /**
     * 收到多模态图片上传事件.
     * @param transactionId sessionId
     * @param dialogId 对话ID.
     */
    fun onUploadMultiModalEvent(
        transactionId: String?,
        dialogId: String,
        payload: String? = null,
        isPush: Boolean = false
    )

    /**
     * 开始上传图片数据event.
     * @param totalSize 图片总大小.
     * @param chunkSize 图片上传需要分片,此为每包的大小.
     */
    fun postImageBegin(
        totalSize: Int,
        chunkSize: Int,
        type: ImageFileHandler.ImageType = ImageFileHandler.ImageType.CROPPED,
        requestId: String
    ): String

    /**
     * 上传图片数据.
     * @param requestId dialogId,发送图片前
     * @param format 图片Format,默认为PNG.
     * @param size 图片尺寸[宽,高].
     * @param bytes 图片数据.
     */
    fun postImageData(
        requestId: String,
        bytes: ByteArray?,
        format: String = AiSpeechEngine.IMAGE_FORMAT,
        size: Pair<Int, Int> = AiSpeechEngine.IMAGE_WIDTH to AiSpeechEngine.IMAGE_HEIGHT,
        type: ImageFileHandler.ImageType = ImageFileHandler.ImageType.CROPPED,
        postEnd: (() -> Unit)? = null
    )

    /**
     * 结束上传图片数据event.
     * @param imageRequestId 上传图片开始的eventId,配对使用.
     * @param dialogId 对话ID.
     */
    fun postImageEnd(imageRequestId: String, dialogId: String?)

    /**
     * 发送图片的OCR数据.
     * @param totalSize 总长度.
     * @param data ocr数据.
     */
    fun postImageOcrData(totalSize: Int, data: ByteArray?)

    /**
     * 收到图片上传的事件.
     * @param dialogId 需要和ImageStart eventID 一致.
     * @param imageId 发送图片返回的id.
     */
    fun onImageFileId(dialogId: String?, imageId: String?)

    /**
     * 发送Nlp事件,比如问"今天天气怎么样?"
     */
    fun postNlpEvent(query: String)

    /**
     * 进入Standby.
     */
    fun enterStandby(streamId: String?)

    /**
     * 退出standby.
     */
    fun exitStandby(reason: String? = "")

    /**
     * 进入支付流程.
     */
    fun enterAlipay(streamId: String?)

    /**
     * 退出支付流程.
     */
    fun exitAlipay(reason: String? = "")

    /**
     * 进入语音反馈状态..
     */
    fun enterVoiceFeedBack(streamId: String? = "")

    /**
     * 结束连续会话.
     * @param requestId 全双工requestId,为空,取内部缓存.
     * @param byCloud 是否云端主动结束.
     * @param reason 缘由
     * @param playSound 是否播放退出提示音.
     * @param isNeedAudioResume 是否需要续播长音频
     */
    fun finishSession(
        requestId: String? = null,
        byCloud: Boolean = false,
        reason: String? = "",
        playSound: Boolean = true,
        isNeedAudioResume: Boolean? = false
    )

    /**
     * 当前引擎的状态.
     */
    fun engineState(): @EngineState Int

    /**
     * 当前对话的状态.
     */
    fun dialogState(): @DialogState Int

    /**
     * 当前TTS的状态.
     */
    fun ttsState(): Int

    /**
     * Stream dialog类型(Standby/RolePlay/Agent).
     */
    fun streamDialogType(): @StreamType String

    /**
     * 获取TTS的音色.
     */
    fun getTtsFont(): @FONT Int

    /**
     * 设置TTS的音色.
     * @param font 音色角色.
     */
    fun setTtsFont(@FONT font: Int)

    /**
     * TTS支持的音色列表.
     * 详细内容见:@FONT
     */
    fun fontList(): List<@FONT Int>

    /**
     * 获取连续对话的超时时间(单位:s).
     */
    fun fullDuplexTimeout(): Long

    /**
     * 设置连续对话的超时时间(单位:s).
     */
    fun setFullDuplexTimeout(second: Long)

    /**
     * 当前设置的音乐源.
     */
    fun getMusicSource(): @MusicSource Int

    /**
     * 设置音乐源.
     */
    fun setMusicSource(@MusicSource source: Int)

    /**
     * 当前设置的电台音乐源.
     */
    fun getRadioStationSource(): Int

    /**
     * 设置电台音乐源.
     */
    fun setRadioStationSource(source: Int)

    /**
     * 支持的电台音乐源列表.
     * @return 电台音乐源list<[key,pkName]>.
     */
    fun radioStationSources(): List<Pair<Int, String>>

    /**
     * 支持的音乐源列表.
     * @return 音乐源list<[key,pkName]>.
     */
    fun musicSources(): List<Pair<@MusicSource Int, String>>

    /**
     * 注册Engine监听.
     * @param key 注册监听的key,作为关键字,建议使用类名.
     * @param lifecycle 生命周期回调,自行注销.如果为null,则自行注销.
     * @param listener:Engine监听回调.
     */
    fun addEngineObserver(
        key: String,
        lifecycle: Lifecycle? = null,
        listener: ISpeechEngineListener
    )

    /**
     * 主动移除Engine监听.
     */
    fun removeEngineObserver(key: String)

    /**
     * 注册Chat数据监听.
     * @param key 注册监听的key,作为关键字,建议使用类名.
     * @param lifecycle 生命周期回调,自行注销.如果为null,则自行注销.
     * @param listener:Chat数据回调.
     */
    fun addChatDataObserver(
        key: String,
        lifecycle: Lifecycle? = null,
        listener: ISpeechChatListener
    )

    /**
     * 主动移除Chat数据监听.
     */
    fun removeChatDataObserver(key: String)

/**
     * 注册zfb监听*
     */
    fun addAlipayObserver(
        key: String,
        lifecycle: Lifecycle? = null,
        listener: IAlipayListener
    )

    /**
     * 主动移除数据监听.
     */
    fun removeAlipayObserver(key: String)

    /**
     * 注册支付宝结果监听*
     */
    fun addAlipayResultsObserver(
        key: String,
        lifecycle: Lifecycle? = null,
        listener: IAlipayResultListener
    )

    /**
     * 主动移除数据监听.
     */
    fun removeAlipayResultsObserver(key: String)

    /**
     * 弱网环境下接收pong次数
     */
    fun pongNum(id: String)

    /**
     * 关闭PingPong
     */
    fun closePingPong(reason: String)
}
