@file:Suppress("TooGenericExceptionCaught", "EmptyFunctionBlock")

package com.xiaomi.aivs.engine.helper

import android.Manifest
import android.Manifest.permission.ACCESS_BACKGROUND_LOCATION
import android.Manifest.permission.ACCESS_FINE_LOCATION
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.os.CancellationSignal
import androidx.annotation.RequiresApi
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

class LocationHelper {

    private var locationManager: LocationManager? = null
    private var locationJob: Job? = null
    private var lastLocation: Location? = null

    fun getLocation(
        context: Context,
        callback: (location: Location?) -> Unit
    ) {
        if (ContextCompat.checkSelfPermission(
                context,
                ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            Timber.w("缺少定位权限")
            callback.invoke(null)
            return
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android11 及以上使用新API
            getLocationWithTimeout(context, callback)
        } else {
            // Android10 以下使用旧API
            getLocationLegacy(context, callback)
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    @RequiresPermission(anyOf = [ACCESS_FINE_LOCATION])
    private fun getLocationWithTimeout(
        context: Context,
        callback: (location: Location?) -> Unit
    ) {
        Timber.d("getLocationWithTimeout")
        locationJob?.cancel()
        locationJob = MainScope().launch {
            withContext(Dispatchers.IO) {
                getLocationImpl(context, callback)
            }
            delay(LOCATION_TIME_OUT)
            callback.invoke(null)
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    @RequiresPermission(anyOf = [ACCESS_FINE_LOCATION])
    private fun getLocationImpl(
        context: Context,
        callback: (location: Location?) -> Unit
    ) {
        if (locationManager == null) {
            locationManager =
                context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        }
        locationManager?.getCurrentLocation(
            LOCATION_PROVIDER,
            CancellationSignal(),
            context.mainExecutor
        ) { location ->
            Timber.d("getCurrentLocation:$location")
            locationJob?.cancel()
            location?.let {
                lastLocation = it
                callback.invoke(it)
            } ?: run {
                callback.invoke(null)
            }
        }
    }

    @RequiresPermission(allOf = [ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private fun getLocationLegacy(
        context: Context,
        callback: (location: Location?) -> Unit
    ) {
        if (locationManager == null) {
            locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        }

        try {
            val location = locationManager?.getLastKnownLocation(LOCATION_PROVIDER)
            if (location != null) {
                lastLocation = location
                callback.invoke(location)
            } else {
                // 如果没有最后位置，请求位置更新
                locationManager?.requestLocationUpdates(
                    LOCATION_PROVIDER,
                    0L,
                    0f,
                    object : LocationListener {
                        override fun onLocationChanged(location: Location) {
                            lastLocation = location
                            callback.invoke(location)
                            locationManager?.removeUpdates(this)
                        }

                        @Deprecated("Deprecated in Java")
                        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {}
                        override fun onProviderEnabled(provider: String) {}
                        override fun onProviderDisabled(provider: String) {
                            callback.invoke(null)
                            locationManager?.removeUpdates(this)
                        }
                    }
                )
            }
        } catch (e: Exception) {
            Timber.e(e, "获取位置失败")
            callback.invoke(null)
        }
    }

    fun requestLocationUpdate(
        context: Context,
        minTimeMs: Long,
        minDistanceM: Float,
        listener: LocationListener,
        callback: (location: Location) -> Unit
    ) {
        if (ActivityCompat.checkSelfPermission(
                context,
                ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
        ) {
            Timber.w("requestLocationUpdates not has location permission.")
            return
        }
        Timber.i("requestLocationUpdates lastLocation:$lastLocation")
        kotlin.runCatching {
            if (lastLocation == null) {
                getLocation(context) { location ->
                    location?.let { callback.invoke(it) }
                }
            }
            locationManager?.requestLocationUpdates(
                LOCATION_PROVIDER,
                minTimeMs,
                minDistanceM,
                listener
            )
        }.getOrElse {
            Timber.e(it, "requestLocationUpdates error.")
        }
    }

    fun removeUpdates(listener: LocationListener) {
        Timber.i("removeUpdates")
        locationManager?.removeUpdates(listener)
    }

    fun backLocationAllow(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
            context.checkSelfPermission(ACCESS_BACKGROUND_LOCATION) == PackageManager.PERMISSION_GRANTED
        } else {
            context.checkSelfPermission(ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
        }
    }

    companion object {
        private const val LOCATION_TIME_OUT = 1200L
        private const val LOCATION_PROVIDER = LocationManager.NETWORK_PROVIDER
    }
}
