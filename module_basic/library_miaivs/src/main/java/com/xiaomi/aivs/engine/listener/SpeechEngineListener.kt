package com.xiaomi.aivs.engine.listener

import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.aivs.data.DialogState
import com.xiaomi.aivs.data.EngineState
import com.xiaomi.aivs.data.StreamType
import com.xiaomi.aivs.data.TtsState
import com.xiaomi.aivs.engine.event.DeviceEvent
import com.xiaomi.aivs.engine.state.EngineStateMachine
import com.xiaomi.aivs.player.UtteranceListener
import com.xiaomi.aivs.track.EventTrackKv
import com.xiaomi.aivs.wearable.IWearableFunc

interface ISpeechEngineListener {

    /**
     * 收到-连接状态变化.
     */
    fun onConnectState(isConnected: Boolean) {}

    /**
     * 收到-对话状态变化.
     */
    fun onDialogState(@DialogState state: Int)

    /**
     * 发送指令->设备.
     * @param transactionId 会话ID.
     * @param requestId 对话ID(dialogId).
     * @param event 设备事件.
     */
    fun sendEventToDevice(
        transactionId: String? = null,
        requestId: String? = null,
        payload: String? = null,
        @DeviceEvent event: Int
    )

    /**
     * 发送指令->设备.
     * @param transactionId 会话ID.
     * @param requestId 对话ID(dialogId).
     * @param event 设备事件.
     */
    fun sendEventToDevice(
        transactionId: String? = null,
        requestId: String? = null,
        @DeviceEvent event: Int
    )

    /**
     * 同步状态->设备.
     * @param dialogState 对话状态.
     * @param engineState 引擎状态.
     */
    fun syncStateToDevice(
        @DialogState dialogState: Int,
        @EngineState engineState: Int,
        @TtsState ttsState: Int
    )

    /**
     * 小爱埋点上报.
     * @param eventName 事件名称.
     * @param params 埋点数据.
     */
    fun onSpeechEventTrack(
        eventName: String,
        params: Map<String, Any>?
    )
}

interface IAlipayListener {
    /**
     * 收到支付意图发送语音数据
     */
    fun offlineCloudStop(startTime: Long)

    /**
     * 根据小爱的RecognizeResult指令更新dialogId
     */
    fun updateDialogId(dialogId: String?)

    /**
     * 通知扫码
     */
    fun scanPay(sessionId: String?, dialogId: String, asrResult: String)

    /**
     * 通知m模拟扫码
     */
    fun scanPayWithDemoMode(sessionId: String?, dialogId: String, asrResult: String)

    /**
     * 退出支付流程
     */
    fun alipayExi(reason: String?)

    /**
     * 退出支付流程
     */
    fun alipayInterrupt(reason: String?)

    /**
     * 持续发送后续语音数据
     */
    fun sendVoiceData(bytes: ByteArray)

    fun startFirstVoice()

    fun clearVoiceData()
}

interface IAlipayResultListener {
    /**
     * 扫码结果回调
     */
    fun scanPayResult(code: Int, msg: String?)

    /**
     * 支付结果回调
     */
    fun onPayResult(code: String?, msg: String?)
}

interface ISpeechChatListener : UtteranceListener {
    /**
     * 语音Query识别.
     * @param sessionId 会话ID.
     * @param dialogId 对话ID.
     * @param query 会话提问识别内容.
     * @param isFinal 是否最终识别结果.
     * @param streamId StreamDialog的ID标识,区分Standby、口语老师等智能体.
     */
    @Suppress("LongParameterList")
    fun onQueryRecognize(
        sessionId: String?,
        dialogId: String?,
        query: String?,
        isFinal: Boolean,
        isFromPostImageForLinkImgId: Boolean? = false,
        instructionJson: String?,
        streamId: String? = EngineStateMachine.streamId()
    )

    /**
     * 图片识别.
     * @param dialogId Query对话ID.
     * @param requestId 图片的上传EventId.
     */
    fun onImageQuery(
        dialogId: String?,
        requestId: String,
        instructionJson: String? = null
    ) {
    }

    /**
     * ImageQAContent指令接收.
     * @param dialogId Query对话ID.
     * @param sessionId 会话ID.
     * @param imgInstruction ImageQAContent指令.
     */
    fun onImageQAContent(
        dialogId: String?,
        sessionId: String?,
        imgInstruction: Instruction<*>?
    ) {
    }

    @Suppress("LongParameterList")
    fun onParkingCard(
        dialogId: String?,
        sessionId: String?,
        title: String,
        subTitle: String,
        url: String,
        instructionJson: String?
    ) {
    }

    /**
     * 文本对话响应生成.
     * @param sessionId 会话ID.
     * @param dialogId 对话ID.
     * @param result 响应内容.
     * @param isFinal 是否最终识别结果.
     * @param streamId StreamDialog的ID标识,区分Standby、口语老师等智能体.
     */
    @Suppress("LongParameterList")
    fun onTextResponseSynthesizer(
        sessionId: String?,
        dialogId: String?,
        result: String?,
        isFinal: Boolean,
        instructionJson: String? = null,
        streamId: String? = EngineStateMachine.streamId()
    )

    /**
     * 支付宝文本对话响应生成.
     * @param sessionId 会话ID.
     * @param dialogId 对话ID.
     * @param result 响应内容.
     * @param isFinal 是否最终识别结果.
     * @param streamId StreamDialog的ID标识,区分Standby、口语老师等智能体.
     */
    @Suppress("LongParameterList")
    fun onAlipayTextResponseSynthesizer(
        sessionId: String?,
        dialogId: String?,
        result: String?,
        isFinal: Boolean,
        instructionJson: String? = null,
        streamId: String? = EngineStateMachine.streamId()
    )

    /**
     * 云端下发的底部说明.
     * @param sessionId 会话ID.
     * @param dialogId 对话ID.
     * @param bottomExplain 底部说明(以上由AI大模型生成，可能包含不准确的信息).
     */
    fun onResponseBottomExplain(
        sessionId: String?,
        dialogId: String?,
        bottomExplain: String?,
        instructionJson: String? = null
    ) {
    }

    /**
     * 触发进入StreamDialog.
     * @param sessionId 会话ID.
     * @param dialogId 对话ID.
     * @param streamType 类型.
     * @param streamId 标识Standby/智能体.
     * @param cardConfig 一级卡片<标题,描述,icon地址>
     * @param pageConfig 页面<标题,avatar地址,欢迎词>
     */
    @Suppress("LongParameterList")
    fun onStreamDialogEnter(
        sessionId: String?,
        dialogId: String?,
        @StreamType streamType: String,
        streamId: String,
        cardConfig: Triple<String, String, String>?,
        pageConfig: Triple<String, String, String?>?,
        instructionJson: String? = null
    ) {
    }

    /**
     * 触发黄反指令Dialog.IllegalContent.
     * @param sessionId 会话ID.
     * @param dialogId 对话ID.
     */
    fun onDialogIllegal(
        sessionId: String?,
        dialogId: String?
    ) {
    }

    /**@param:dialogId 本次会话的dialogId，这个dialogId 和 首次出发支付请求的dialogId保持一直
     * @param content 需要展示在对话记录里的内容
     * @param type 0 是 答， type 1 是 问
     * @param timestamp 时间戳*/
    fun addToChatHistory(
        sessionId: String?,
        dialogId: String,
        content: String,
        type: Int,
        timestamp: Long = System.currentTimeMillis()
    )
}

interface IExpandAbility {
    /**
     *关联iWearableFunc 眼镜相关实现.
     */
    fun addGlassFunc(iWearableFunc: IWearableFunc)
}

interface IEventTrack {

    fun onEventTrackTime(dialogId: String? = null, key: EventTrackKv, forceUpdate: Boolean = false)

    fun onEventIncrease(dialogId: String? = null, key: EventTrackKv)

    fun onEventTrack(dialogId: String? = null, key: EventTrackKv, value: Any)

    fun onEventError(
        dialogId: String? = null,
        key: EventTrackKv,
        type: String,
        errorCode: Int,
        errorMsg: String
    )
}
