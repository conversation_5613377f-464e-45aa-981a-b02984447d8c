package com.xiaomi.aivs.engine.policy

import com.xiaomi.ai.api.AIApiConstants
import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.R
import com.xiaomi.aivs.data.DialogNode
import com.xiaomi.aivs.data.TimeOutPolicyNode
import com.xiaomi.aivs.data.TimeOutPolicyNode.Companion.ASR_FINAL
import com.xiaomi.aivs.data.TimeOutPolicyNode.Companion.ASR_PARTIAL
import com.xiaomi.aivs.data.TimeOutPolicyNode.Companion.DIALOG_TIMER
import com.xiaomi.aivs.data.TimeOutPolicyNode.Companion.STREAM_PARTIAL
import com.xiaomi.aivs.engine.policy.timber.TimeoutTimer
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl
import com.xiaomi.aivs.engine.state.EngineStateMachine
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.track.EventTrackKv
import com.xiaomi.aivs.utils.FeedbackAsrUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * 全双工-计时策略.
 * 方案: https://xiaomi.f.mioffice.cn/docx/doxk4FucONw0AxSwj2i0NmEPS2d
 */
class TimeOutPolicyImpl : ITimeOutPolicy, FeedbackAsrUtil.FeedbackActionListener {

    private var supportStreamDialog = true

    private var requestId: String = ""
    private var curDialogId: String? = null
    private var lastAsrText: String? = null
    private var scheduledJob: Job? = null

    // 语音反馈超时.
    private var feedBackTimer: TimeoutTimer? = null

    // 连续对话超时.
    private var dialogTimer: TimeoutTimer? = null

    // 指令超时.
    private var cmdTimer: TimeoutTimer? = null

    // 判定用户是否正在说话.
    private val userInputting = AtomicBoolean(false)

    // 25s后如果用户还在说话也不开启25倒计时
    private val userWaiting = AtomicBoolean(false)

    // 对话轮次.
    private var dialogRounds = AtomicInteger(0)

    // 收到连续拒识指令计数.
    private var rejectContinuousCount = AtomicInteger(0)

    override fun requestId() = requestId

    override fun isFirstDialogRound(): Boolean = dialogRounds.get() < 1

    /**
     * 连续对话超时时间.
     * ps:
     * 1、连续对话超时,默认为10s,用户可以设置.
     * 2、Standby/口语老师 对话超时时间为60s.
     */
    override fun countdownTime() =
        if (EngineStateMachine.isVoiceFeedBack()) {
            VOICE_FEEDBACK_TIME_OUT
        } else if (EngineStateMachine.isStandBy()) {
            STANDBY_TIME_OUT
        } else {
            AiSpeechEngine.INSTANCE.fullDuplexTimeout()
        }

    override fun initTimer(requestId: String, reason: String?) {
        Timber.tag(TAG).d("initTimer:$requestId,$reason")
        this.requestId = requestId
        initFlag()
        EngineStateMachine.onDialogEnter()
        restartTimer(
            "initTimer",
            FIRST_ROUND_COUNTDOWN_TIME,
            isUserInputWait = false,
            isFirstTime = true
        )
    }

    override fun restartTimer(
        reason: String?,
        countdownTime: Long,
        isUserInputWait: Boolean,
        isFirstTime: Boolean
    ) {
        Timber.tag(TAG).d("restartDialogTimer:$reason,$isUserInputWait,$countdownTime,$isFirstTime")
        cancelCmdTimer("restartDialogTimer")
        cancelDialogTimer("restartDialogTimer")
        dialogTimer = TimeoutTimer(DIALOG_TIMER) { onDialogTimerDone(isUserInputWait, isFirstTime) }
        dialogTimer?.restartTimer(reason, countdownTime)
    }

    override fun restartFeedBackTimer(
        reason: String?,
        countdownTime: Long
    ) {
        Timber.tag(TAG).d("restartFeedBackTimer:$reason")
        cancelCmdTimer("restartFeedBackTimer")
        cancelDialogTimer("restartFeedBackTimer")
        feedBackTimer = TimeoutTimer(DIALOG_TIMER) { onFeedBackTimerDone() }
        feedBackTimer?.restartTimer(reason, countdownTime)
    }

    private fun onFeedBackTimerDone() {
        Timber.tag(TAG).d("onFeedBackTimerDone:${userInputting.get()}")

        if (userInputting.get()) {
            restartTimer("FeedBackRecording", VOICE_FEEDBACK_MAX_TIME)
        } else {
            // 播放TTS
            cancelFeedBackTimer()
            onDialogExit(byCloud = false, reason = "onFeedBackTimerDone.")
            AiSpeechEngine.INSTANCE.startTts("没听清你说的，重新反馈一下吧")
        }
    }

    private fun onDialogTimerDone(isUserInputWait: Boolean, isFirstTime: Boolean) {
        Timber.tag(TAG).d(
            "onDialogTimerDone:$isFirstTime,$isUserInputWait," +
                " isVoiceFeedBack(): ${EngineStateMachine.isVoiceFeedBack()}"
        )
        if (EngineStateMachine.isVoiceFeedBack()) {
            onSendTriggered()
        } else {
            // 连续对话超时Timber结束时,启动用户说话等待超时定时器（25s)
            if (userInputting.get() && !isUserInputWait && !userWaiting.get()) {
                restartTimer("UserInputWait", USER_INPUT_WAIT_COUNTDOWN_TIME)
                userWaiting.set(true)
            } else {
                if (isFirstTime && AiSpeechEngine.INSTANCE.judgeThirdTimeWeakNetwork()) {
                    onNetworkError {
                        onDialogExit(
                            byCloud = false,
                            reason = "onCommandTimerDone:judgeThirdTimeWeakNetwork"
                        )
                    }
                } else {
                    onDialogExit(byCloud = false, reason = "onDialogTimerDone.")
                }
            }
        }
    }

    override fun cancelTimer(reason: String?, withCmd: Boolean) {
        Timber.tag(TAG).d("cancelTimer:$reason,$withCmd")
        cancelDialogTimer(reason)
        if (withCmd) {
            cancelCmdTimer(reason)
        }
    }

    override fun cancelFeedBackJobs() {
        cancelFeedBackTimer()
        clearAsrReceived()
    }

    private fun cancelDialogTimer(reason: String?) {
        dialogTimer?.cancelTimer(reason)
    }

    private fun cancelFeedBackTimer() {
        feedBackTimer?.cancelTimer("cancelFeedBackTimer")
    }

    private fun restartCmdTimer(
        reason: String?,
        node: @TimeOutPolicyNode String,
        countdownTime: Long,
        isStreamCmd: Boolean = false
    ) {
        Timber.tag(TAG).d("restartCmdTimer:$reason,$countdownTime,$isStreamCmd")
        cmdTimer?.cancelTimer(reason)
        cmdTimer = TimeoutTimer(node) { onCommandTimerDone(node, reason, isStreamCmd) }
        cmdTimer?.restartTimer(reason, countdownTime)
    }

    private fun onCommandTimerDone(
        node: @TimeOutPolicyNode String,
        reason: String?,
        isStreamCmd: Boolean
    ) {
        Timber.tag(TAG).d("onCommandTimerDone:$node,$isStreamCmd,$reason")
        // 防止播报异常话术的时候,再次收到指令.
        AiSpeechEngine.INSTANCE.interrupt(
            reason = reason,
            stopOptions = SpeechEngineProxyImpl.Companion.TtsStopOptions(
                calledFrom = reason ?: "onCommandTimerDone",
                needResumeMediaPlayer = null,
                needStopMediaPlayer = null,
                stopReason = null
            )
        )
        onNetworkError {
            if (isStreamCmd) {
                restartTimer(reason = "Nlp.FinishStream", countdownTime())
            } else {
                onDialogExit(byCloud = false, reason = "onCommandTimerDone:$reason")
            }
        }
    }

    private fun cancelCmdTimer(reason: String?) {
        cmdTimer?.cancelTimer(reason)
    }

    override fun isStreamDialogSupport(): Boolean {
        return supportStreamDialog
    }

    override fun setStreamDialogSupport(support: Boolean) {
        Timber.tag(TAG).d("setStreamDialogSupport:$support")
        this.supportStreamDialog = support
    }

    override fun onAsrInput(dialogId: String?, input: String?, isFinal: Boolean) {
        Timber.tag(TAG).d("onAsrInput:$dialogId,$input,$isFinal")
        input?.takeIf { it.isNotEmpty() }?.let {
            updateAsrInputState(dialogId, true)
            if (EngineStateMachine.isVoiceFeedBack()) {
                onAsrReceived(input, isFinal)
            }
        }

        if (!EngineStateMachine.isVoiceFeedBack() && !AiSpeechEngine.INSTANCE.getAlipayStatus()) {
            if (isFinal) {
                onAsrFinal(dialogId, input)
            } else {
                onAsrPartial(dialogId, input)
            }
        }
    }

    private fun onAsrReceived(text: String, isFinal: Boolean) {
        if (!isFinal) {
            Timber.tag(TAG).d("onAsrReceived text---------------:$text")
            if (text == lastAsrText) return // 忽略相同文本
            lastAsrText = text
            Timber.tag(TAG).d("onAsrReceived lastAsrText ---------------:$lastAsrText")

            // 取消之前的检测任务
            scheduledJob?.cancel()
            scheduledJob = CoroutineScope(Dispatchers.IO).launch {
                Timber.tag(TAG).d("onAsrReceived scheduledJob runing")
                delay(VOICE_FEEDBACK_VAD_TIME_OUT)
                // 处理ASR 3s超时
                Timber.tag(TAG).d("onAsrReceived 3s超时")
                onSendTriggered()
                return@launch
            }

            Timber.tag(TAG).d("processInput text")
            FeedbackAsrUtil.processInput(text, this)
        }
    }

    override fun onSendTriggered() {
        Timber.d("onSendTriggered isIdle: ${EngineStateMachine.isIdle()}")

        // 保存ASR
        lastAsrText?.let { AiSpeechEngine.INSTANCE.saveFeedBackAsr(it, requestId) }
        clearAsrReceived()

        // 退出会话
        if (!EngineStateMachine.isIdle()) {
            onDialogExit(byCloud = false, reason = "onDialogTimerDone.")
        }

        // 播放TTS
        AiSpeechEngine.INSTANCE.startTts("收到，开始为您上传日志")

        // 发起上传，上传成功和失败也需要播放TTS
        AiSpeechEngine.INSTANCE.sendTaskBroadcast(requestId, "Feed_Back_Voice")
    }

    override fun onCancelTriggered() {
        Timber.d("onCancelTriggered")
        // 退出会话
        if (!EngineStateMachine.isIdle()) {
            onDialogExit(byCloud = false, reason = "onDialogTimerDone.")
        }
        AiSpeechEngine.INSTANCE.startTts("已取消反馈")
        clearAsrReceived()
    }

    private fun clearAsrReceived() {
        lastAsrText = null
        scheduledJob?.cancel()
    }

    override fun onNlpEnter(dialogId: String?, cmd: String?, isStream: Boolean, reCountDown: Boolean) {
        Timber.tag(TAG).d("onNlpEnter:$dialogId,$cmd,$isStream")
        onNewDialogRound()
        updateAsrInputState(dialogId, false)
        if (reCountDown) {
            if (isStream) {
                cancelDialogTimer(reason = cmd)
                restartCmdTimer(
                    reason = cmd,
                    node = STREAM_PARTIAL,
                    countdownTime = STREAM_PARTIAL_COUNTDOWN_TIME
                )
            } else {
                cancelCmdTimer(reason = cmd)
                if (!isTtsSpeaking()) {
                    restartTimer(reason = cmd, countdownTime())
                } else {
                    Timber.tag(TAG).w("nlp enter ignore when tts is speaking!")
                }
            }
        }
    }

    override fun onNlpExit(dialogId: String?, cmd: String?, isStream: Boolean) {
        Timber.tag(TAG).d("onNlpExit:$dialogId,$cmd,$isStream")
        if (isStream) {
            cancelCmdTimer(reason = cmd)
            if (!isTtsSpeaking()) {
                restartTimer(reason = cmd, countdownTime())
            } else {
                Timber.tag(TAG).w("nlp exit ignore when tts is speaking!")
            }
        } else {
            // TODO 目前没有需要处理的逻辑.
        }
    }

    override fun onDialogReject(dialogId: String?) {
        cancelCmdTimer("onDialogReject")
        updateAsrInputState(dialogId, false)
        if (AiSpeechEngine.INSTANCE.isTtsSpeaking()) {
            Timber.tag(TAG).w("tts is speaking,ignore Reject!")
            resetRejectCount("isTtsSpeaking")
            return
        }
        rejectContinuousCount.incrementAndGet()
        Timber.tag(TAG).d("onDialogReject:$dialogId,$rejectContinuousCount")
        if (isFirstDialogRound()) {
            onDialogExit(reason = "首轮收到拒识.")
        } else if (rejectContinuousCount.get() >= MAX_REJECT_COUNT) {
            onDialogExit(reason = "收到连续${rejectContinuousCount}次拒识.")
        } else {
            restartTimer("DialogReject", countdownTime())
        }
    }

    override fun onStreamInstruction(instruction: Instruction<*>?) {
        Timber.tag(TAG).d("onStreamInstruction:${instruction?.header?.fullName}")
        if (AIApiConstants.Dialog.Finish.equals(instruction?.fullName, true)) {
            return
        }
        // SDK偶现时序问题,先规避.
        if (AIApiConstants.SpeechSynthesizer.SpeakStream.equals(instruction?.fullName, true)
        ) {
            return
        }

        restartCmdTimer(
            reason = instruction?.header?.fullName,
            node = STREAM_PARTIAL,
            STREAM_PARTIAL_COUNTDOWN_TIME
        )
    }

    private fun onAsrPartial(dialogId: String?, input: String?) {
        Timber.tag(TAG).i("onAsrPartial:$dialogId,$input")
        if (!input.isNullOrEmpty()) {
            cancelDialogTimer("onAsrPartial:$input")
            restartCmdTimer("onAsrPartial", node = ASR_PARTIAL, ASR_PARTIAL_COUNTDOWN_TIME)
        } else if (cmdTimer?.timerNode() == ASR_PARTIAL) { // 规避先有asr,后变为kong的case.
            val reason = "onAsrPartial Empty"
            cancelCmdTimer(reason = reason)
            restartTimer(reason = reason, countdownTime())
        }
    }

    private fun onAsrFinal(dialogId: String?, input: String?) {
        Timber.tag(TAG).i("onAsrFinal:$dialogId,$input")
        input?.takeIf { it.isNotEmpty() }?.let {
            EngineStateMachine.onDialogNode(DialogNode.ASR_FINAL)

            cancelDialogTimer("onAsrFinal:$input")
            restartCmdTimer("onAsrFinal", node = ASR_FINAL, ASR_FINAL_NLP_COUNTDOWN_TIME)
        }
    }

    /**
     * 判断正在说话的状态变量的更新逻辑.
     * ps:遇到非空ASR设置为true; 收到nlp结果，设置为false
     */
    private fun updateAsrInputState(dialogId: String?, isInput: Boolean) {
        Timber.tag(TAG).i("updateAsrInputState:$dialogId,$isInput")
        if (!isInput && dialogId != curDialogId) { // 规避下一轮为空的asr
            Timber.tag(TAG).w("updateAsrInputState has new input")
            return
        }
        curDialogId = dialogId
        userInputting.set(isInput)
    }

    private fun onNewDialogRound() {
        resetRejectCount("onNewDialogRound")
        dialogRounds.incrementAndGet()
        Timber.tag(TAG).i("onNewDialogRound:${dialogRounds.get()}")
    }

    private fun onDialogExit(byCloud: Boolean = false, reason: String?) {
        Timber.tag(TAG).i("onDialogExit:$requestId,$byCloud,$reason")
        cancelCmdTimer("onDialogExit")
        cancelDialogTimer("onDialogExit")
        AiSpeechEngine.INSTANCE.finishSession(
            requestId = requestId,
            byCloud = byCloud,
            reason = reason,
            isNeedAudioResume = true
        )
        reason?.let {
            EventTrack.onEventTrack(
                dialogId = requestId,
                key = EventTrackKv.STATE_VAD_END_TYPE,
                value = it
            )
        }
    }

    private fun initFlag() {
        Timber.tag(TAG).w("initFlag")
        this.dialogRounds.set(0)
        this.userInputting.set(false)
        this.userWaiting.set(false)
        this.dialogTimer?.cancelTimer("initFlag")
        this.cmdTimer?.cancelTimer("initFlag")
        resetRejectCount("initFlag")
    }

    private fun resetRejectCount(reason: String?) {
        Timber.tag(TAG).w("resetRejectCount:$reason")
        this.rejectContinuousCount.set(0)
    }

    private fun isTtsSpeaking() = AiSpeechEngine.INSTANCE.isTtsSpeaking()

    private fun onNetworkError(onComplete: () -> Unit) {
        Timber.tag(TAG).w("onNetworkError")
        AiSpeechEngine.INSTANCE
            .playTipSound(resourceId = R.raw.network_error) { onComplete.invoke() }
    }

    companion object {
        private const val TAG = "TimeOutPolicy"

        // 首轮超时时间为6s,次轮时间为用户设置.
        private const val FIRST_ROUND_COUNTDOWN_TIME = 6L

        // Asr Partial超时时间为4s
        private const val ASR_PARTIAL_COUNTDOWN_TIME = 4L

        // Asr final 到Nlp超时
        private const val ASR_FINAL_NLP_COUNTDOWN_TIME = 8L

        // 大模型指令间超时
        private const val STREAM_PARTIAL_COUNTDOWN_TIME = 12L

        // Standby的超时时间.
        private const val STANDBY_TIME_OUT = 60L

        // 用户等待说话超时时间.
        private const val USER_INPUT_WAIT_COUNTDOWN_TIME = 25L

        // 最大的连续拒识次数.
        private const val MAX_REJECT_COUNT = 2

        // 语音反馈录音的最长时间.
        private const val VOICE_FEEDBACK_MAX_TIME = 53L

        // 语音反馈录音的超时时间.
        private const val VOICE_FEEDBACK_TIME_OUT = 7L

        // 语音反馈VAD的超时时间.
        private const val VOICE_FEEDBACK_VAD_TIME_OUT = 3000L
    }
}
