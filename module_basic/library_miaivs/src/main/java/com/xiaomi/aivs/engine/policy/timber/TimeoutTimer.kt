package com.xiaomi.aivs.engine.policy.timber

import com.xiaomi.aivs.data.TimeOutPolicyNode
import com.xiaomi.aivs.engine.state.EngineStateMachine
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.atomic.AtomicReference
import kotlin.coroutines.cancellation.CancellationException

class TimeoutTimer(
    @TimeOutPolicyNode private val node: String,
    private val onTimeDone: () -> Unit
) : ITimeOut {
    @Volatile
    private var timerScope: CoroutineScope = createNewScope()

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun createNewScope(): CoroutineScope {
        return CoroutineScope(SupervisorJob() + Dispatchers.IO.limitedParallelism(1))
    }

    private val countdownJob = AtomicReference<Job?>() // 原子更新Job

    override fun isActive(): Boolean = countdownJob.get()?.isActive == true

    override fun restartTimer(reason: String?, countdownTime: Long) {
        Timber.tag(node).d("restartTimer:$reason")
        val oldJob = countdownJob.getAndSet(null)
        if (oldJob?.isActive == true) {
            try {
                oldJob.cancel() // 非阻塞取消
            } catch (e: CancellationException) {
                Timber.tag(node).w("旧Job已取消")
            }
        }
        wakeupTimer(reason, countdownTime)
    }

    private fun wakeupTimer(reason: String?, countdownTime: Long) {
        Timber.tag(node).d("wakeupTimer:$reason,$countdownTime,${EngineStateMachine.isIdle()}")
        if (EngineStateMachine.isIdle()) {
            Timber.tag(node).w("非连续对话中，不启动倒计时.")
            return
        }

        countdownJob.set(
            timerScope.launch {
                Timber.tag(node).d("Timer started: $reason, $countdownTime ms")
                try {
                    delay(countdownTime * TIME_UNIT)
                    if (isActive()) {
                        Timber.tag(node).d("Timer expired, invoking callback")
                        onTimeoutDone()
                    }
                } catch (e: CancellationException) {
                    Timber.tag(node).w("Timer interrupted: $reason")
                    return@launch // 协程被取消后直接返回，不执行回调
                }
            }
        )
    }

    override fun timerNode(): String? = if (isActive()) node else null

    private fun onTimeoutDone() {
        Timber.tag(node).d("onTimeoutDone")
        onTimeDone.invoke()
    }

    override fun cancelTimer(reason: String?) {
        Timber.tag(node).d("cancelTimer started: $reason")
        countdownJob.getAndSet(null)?.takeIf { it.isActive }?.cancel().apply {
            Timber.tag(node).d("cancelTimer done: $reason")
        }
    }

    companion object {
        private const val TIME_UNIT = 1000L
    }
}
