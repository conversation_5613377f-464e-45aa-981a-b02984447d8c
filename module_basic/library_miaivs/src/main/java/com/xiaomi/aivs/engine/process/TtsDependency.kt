package com.xiaomi.aivs.engine.process

import kotlinx.coroutines.Runnable
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

/**
 * TTS依赖处理,有TTS需要先播报,结束后执行下发指令.
 */
object TtsDependency {

    /**
     * 标识本次对话是否有TTS播报(对应SpeechSynthesizer.Speak).
     * 如果有,其他指令需要播报结束后执行.
     */
    private var hasTtsSpeech = false
    private var dialogId: String? = ""
    private val dependencyTaskMap = ConcurrentHashMap<String?, Runnable>()
    private val stopTaskMap = ConcurrentHashMap<String?, Runnable?>()

    fun hasTtsSpeech() = hasTtsSpeech

    fun resetTtsTtsSpeechFlag(reason: String?) {
        Timber.d("resetTtsTtsSpeechFlag:$reason")
        this.hasTtsSpeech = false
    }

    fun onReceiveTtsSpeech(dialogId: String?) {
        Timber.d("onReceiveTtsSpeech:$dialogId")
        this.hasTtsSpeech = true
        this.dialogId = dialogId
    }

    fun onTtsPlayDone(dialogId: String) {
        val task = dependencyTaskMap.remove(dialogId)
        val stopTask = stopTaskMap.remove(dialogId)
        Timber.d("onTtsPlayDone:$dialogId,$task,$stopTask")
        task?.let { task.run() }
    }

    /**
     * 当TTS播报被停止时调用
     * @param dialogId 停止的对话ID
     */
    fun onTtsPlayStop(dialogId: String) {
        val task = dependencyTaskMap.remove(dialogId)
        val stopTask = stopTaskMap.remove(dialogId)
        stopTask?.run()
        Timber.d("onTtsPlayStop:$dialogId,$stopTask,$task - 任务已取消")
    }

    fun processDependencyTask(
        dialogId: String?,
        onStopTask: Runnable? = null,
        dependencyTask: Runnable
    ) {
        Timber.d("processDependencyTask:$dialogId,$hasTtsSpeech,$dependencyTask")
        dependencyTaskMap.clear()
        if (hasTtsSpeech) {
            dialogId?.let {
                dependencyTaskMap[dialogId] = dependencyTask
                onStopTask?.let {
                    stopTaskMap[dialogId] = onStopTask
                }
            }
        } else {
            dependencyTask.run()
        }
    }
}
