package com.xiaomi.aivs.track

enum class EventTrackEvent {
    EXECUTE; // 调接口、性能类、质量类

    fun point() = this.name.lowercase()
}

enum class EventTrackKv {

    STATE_ASR_RECORD_FAILED, // 上报ASR录音失败异常（并携带失败原因）
    ASR_FIRST_PACK_SENT, // 上报开始发送ASR音频
    ASR_FIRST_PARTIAL, // 上报接收首个ASR PARTIAL包（不判断是否包含文本）
    ASR_FIRST_TEXT, // 上报收到首个ASR文本
    STATE_ASR_FINAL_SIZE, // 上报ASR FINAL文本长度
    ASR_FINAL, // 上报接收到ASR判停信号（ASR FINAL为TRUE）
    STATE_NLP_REQUEST_FAILED, // 上报发起NLP请求失败（并携带失败原因）
    NLP_START_ANSWER, // 上报接收到STARTANSWER
    START_PRE_STREAM_TIME, // 上报接收到STARTPRESTREAM
    START_STREAM_TIME, // 上报接收到STARTSTREAM
    NLP_SPEAK_STREAM_INS_RECEIVED, // 上报收到SPEAK STREAM指令
    NLP_SPEAK_URL_INS_RECEIVED, // 上报收到SPEAK URL指令
    STATE_NLP_EXCEPTIONS, // 上报接收到NLP EXCEPTIONS指令数
    STATE_NLP_EXCEPTION_PAYLOAD, // 上报NLP EXCEPTIONS异常内容
    STATE_NLP_SYSTEM_ABORT, // 上报接受SYSTEM ABORT指令数
    STATE_NLP_ABORT_PAYLOAD, // 上报SYSTEM ABORT异常内容
    NLP_FINISH_ANSWER, // 上报接收到FINISHANSWER
    STATE_EXEC_INS_TOTAL, // 上报NLP结果指令数
    STATE_NLP_EXEC_FAILED, // 上报NLP执行失败（并携带执行失败的指令、错误码和错误信息）
    STATE_TTS_REQUEST_FAILED, // 上报发起TTS请求失败
    TTS_STREAM_START, // 上报TTS流播放开始
    TTS_URL_START, // 上报TTS URL播放开始
    STATE_TTS_URL_PLAY_FAILED, // 上报TTS-URL播放失败
    STATE_TTS_STREAM_PLAY_FAILED, // 上报TTS二进制音频播放失败
    WAKEUP_AFTER_TALK_XUE, // 说完“小爱同学”学字
    WAKEUP_SENT_EVENT, // 唤醒服务发送唤醒事件
    WAKEUP_RECEIVED_EVENT, // 小爱接收到唤醒事件
    WAKEUP_VOICE_REPLY, // 开始播放应答音
    WAKEUP_BALL_APPEAR, // 开始展示唤醒动效（灯效）
    ASR_OPEN_MIC, // 打开麦克风
    ASR_RECOGNIZE_SENT, // 发送RECOGNIZE事件
    ASR_FIRST_SAME_FINAL, // 接收到首个与FINAL相同的PARTIAL
    NLP_FINISH_STREAM, // 接收到FINISHSTREAM
    TTS_FIRST_PACK_RECEIVED, // 接收到第一个音频包
    STATE_EXEC_INS_SUCCESS, // NLP执行指令成功数
    STATE_EXEC_INS_FAILED, // NLP执行指令失败数
    STATE_EXEC_INS_FILTERED, // NLP指令未被处理数
    STATE_EXEC_RESULT, // 指令执行整体状态
    STATE_REQUEST_ID, // REQUEST ID
    STATE_QUERY_TYPE, // QUERY类型
    STATE_RESULT_TYPE, // 本次QUERY最终结果
    STATE_ERROR_CODE, // 错误状态码
    STATE_ERROR_MSG, // 错误描述
    STATE_CANCEL_MSG, // 取消原因
    STATE_DUPLEX, // 本次QUERY是否全双工，对于全双工模式下用户首轮QUERY会被处理成非全双工
    REJECT_RECOGNIZE_INS_RECEIVED, // 上报收到拒识指令时间
    STATE_VAD_END_TYPE, // VAD结束类型
    EXIT_XIAOAI_REASON, // 退出小爱的原因
    TTS_STOP_REASON, // TTS播放停止原因
    MEDIA_PLAY, //  媒体不执行的原因
    SET_PROP_ERROR, // 设置系统属性错误
    FINISH_REASON, // 会话结束原因
    UNRESPONSE_INFO,
    ROM_VER, // 眼镜固件版本
    SRV_ENV, // 小爱环境
    UUID, // 固件
    DEVICE_ID; // 设备ID.

    fun point() = this.name.lowercase()
}

object UploadImageQAEventParams {
    const val EVENT_KEY_TIP = "tip"
    const val EVENT_IMAGE_UPLOAD_TIP = "1634.4.3.1.43468"
    const val EVENT_IMAGE_UPLOAD_REQUEST_ID = "request_id"
    const val EVENT_IMAGE_UPLOAD_EVENT_ID = "event_id"
    const val EVENT_IMAGE_UPLOAD_RESULT = "image_upload_result"
}

object CrossDeviceControlEventParams {
    const val EVENT_KEY_TIP = "tip"
    const val EVENT_CROSS_DEVICE_CONTROL_TIP = "1634.5.1.1.44847"
    const val ACTIVITY_TYPE = "activity_type"
    const val START_TIME = "start_time"
    const val TRACE_ID = "trace_id"
}

/**
 * 反馈上传日志相关埋点参数
 */
object FeedBackLogEventParams {
    const val EVENT_KEY_TIP = "tip"
    const val EVENT_FEED_BAK_LOG_TIP = "1634.6.1.1.45309"
    const val EVENT_FEED_BAK_LOG_ACTIVITY_TYPE = "activity_type"
    const val EVENT_FEED_BAK_LOG_REQUEST_ID = "request_id"
    const val EVENT_FEED_BAK_LOG_START_TIME = "start_time"
    const val EVENT_FEED_BAK_LOG_FROM = "from"
}
