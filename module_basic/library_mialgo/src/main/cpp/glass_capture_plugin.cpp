#include "glass_capture_plugin.h"
#include "arc_glass_capture.h"
#include <math.h>
#include <float.h>
#define LOG_TAG "GLASS_CAPTRUE_PLUGIN"
int checkInitParam(CaptureInitParam* param, GlassCaptureHandle* handle)
{
    if (NULL == param)
    {
        ARC_LOGE("[%s]: checkInitParam error, param is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    if (NULL == handle)
    {
        ARC_LOGE("[%s]: checkInitParam error, handle is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    ARC_LOGD("[%s]: checkInitParam, maxWidth=%d, maxHeight=%d, \n", LOG_TAG, param->maxWidth, param->maxHeight);
    ARC_LOGD("[%s]: checkInitParam, metaNum=%d\n", LOG_TAG, param->metaNum);
    if (NULL == param->metaPath) 
    {
        ARC_LOGE("[%s]: checkInitParam error, metaPath is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }
    
    if (NULL == param->metaPath[0])
    {
        ARC_LOGE("[%s]: checkInitParam error, metaPath[0] is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    ARC_LOGD("[%s]: checkInitParam, LDC_binPath=%s \n", LOG_TAG, param->LDC_binPath);
    if (NULL == param->LDC_binPath)
    {
        ARC_LOGE("[%s]: checkInitParam error, LDC_binPath is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    if (param->maxWidth == 0 || param->maxHeight == 0)
    {
        ARC_LOGE("[%s]: checkInitParam error, maxWidth or maxHeight or maxFOV is 0 \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    return MOK;
}

int checkProcessParam(GlassCaptureHandle handle, CaptureProcessInput* input, CaptureProcessOutput* output)
{
    if (NULL == handle)
    {
        ARC_LOGE("[%s]: checkProcessParam error, handle is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    if (NULL == input)
    {
        ARC_LOGE("[%s]: checkProcessParam error, input is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    ARC_LOGD("[%s]: checkProcessParam, input frameNum =%d, width=%d, height=%d\n", LOG_TAG, input->frameNum, input->width, input->height);
    ARC_LOGD("[%s]: checkProcessParam, ouput width=%d, height=%d\n", LOG_TAG, output->width, output->height);
    if (input->width == 0 || input->height == 0 || input->frameNum == 0)
    {
        ARC_LOGE("[%s]: checkProcessParam error, input width or height or frameNum is 0 \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    if (input->frameNum > GLASS_MAX_INPUT_IMAGE_NUM)
    {
        ARC_LOGE("[%s]: checkProcessParam error, input frameNum[%d] exceed max frame[%d] \n", LOG_TAG, input->frameNum, GLASS_MAX_INPUT_IMAGE_NUM);
        return ERROR_INPUT_PARAM;
    }

    for(int i=0; i<input->frameNum; i++) 
    {
        if (NULL == input->buffer[i])
        {
            ARC_LOGE("[%s]: checkProcessParam error, input buffer[%d] is null \n", LOG_TAG, i);
            return ERROR_INPUT_PARAM;
        }
    }

    if (NULL == output)
    {
        ARC_LOGE("[%s]: checkProcessParam error, output is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }
    
    if (output->width == 0 || output->height == 0)
    {
        ARC_LOGE("[%s]: checkProcessParam error, output width or height is 0 \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }
    
    if (NULL == output->buffer)
    {
        ARC_LOGE("[%s]: checkProcessParam error, output buffer is null \n", LOG_TAG);
        return ERROR_INPUT_PARAM;
    }

    return MOK;
}

void init(CaptureInitParam* param, /*out*/GlassCaptureHandle* handle)
{
    MUInt64 ret = MOK;
	int logLevel = param->loglevel;
	if (logLevel > 0) 
	{
        config_log_level(ARC_ITG_ERR_MAX);
    }

    ret = checkInitParam(param, handle);
    if (ret != MOK)
    {
        ARC_LOGE("[%s]: init error, checkInitParam ret =%d\n", LOG_TAG, ret);
        return;
    }
    
    long start = now_ms();
    ArcGlassCapture* pArcGlassCapture = new ArcGlassCapture();
    *handle = (GlassCaptureHandle)pArcGlassCapture;
    ret = pArcGlassCapture->init(param);
    ARC_LOGD("[%s]: init ret=%d, cost time = %.2f ms\n", LOG_TAG, ret, now_ms()-start);
}


bool process(GlassCaptureHandle handle, CaptureProcessInput* input, CaptureProcessOutput* output)
{
    MUInt64 ret = MOK;
    ARC_LOGD("[%s]: 1111\n", LOG_TAG);
    ret = checkProcessParam(handle, input, output);
    ARC_LOGD("[%s]: 2222\n", LOG_TAG);
    if (ret != MOK)
    {
        ARC_LOGE("[%s]: process error, checkProcessParam ret =%d\n", LOG_TAG, ret);
        return false;
    }
    
    long start = now_ms();
    ArcGlassCapture* pArcGlassCapture = (ArcGlassCapture*)handle;
    ret = pArcGlassCapture->process(input, output);  
    ARC_LOGD("[%s]: process ret=%d, cost time = %.2f ms\n", LOG_TAG, ret, now_ms()-start);
    return ret == MOK ? true : false;
}

void setParam(GlassCaptureHandle handle, CaptureProcessParam* param)
{
    if (NULL == handle)
    {
        ARC_LOGE("[%s]: setParam error, handle is null \n", LOG_TAG);
        return;
    }

    if (NULL == param)
    {
        ARC_LOGE("[%s]: setParam error, param is null \n", LOG_TAG);
        return;
    }

    long start = now_ms();
    MUInt64 ret = MOK;
    ArcGlassCapture* pArcGlassCapture = (ArcGlassCapture*)handle;
    ret = pArcGlassCapture->setParam(param);  
    ARC_LOGD("[%s]: setParam ret=%d, cost time = %.2f ms\n", LOG_TAG, ret, now_ms()-start);
}

void uninit(GlassCaptureHandle handle)
{
    if (NULL == handle)
    {
        ARC_LOGE("[%s]: uninit error, handle is null \n", LOG_TAG);
        return;
    }

    long start = now_ms();
    MUInt64 ret = MOK;
    ArcGlassCapture* pArcGlassCapture = (ArcGlassCapture*)handle;
    ret = pArcGlassCapture->uninit(); 
    delete pArcGlassCapture;
    ARC_LOGD("[%s]: uninit ret=%d, cost time = %.2f ms\n", LOG_TAG, ret, now_ms()-start);
}