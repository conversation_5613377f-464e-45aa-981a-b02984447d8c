//
// Created by chico on 2025/2/25.
//

#include <jni.h>
#include <string>
#include "merror.h"
#include "ammem.h"
#include "glass_capture_plugin.h"
#include "libyuv/convert_from.h"
#include "libyuv/convert.h"
#include <stdlib.h>
#include <android/log.h>

extern "C" {
#include "include/heif.h"
}

#define TAG "PictureJNI"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__);
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__);
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__);
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__);
#define LOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, TAG, __VA_ARGS__);

jclass entryClass;
jfieldID handleFieldID;
jfieldID widthFieldID;
jfieldID heightFieldID;
jfieldID metaNumFieldID;
jfieldID metaPathFieldID;
jfieldID ldcPathFieldID;
jfieldID dumpPathFieldID;
jfieldID algoDebugFieldID;
jfieldID unRevisedFieldID;

jclass inputClass;
jfieldID imgNumFieldID;
//jfieldID imagesFieldID;
jfieldID inputWidthFieldID;
jfieldID inputHeightFieldID;
jfieldID filesFieldID;
jfieldID inputFormatTypeID;


jclass listClass;
jmethodID listGetMethodID;
jclass processImageClass;
jfieldID dataFieldID;


jclass outputClass;
jfieldID resultDataFieldID;
jfieldID unRevisedDataFieldID;
jfieldID resultWidthFieldID;
jfieldID resultHeightFieldID;

extern "C"
JNIEXPORT jint JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_JpegProcessorServer_initNative(JNIEnv *env,
                                                                                   jobject thiz,
                                                                                   jobject entry) {
    // TODO: implement initNative()
    // TODO: implement initNative()
    jint metaNum = env->GetIntField(entry, metaNumFieldID);

    jint width = env->GetIntField(entry, widthFieldID);
    jint height = env->GetIntField(entry, heightFieldID);
    jboolean algoDebug = env->GetBooleanField(entry, algoDebugFieldID);

    CaptureInitParam initParam = {0};
    initParam.metaNum = metaNum;
    jobject metaPath = env->GetObjectField(entry, metaPathFieldID);
    if (metaPath == NULL) {
        LOGE("error!!! metaPath is null");
        return -1;
    }

    char **metaPaths = (char **) malloc(sizeof(char *) * metaNum);
    if (metaPaths == NULL) {
        LOGE("error!!! malloc metaPaths");
        return -1;
    }
    for (int i = 0; i < metaNum; i++) {
        jstring metaString = (jstring) env->CallObjectMethod(metaPath, listGetMethodID, i);
        if (metaString == NULL) {
            LOGE("error!!! metaString is null");
            return -1;
        }
        const char *metaChar = env->GetStringUTFChars(metaString, 0);
        char *copiedStr = strdup(metaChar);
        *(metaPaths + i) = copiedStr;
        env->ReleaseStringUTFChars(metaString, metaChar);
        env->DeleteLocalRef(metaString);
    }
    initParam.metaPath = metaPaths;

    jstring ldcPathString = (jstring) env->GetObjectField(entry, ldcPathFieldID);
    if (ldcPathString == NULL) {
        LOGE("error!!! ldcPathString is null");
        return -1;
    }
    const char *ldcChar = env->GetStringUTFChars(ldcPathString, 0);
    initParam.LDC_binPath = const_cast<char *>(ldcChar);

    jstring dumpPathString = (jstring) env->GetObjectField(entry, dumpPathFieldID);
    jboolean dumpEmpty = env->IsSameObject(dumpPathString, NULL);
    const char *dumpChar = NULL;
    if (dumpEmpty) {
        initParam.dumpPath = NULL;
        initParam.needDump = JNI_FALSE;
    } else {
        dumpChar = env->GetStringUTFChars(dumpPathString, 0);
        initParam.dumpPath = const_cast<char *>(dumpChar);
        initParam.needDump = JNI_TRUE;
    }

    if (algoDebug) {
        initParam.loglevel = 1;
    } else {
        initParam.loglevel = 0;
    }

    jstring unRevisedString = (jstring) env->GetObjectField(entry, unRevisedFieldID);
    jboolean unRevisedEmpty = env->IsSameObject(unRevisedString, NULL);
    if (unRevisedEmpty) {
        initParam.enableSLC = JNI_FALSE;
    } else {
        initParam.enableSLC = JNI_TRUE;
    }
    env->DeleteLocalRef(unRevisedString);

    //先传着吧，算法后期可以从meta中读，目前他们还没有确定方案
    initParam.maxWidth = width;
    initParam.maxHeight = height;

    jlong outHandle;
    init(&initParam, (GlassCaptureHandle *) &outHandle);

    //用完即删
    for (int i = 0; i < metaNum; i++) {
        free(*(metaPaths + i));
    }
    free(metaPaths);

    env->DeleteLocalRef(metaPath);

    env->ReleaseStringUTFChars(ldcPathString, ldcChar);
    env->DeleteLocalRef(ldcPathString);

    if (!dumpEmpty) {
        env->ReleaseStringUTFChars(dumpPathString, dumpChar);
        env->DeleteLocalRef(dumpPathString);
    }

    env->SetLongField(entry, handleFieldID, outHandle);
    return 0;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_JpegProcessorServer_uninitNative(JNIEnv *env,
                                                                                     jobject thiz,
                                                                                     jlong handle) {
    // TODO: implement uninitNative()
    uninit((GlassCaptureHandle) handle);
}

jbyte *readFileIntoByteArray(const char *filePath, size_t *outSize) {
    FILE *file = fopen(filePath, "rb"); // 以二进制模式打开文件

    // 检查文件是否成功打开
    if (file == NULL) {
        LOGE("error open %s", filePath);
        return NULL;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    size_t fileSize = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 检查文件大小是否超过了SIZE_MAX
    if (fileSize > SIZE_MAX) {
        fclose(file);
        LOGE("error!!! to large file");
        return NULL;
    }

    // 分配一个足够大的数组来存储文件内容
    jbyte *fileData = (jbyte *) malloc(fileSize);
    if (fileData == NULL) {
        fclose(file);
        LOGE("error not enough memory");
        return NULL;
    }

    // 读取文件内容到数组
    size_t bytesRead = fread(fileData, 1, fileSize, file);

    // 确保我们成功读取了文件大小
    if (bytesRead != fileSize) {
        free(fileData);
        fclose(file);
        LOGE("error!!! read file");
        return NULL;
    }

    // 关闭文件
    fclose(file);

    // 如果outSize不是NULL，则设置输出的大小
    if (outSize != NULL) {
        *outSize = fileSize;
    }

    return fileData;
}

jbyte *getBytesFromHeic(const char *filePath, int type) {
    heif_context *context = heif_context_alloc();
    if (!context) {
        LOGE("heif failed to allocate heif context");
        return NULL;
    }

    // 加载HEIC文件
    heif_error error = heif_context_read_from_file(context, filePath, NULL);
    if (error.code != heif_error_Ok) {
        LOGE("Error reading heif file: %s", error.message);
        heif_context_free(context);
        return NULL;
    }

    // 获取主图片句柄
    heif_image_handle *image_handle;
    error = heif_context_get_primary_image_handle(context, &image_handle);
    if (error.code != heif_error_Ok) {
        LOGE("Error heif get primary image handle: %s", error.message);
        heif_context_free(context);
        return NULL;
    }

    // 解码像素数据
    heif_image *image;
    error = heif_decode_image(image_handle, &image, heif_colorspace_YCbCr, heif_chroma_420,
                              NULL);  // 可选：指定解码格式
    if (error.code != heif_error_Ok) {
        LOGE("heif failed to decode image: %s", error.message);
        heif_image_handle_release(image_handle);
        heif_context_free(context);
        return NULL;
    }

    // 获取YUV420的平面数据
    int yStride;
    const jbyte *yPlane = (const jbyte *) heif_image_get_plane_readonly(image, heif_channel_Y,
                                                                        &yStride);
    if (!yPlane) {
        LOGE("heif failed to get Y plane");
        heif_image_release(image);
        heif_image_handle_release(image_handle);
        heif_context_free(context);
        return NULL;
    }

    int uStride;
    const jbyte *uPlane = (const jbyte *) heif_image_get_plane_readonly(image, heif_channel_Cb,
                                                                        &uStride);
    if (!uPlane) {
        LOGE("heif failed to get U plane");
        heif_image_release(image);
        heif_image_handle_release(image_handle);
        heif_context_free(context);
        return NULL;
    }

    int vStride;
    const jbyte *vPlane = (const jbyte *) heif_image_get_plane_readonly(image, heif_channel_Cr,
                                                                        &vStride);
    if (!vPlane) {
        LOGE("heif failed to get V plane");
        heif_image_release(image);
        heif_image_handle_release(image_handle);
        heif_context_free(context);
        return NULL;
    }

    // 获取宽高
    int width = heif_image_get_width(image, heif_channel_Y);
    int height = heif_image_get_height(image, heif_channel_Y);
    LOGE("heif size %d x %d", width, height);
    int size = width * height * 3 / 2;  // NV21数据大小

    // 分配NV21缓冲区
    jbyte *result = (jbyte *) malloc(size);
    if (!result) {
        LOGE("heif memory allocation failed");
        heif_image_release(image);
        heif_image_handle_release(image_handle);
        heif_context_free(context);
        return NULL;
    }

    int res = 0;
    if (type == 0) {
        //0 是 i420
        res = libyuv::I420Copy(
                reinterpret_cast<const uint8_t *>(yPlane),
                yStride,
                reinterpret_cast<const uint8_t *>(uPlane),
                uStride,
                reinterpret_cast<const uint8_t *>(vPlane),
                vStride,
                reinterpret_cast<uint8_t *>(result),
                width,
                reinterpret_cast<uint8_t *>(result + (width * height)),
                width >> 1,
                reinterpret_cast<uint8_t *>(result + (width * height * 5 / 4)),
                width >> 1,
                width, height); // 默认无旋转
    } else if (type == 1) {
        //1是nv12
        res = libyuv::I420ToNV12(
                reinterpret_cast<const uint8_t *>(yPlane),
                yStride,
                reinterpret_cast<const uint8_t *>(uPlane),
                uStride,
                reinterpret_cast<const uint8_t *>(vPlane),
                vStride,
                reinterpret_cast<uint8_t *>(result),
                width,
                reinterpret_cast<uint8_t *>(result + (width * height)),
                width,
                width, height); // 默认无旋转
    } else if (type == 2) {
        //2是nv21
        res = libyuv::I420ToNV21(
                reinterpret_cast<const uint8_t *>(yPlane),
                yStride,
                reinterpret_cast<const uint8_t *>(uPlane),
                uStride,
                reinterpret_cast<const uint8_t *>(vPlane),
                vStride,
                reinterpret_cast<uint8_t *>(result),
                width,
                reinterpret_cast<uint8_t *>(result + (width * height)),
                width,
                width, height); // 默认无旋转
    } else {
        res = -1;
    }


    // 释放资源
    heif_image_release(image);
    heif_image_handle_release(image_handle);
    heif_context_free(context);

    if (res != 0) {
        LOGE("error!!! heif I420ToNV21");
        free(result);
        return NULL;
    }
    return result;
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_JpegProcessorServer_processNative(JNIEnv *env,
                                                                                      jobject thiz,
                                                                                      jlong handle,
                                                                                      jobject input,
                                                                                      jobject output) {
    int imgNumObj = env->GetIntField(input, imgNumFieldID);
    if (imgNumObj < 1) {
        return JNI_FALSE;
    }
    int inputFormatType = env->GetIntField(input, inputFormatTypeID);
    CaptureProcessInput processInput = {0};
    processInput.frameNum = imgNumObj;
    processInput.width = env->GetIntField(input, inputWidthFieldID);
    processInput.height = env->GetIntField(input, inputHeightFieldID);

    CaptureProcessOutput processOutput;
    processOutput.width = env->GetIntField(output, resultWidthFieldID);
    processOutput.height = env->GetIntField(output, resultHeightFieldID);
    jint *imageTypes = (jint *) malloc(sizeof(jint) * 2);
    if (imageTypes == NULL) {
        LOGE("error!!! malloc image types");
        return JNI_FALSE;
    }

    jbyte **inputBuffers = (jbyte **) malloc(sizeof(jbyte *) * imgNumObj);
    if (inputBuffers == NULL) {
        LOGE("error!!! malloc inputBuffers");
        free(imageTypes);
        return JNI_FALSE;
    }

    jbyte **outputBuffers = (jbyte **) malloc(sizeof(jbyte *) * 2);//就分配两个指针，应该不会失败吧
    if (outputBuffers == NULL) {
        LOGE("error!!! malloc outputBuffers");
        free(imageTypes);
        free(inputBuffers);
        return JNI_FALSE;
    }

    jint outputLength = processOutput.width * processOutput.height * 3 / 2;
    jbyte *outputBuffer = (jbyte *) malloc(outputLength);
    if (outputBuffer == NULL) {
        LOGE("error!!! malloc outputBuffer");
        free(imageTypes);
        for (int i = 0; i < imgNumObj; i++) {
            free(*(inputBuffers + i));
        }
        free(inputBuffers);

        for (int i = 0; i < 2; i++) {
            free(*(outputBuffers + i));
        }
        free(outputBuffers);
        return JNI_FALSE;
    }
    *outputBuffers = outputBuffer;

    jbyte *outputBuffer2 = (jbyte *) malloc(outputLength);
    if (outputBuffer2 == NULL) {
        LOGE("error!!! malloc outputBuffer2");
        free(imageTypes);
        for (int i = 0; i < imgNumObj; i++) {
            free(*(inputBuffers + i));
        }
        free(inputBuffers);

        for (int i = 0; i < 2; i++) {
            free(*(outputBuffers + i));
        }
        free(outputBuffers);
        return JNI_FALSE;
    }
    *(outputBuffers + 1) = outputBuffer2;

    processOutput.buffer = (void **) outputBuffers;

    for (int i = 0; i < imgNumObj; i++) {
        jobject filesObj = env->GetObjectField(input, filesFieldID);
        jstring fileStrObj = (jstring) env->CallObjectMethod(filesObj, listGetMethodID, i);

        const char *fileStrChar = env->GetStringUTFChars(fileStrObj, 0);
        size_t readSize = 0;
        jbyte *input = NULL;
        if (inputFormatType == 2) {
            input = getBytesFromHeic(fileStrChar, 2);
        } else {
            input = readFileIntoByteArray(fileStrChar, &readSize);
        }
        if (input == NULL) {
            LOGE("error !!! read file");
            free(imageTypes);
            for (int j = 0; j < i; j++) {
                free(*(inputBuffers + j));
            }
            free(inputBuffers);
            for (int k = 0; k < 2; k++) {
                free(*(outputBuffers + k));
            }
            free(outputBuffers);
            env->ReleaseStringUTFChars(fileStrObj, fileStrChar);
            env->DeleteLocalRef(fileStrObj);
            env->DeleteLocalRef(filesObj);
            return JNI_FALSE;
        }

        *(inputBuffers + i) = input;

        env->ReleaseStringUTFChars(fileStrObj, fileStrChar);
        env->DeleteLocalRef(fileStrObj);
        env->DeleteLocalRef(filesObj);
    }
    processInput.buffer = (void **) inputBuffers;
    processOutput.ImageTypes = imageTypes;

    jboolean nativeResult = process((GlassCaptureHandle) handle, &processInput, &processOutput);
    if (nativeResult) {
        //返回值为true, 处理成功，processOutput中至少有一个未经过校正的buffer
        if (*(processOutput.ImageTypes) == 1) {
            //ImageType[0]为1代表buffer[0]有输出图，该图未经过水平校正
            if (*(processOutput.ImageTypes + 1) == 2) {
                LOGI("both final and unrevised image ready");
                //ImageType[1]为2代表buffer[1]有输出图，该图经过水平校正,使用该图作为最终图, buffer[0]作为未校正图
                jbyteArray resultArray = env->NewByteArray(outputLength);
                if (resultArray == NULL) {
                    LOGE("error!!! final NewByteArray is null, maybe lack of memory");
                    //无法生成最终图，释放所有空间
                    free(imageTypes);
                    for (int i = 0; i < 2; i++) {
                        free(*(outputBuffers + i));
                    }
                    free(outputBuffers);
                    for (int i = 0; i < imgNumObj; i++) {
                        free(*(inputBuffers + i));
                    }
                    free(inputBuffers);
                    return JNI_FALSE;
                }
                env->SetByteArrayRegion(resultArray, 0,
                                        outputLength,
                                        (const jbyte *) (*(processOutput.buffer + 1)));
                env->SetObjectField(output, resultDataFieldID, resultArray);
                env->DeleteLocalRef(resultArray);

                jbyteArray unrevisedArray = env->NewByteArray(outputLength);
                if (unrevisedArray == NULL) {
                    LOGE("error!!! unrevised NewByteArray is null, maybe lack of memory");
                    free(imageTypes);
                    for (int i = 0; i < 2; i++) {
                        free(*(outputBuffers + i));
                    }
                    free(outputBuffers);
                    for (int i = 0; i < imgNumObj; i++) {
                        free(*(inputBuffers + i));
                    }
                    free(inputBuffers);
                    //这里还是要返回TRUE的，毕竟有一个最终值了
                    return JNI_TRUE;
                }
                env->SetByteArrayRegion(unrevisedArray, 0,
                                        outputLength,
                                        (const jbyte *) (*(processOutput.buffer)));
                env->SetObjectField(output, unRevisedDataFieldID, unrevisedArray);
                env->DeleteLocalRef(unrevisedArray);
            } else if (*(processOutput.ImageTypes + 1) == 0) {
                //buffer[1]无图， 使用buffer[0]作为最终图, 此时只有一张图
                LOGI("final image is unrevised");
                jbyteArray resultArray = env->NewByteArray(outputLength);
                if (resultArray == NULL) {
                    LOGE("error!!! final NewByteArray is null, maybe lack of memory");
                    //无法生成最终图，释放所有空间
                    free(imageTypes);
                    for (int i = 0; i < 2; i++) {
                        free(*(outputBuffers + i));
                    }
                    free(outputBuffers);
                    for (int i = 0; i < imgNumObj; i++) {
                        free(*(inputBuffers + i));
                    }
                    free(inputBuffers);
                    return JNI_FALSE;
                }
                env->SetByteArrayRegion(resultArray, 0,
                                        outputLength,
                                        (const jbyte *) (*(processOutput.buffer)));
                env->SetObjectField(output, resultDataFieldID, resultArray);
                env->DeleteLocalRef(resultArray);
            } else {
                LOGE("error!!! no final and unrevised image");
            }
        } else {
            //ImageType[0]为其他值，代表没有任何输出图像
            LOGE("error!!! processNative no output image");
            free(imageTypes);
            for (int i = 0; i < 2; i++) {
                free(*(outputBuffers + i));
            }
            free(outputBuffers);
            for (int i = 0; i < imgNumObj; i++) {
                free(*(inputBuffers + i));
            }
            free(inputBuffers);
            return JNI_FALSE;
        }
    } else {
        //process failed, no log
    }
    free(imageTypes);
    for (int i = 0; i < 2; i++) {
        free(*(outputBuffers + i));
    }
    free(outputBuffers);
    for (int i = 0; i < imgNumObj; i++) {
        free(*(inputBuffers + i));
    }
    free(inputBuffers);
    return nativeResult;
}
extern "C"
JNIEXPORT void JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_JpegProcessorServer_setupNative(JNIEnv *env,
                                                                                    jclass clazz) {
    // TODO: implement setupNative()
    // TODO: implement setupNative()
    jclass localEntryClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessEntry");
    if (localEntryClass == NULL) {
        LOGE("error!!! localEntryClass is null");
        return;
    }
    entryClass = (jclass) env->NewGlobalRef(localEntryClass);

    jclass localInputClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessInput");
    if (localInputClass == NULL) {
        LOGE("error!!! localInputClass is null");
        return;
    }
    inputClass = (jclass) env->NewGlobalRef(localInputClass);

    jclass localOutputClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessOutput");
    if (localOutputClass == NULL) {
        LOGE("error!!! localOutputClass is null");
        return;
    }
    outputClass = (jclass) env->NewGlobalRef(localOutputClass);

    jclass localListClass = env->FindClass("java/util/List");
    if (localListClass == NULL) {
        LOGE("error!!! localListClass is null");
        return;
    }
    listClass = (jclass) env->NewGlobalRef(localListClass);

    jclass localImageClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessImage");
    if (localImageClass == NULL) {
        LOGE("error!!! localImageClass is null");
        return;
    }
    processImageClass = (jclass) env->NewGlobalRef(localImageClass);

    handleFieldID = env->GetFieldID(entryClass, "handle", "J");
    if (handleFieldID == NULL) {
        LOGE("error!!! handleFieldID is null");
        return;
    }
    widthFieldID = env->GetFieldID(entryClass, "width", "I");
    if (widthFieldID == NULL) {
        LOGE("error!!! widthFieldID is null");
        return;
    }
    heightFieldID = env->GetFieldID(entryClass, "height", "I");
    if (heightFieldID == NULL) {
        LOGE("error!!! heightFieldID is null");
        return;
    }
    algoDebugFieldID = env->GetFieldID(entryClass, "enableAlgoDebug", "Z");
    if (algoDebugFieldID == NULL) {
        LOGE("error!!! algoDebugFieldID is null");
        return;
    }

    metaNumFieldID = env->GetFieldID(entryClass, "metaNum", "I");
    if (metaNumFieldID == NULL) {
        LOGE("error!!! metaNumFieldID is null");
        return;
    }
    metaPathFieldID = env->GetFieldID(entryClass, "metaPath", "Ljava/util/List;");
    if (metaPathFieldID == NULL) {
        LOGE("error!!! metaPathFieldID is null");
        return;
    }
    ldcPathFieldID = env->GetFieldID(entryClass, "ldcPath", "Ljava/lang/String;");
    if (ldcPathFieldID == NULL) {
        LOGE("error!!! ldcPathFieldID is null");
        return;
    }
    dumpPathFieldID = env->GetFieldID(entryClass, "dumpPath", "Ljava/lang/String;");
    if (dumpPathFieldID == NULL) {
        LOGE("error!!! dumpPathFieldID is null");
        return;
    }

    unRevisedFieldID = env->GetFieldID(entryClass, "unrevisedPath", "Ljava/lang/String;");
    if (unRevisedFieldID == NULL) {
        LOGE("error!!! unRevisedFieldID is null");
        return;
    }

    imgNumFieldID = env->GetFieldID(inputClass, "imgNum", "I");
    if (imgNumFieldID == NULL) {
        LOGE("error!!! imgNumFieldID is null");
        return;
    }
    inputWidthFieldID = env->GetFieldID(inputClass, "width", "I");
    if (inputWidthFieldID == NULL) {
        LOGE("error!!! inputWidthFieldID is null");
        return;
    }
    inputHeightFieldID = env->GetFieldID(inputClass, "height", "I");
    if (inputHeightFieldID == NULL) {
        LOGE("error!!! inputHeightFieldID is null");
        return;
    }
    inputFormatTypeID = env->GetFieldID(inputClass, "inputFormatType", "I");
    if (inputFormatTypeID == NULL) {
        LOGE("error!!! inputFormatTypeID is null");
        return;
    }
//    imagesFieldID = env->GetFieldID(inputClass, "images", "Ljava/util/List;");
//    if (imagesFieldID == NULL) {
//        LOGE("error!!! imagesFieldID is null");
//        return;
//    }
    filesFieldID = env->GetFieldID(inputClass, "files", "Ljava/util/List;");
    if (filesFieldID == NULL) {
        LOGE("error!!! filesFieldID is null");
        return;
    }
    resultDataFieldID = env->GetFieldID(outputClass, "result", "[B");
    if (resultDataFieldID == NULL) {
        LOGE("error!!! resultDataFieldID is null");
        return;
    }
    unRevisedDataFieldID = env->GetFieldID(outputClass, "unRevisedResult", "[B");
    if (unRevisedDataFieldID == NULL) {
        LOGE("error!!! unRevisedDataFieldID is null");
        return;
    }
    resultWidthFieldID = env->GetFieldID(outputClass, "width", "I");
    if (resultWidthFieldID == NULL) {
        LOGE("error!!! resultWidthFieldID is null");
        return;
    }
    resultHeightFieldID = env->GetFieldID(outputClass, "height", "I");
    if (resultHeightFieldID == NULL) {
        LOGE("error!!! resultHeightFieldID is null");
        return;
    }
    listGetMethodID = env->GetMethodID(listClass, "get", "(I)Ljava/lang/Object;");
    if (listGetMethodID == NULL) {
        LOGE("error!!! listGetMethodID null");
        return;
    }
    dataFieldID = env->GetFieldID(processImageClass, "data", "[B");
    if (dataFieldID == NULL) {
        LOGE("error!!! dataFieldID is null");
        return;
    }
}
extern "C"
JNIEXPORT jbyteArray JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_JpegProcessorServer_heicToNV21(JNIEnv *env,
                                                                                   jclass clazz,
                                                                                   jstring heic_path,
                                                                                   jint width,
                                                                                   jint height) {
    // TODO: implement heicToNV21()
    const char *fileStrChar = env->GetStringUTFChars(heic_path, 0);
    jbyte *result = getBytesFromHeic(fileStrChar, 2);

    jbyteArray resultArray = env->NewByteArray(width * height * 3 / 2);
    if (resultArray == NULL) {
        LOGE("error!!! NewByteArray is null, maybe lack of memory");
        free(result);
        env->ReleaseStringUTFChars(heic_path, fileStrChar);
        return NULL;
    }
    env->SetByteArrayRegion(resultArray, 0, width * height * 3 / 2, result);

    free(result);
    env->ReleaseStringUTFChars(heic_path, fileStrChar);
    return resultArray;
}
extern "C"
JNIEXPORT jbyteArray JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_JpegProcessorServer_heicToI420(JNIEnv *env,
                                                                                   jclass clazz,
                                                                                   jstring heic_path,
                                                                                   jint width,
                                                                                   jint height) {
    // TODO: implement heicToI420()
    const char *fileStrChar = env->GetStringUTFChars(heic_path, 0);
    jbyte *result = getBytesFromHeic(fileStrChar, 0);

    jbyteArray resultArray = env->NewByteArray(width * height * 3 / 2);
    if (resultArray == NULL) {
        LOGE("error!!! NewByteArray is null, maybe lack of memory");
        free(result);
        env->ReleaseStringUTFChars(heic_path, fileStrChar);
        return NULL;
    }
    env->SetByteArrayRegion(resultArray, 0, width * height * 3 / 2, result);

    free(result);
    env->ReleaseStringUTFChars(heic_path, fileStrChar);
    return resultArray;
}