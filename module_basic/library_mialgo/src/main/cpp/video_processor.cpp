//
// Created by chico on 2025/2/25.
//

#include <jni.h>
#include <string>
#include "arcsoft_videostabilizer.h"
#include "arcsoft_videostabilizer_common.h"
#include "merror.h"
#include "ammem.h"
#include <stdlib.h>
#include <android/log.h>
#include "glass_video_plugin.h"

#define TAG "VideoJNI"
#define BYPASS 0
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__);
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__);
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__);
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__);
#define LOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, TAG, __VA_ARGS__);

jclass entryClass;
jfieldID handleFieldID;
jfieldID widthFieldID;
jfieldID heightFieldID;
jfieldID delayNumFieldID;
jfieldID blurFieldID;
jfieldID cropWFieldID;
jfieldID cropHFieldID;
jfieldID metaNumFieldID;
jfieldID gyroNumFieldID;
jfieldID metaPathFieldID;
jfieldID gyroPathFieldID;
jfieldID ldcPathFieldID;
jfieldID dumpPathFieldID;
jfieldID outputWidthFieldID;
jfieldID outputHeightFieldID;
jfieldID algoDebugFieldID;

jclass inputClass;
jfieldID inputFrameIdFieldID;
jfieldID imgNumFieldID;
jfieldID imagesFieldID;
jfieldID inputWidthFieldID;
jfieldID inputHeightFieldID;
jfieldID inputTSFieldID;
jfieldID inputFormatFieldID;

jclass listClass;
jmethodID listGetMethodID;
jclass processImageClass;
jfieldID dataFieldID;


jclass outputClass;
jfieldID resultTSFieldID;
jfieldID resultDataFieldID;
jfieldID resultWidthFieldID;
jfieldID resultHeightFieldID;
jfieldID resultFrameIDFieldID;
jfieldID resultFormatFieldID;


extern "C"
JNIEXPORT jint JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_VideoProcessorServer_initNative(JNIEnv *env,
                                                                                    jobject thiz,
                                                                                    jobject entry) {
    if (BYPASS) {
        env->SetLongField(entry, handleFieldID, 1l);
        return 0;
    }
    // TODO: implement initNative()
    jint metaNum = env->GetIntField(entry, metaNumFieldID);
    if (metaNum != 1) {
        return -1;
    }
    jint gyroNum = env->GetIntField(entry, gyroNumFieldID);
    if (gyroNum != 1) {
        return -1;
    }
    jint width = env->GetIntField(entry, widthFieldID);
    jint height = env->GetIntField(entry, heightFieldID);
    jint outputWidth = env->GetIntField(entry, outputWidthFieldID);
    jint outputHeight = env->GetIntField(entry, outputHeightFieldID);
    jint cropW = env->GetIntField(entry, cropWFieldID);
    jint cropH = env->GetIntField(entry, cropHFieldID);
    jint delayNum = env->GetIntField(entry, delayNumFieldID);
    jint blur = env->GetIntField(entry, blurFieldID);
    jboolean algoDebug = env->GetBooleanField(entry, algoDebugFieldID);

    InitParam initParam;
    jobject metaPath = env->GetObjectField(entry, metaPathFieldID);
    if (metaPath == NULL) {
        LOGE("error!!! metaPath is null");
        return -1;
    }
    jstring metaString = (jstring) env->CallObjectMethod(metaPath, listGetMethodID, 0);
    if (metaString == NULL) {
        LOGE("error!!! metaString is null");
        return -1;
    }
    const char *metaChar = env->GetStringUTFChars(metaString, 0);
    initParam.sensorBaseParamPath = const_cast<char *>(metaChar);

    jobject gyroPath = env->GetObjectField(entry, gyroPathFieldID);
    if (gyroPath == NULL) {
        LOGE("error!!! gyroPath is null");
        return -1;
    }
    jstring gyroString = (jstring) env->CallObjectMethod(gyroPath, listGetMethodID, 0);
    if (gyroString == NULL) {
        LOGE("error!!! gyroString is null");
        return -1;
    }
    const char *gyroChar = env->GetStringUTFChars(gyroString, 0);
    initParam.sensorDataPath = const_cast<char *>(gyroChar);

    jstring ldcPathString = (jstring) env->GetObjectField(entry, ldcPathFieldID);
    if (ldcPathString == NULL) {
        LOGE("error!!! ldcPathString is null");
        return -1;
    }
    const char *ldcChar = env->GetStringUTFChars(ldcPathString, 0);
    initParam.LDC_binPath = const_cast<char *>(ldcChar);

    jstring dumpPathString = (jstring) env->GetObjectField(entry, dumpPathFieldID);
    jboolean dumpEmpty = env->IsSameObject(dumpPathString, NULL);
    const char *dumpChar = NULL;
    if (dumpEmpty) {
        initParam.dumpPath = NULL;
        initParam.needDump = JNI_FALSE;
    } else {
        dumpChar = env->GetStringUTFChars(dumpPathString, 0);
        initParam.dumpPath = const_cast<char *>(dumpChar);
        initParam.needDump = JNI_TRUE;
    }

    initParam.pipeline = 0;
    initParam.blur_reduction_strength = blur;
    initParam.output_delay = delayNum;
    initParam.input_w = width;
    initParam.input_h = height;
    initParam.output_w = outputWidth;
    initParam.output_h = outputHeight;
    LOGD("chicodong init width: %d,height:%d, outputWidth: %d, outputHeight: %d", width, height,
         outputWidth, outputHeight);
    initParam.crop_ratio_w = cropW;
    initParam.crop_ratio_h = cropH;
    if (algoDebug) {
        initParam.loglevel = 1;
    } else {
        initParam.loglevel = 0;
    }

    jlong outHandle;
    jint initResult = init(&initParam, (GlassVideoHandle *) &outHandle);
    env->ReleaseStringUTFChars(metaString, metaChar);
    env->DeleteLocalRef(metaPath);
    env->DeleteLocalRef(metaString);

    env->ReleaseStringUTFChars(gyroString, gyroChar);
    env->DeleteLocalRef(gyroPath);
    env->DeleteLocalRef(gyroString);

    env->ReleaseStringUTFChars(ldcPathString, ldcChar);
    env->DeleteLocalRef(ldcPathString);

    if (!dumpEmpty) {
        env->ReleaseStringUTFChars(dumpPathString, dumpChar);
        env->DeleteLocalRef(dumpPathString);
    }

    env->SetLongField(entry, handleFieldID, outHandle);
    return initResult;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_VideoProcessorServer_uninitNative(JNIEnv *env,
                                                                                      jobject thiz,
                                                                                      jlong handle) {
    // TODO: implement uninitNative()
    if (BYPASS) {
        return;
    }
    uninit((GlassVideoHandle) handle);
}
extern "C"
JNIEXPORT jint JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_VideoProcessorServer_processNative(JNIEnv *env,
                                                                                       jobject thiz,
                                                                                       jlong handle,
                                                                                       jobject input,
                                                                                       jobject output) {
    // TODO: implement processNative()
    int imgNumObj = env->GetIntField(input, imgNumFieldID);
    if (imgNumObj > 1) {
        LOGE("only support one image now");
        return -1;
    }
    int resultWidthObj = env->GetIntField(output, resultWidthFieldID);
    int resultHeightObj = env->GetIntField(output, resultHeightFieldID);
    ProcessOutput processOutput;
    processOutput.height = resultHeightObj;
    processOutput.width = resultWidthObj;
    int outputLength = resultWidthObj * resultHeightObj * 3 / 2;

    //优先开栈内存，不开堆内存, 记得free
    jbyte *macData = (jbyte *) malloc(outputLength);
    if (macData == NULL) {
        LOGE("error!!! malloc output buffer");
        return -1;
    }
    //jbyte macData[outputLength];
    processOutput.buffer = macData;

    if (imgNumObj == 0) {
        jint nativeResult = process((GlassVideoHandle) handle, NULL, &processOutput);
        if (nativeResult == 0) {
            //算法需要填充frameIndex和buffer，包含有效数据
            env->SetIntField(output, resultFrameIDFieldID, processOutput.frameIndex);
            env->SetIntField(output, resultFormatFieldID, processOutput.format);
            env->SetLongField(output, resultTSFieldID, processOutput.timestamp);
            jbyteArray resultArray = env->NewByteArray(outputLength);
            if (resultArray == NULL) {
                LOGE("reissue error create java array!!!");
                free(macData);
                return -1;
            }
            env->SetByteArrayRegion(resultArray, 0, outputLength,
                                    (const jbyte *) processOutput.buffer);
            env->SetObjectField(output, resultDataFieldID, resultArray);
            env->DeleteLocalRef(resultArray);
        } else if (nativeResult == 10) {
            LOGE("chicodong, should not return reissue value");
        } else {
            LOGE("chicodong, process reissue error %d", nativeResult);
        }
        free(macData);
        return nativeResult;
    } else {
        //如果携带有效输入数据
        jobject imagesObj = env->GetObjectField(input, imagesFieldID);
        //只处理第一个
        jobject elementObj = env->CallObjectMethod(imagesObj, listGetMethodID, 0);
        jbyteArray dataObj = (jbyteArray) env->GetObjectField(elementObj, dataFieldID);
        if (env->IsSameObject(dataObj, NULL)) {
            LOGE("do not contains valid input");
            env->DeleteLocalRef(dataObj);
            env->DeleteLocalRef(elementObj);
            env->DeleteLocalRef(imagesObj);
            free(macData);
            return -1;
        }
        jbyte *dataByte = env->GetByteArrayElements(dataObj, 0);
        if (dataByte == NULL) {
            LOGE("get image data error");
            env->ReleaseByteArrayElements(dataObj, dataByte, 0);
            env->DeleteLocalRef(dataObj);
            env->DeleteLocalRef(elementObj);
            env->DeleteLocalRef(imagesObj);
            free(macData);
            return -1;
        }

        ProcessInput processInput;
        processInput.frameIndex = env->GetIntField(input, inputFrameIdFieldID);
        processInput.width = env->GetIntField(input, inputWidthFieldID);
        processInput.height = env->GetIntField(input, inputHeightFieldID);
        processInput.timestamp = env->GetLongField(input, inputTSFieldID);
        processInput.buffer = dataByte;
        jint format = env->GetIntField(input, inputFormatFieldID);
        if (format == 2) {
            processInput.format = ARC_FORMAT_NV21;
        } else {
            processInput.format = ARC_FORMAT_NV12;
        }

        jint nativeResult = -1;
        if (BYPASS) {
            processOutput.buffer = processInput.buffer;
            processOutput.frameIndex = processInput.frameIndex;
            processOutput.timestamp = processInput.timestamp;
            nativeResult = 0;
        } else {
            nativeResult = process((GlassVideoHandle) handle, &processInput, &processOutput);
        }
        if (nativeResult == 0) {
            //填充返回值
            env->SetIntField(output, resultFrameIDFieldID, processOutput.frameIndex);
            env->SetLongField(output, resultTSFieldID, processOutput.timestamp);
            env->SetIntField(output, resultFormatFieldID, processOutput.format);
            jbyteArray resultArray = env->NewByteArray(outputLength);
            if (resultArray == NULL) {
                LOGE("error create java array!!!");
                env->ReleaseByteArrayElements(dataObj, dataByte, 0);
                env->DeleteLocalRef(elementObj);
                env->DeleteLocalRef(dataObj);
                env->DeleteLocalRef(imagesObj);
                free(macData);
                return -1;
            }
            env->SetByteArrayRegion(resultArray, 0,
                                    outputLength,
                                    (const jbyte *) processOutput.buffer);
            env->SetObjectField(output, resultDataFieldID, resultArray);
            env->DeleteLocalRef(resultArray);
        } else if (nativeResult == 10) {
            //发送的数据被缓存了
        } else {
            LOGE("chicodong", "process may error %d", nativeResult);
        }
        env->ReleaseByteArrayElements(dataObj, dataByte, 0);
        env->DeleteLocalRef(elementObj);
        env->DeleteLocalRef(dataObj);
        env->DeleteLocalRef(imagesObj);
        free(macData);
        return nativeResult;
    }
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_VideoProcessorServer_setParamNative(JNIEnv *env,
                                                                                        jobject thiz,
                                                                                        jint handle,
                                                                                        jobject param) {
    // TODO: implement setParamNative(
}
extern "C"
JNIEXPORT void JNICALL
Java_com_xiaomi_algoprocessor_core_processor_server_VideoProcessorServer_setupNative(JNIEnv *env,
                                                                                     jclass clazz) {
    // TODO: implement setupNative()
    jclass localEntryClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessEntry");
    if (localEntryClass == NULL) {
        LOGE("error!!! localEntryClass is null");
        return;
    }
    entryClass = (jclass) env->NewGlobalRef(localEntryClass);

    jclass localInputClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessInput");
    if (localInputClass == NULL) {
        LOGE("error!!! localInputClass is null");
        return;
    }
    inputClass = (jclass) env->NewGlobalRef(localInputClass);

    jclass localOutputClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessOutput");
    if (localOutputClass == NULL) {
        LOGE("error!!! localOutputClass is null");
        return;
    }
    outputClass = (jclass) env->NewGlobalRef(localOutputClass);

    jclass localListClass = env->FindClass("java/util/List");
    if (localListClass == NULL) {
        LOGE("error!!! localListClass is null");
        return;
    }
    listClass = (jclass) env->NewGlobalRef(localListClass);

    jclass localImageClass = env->FindClass("com/xiaomi/algoprocessor/core/data/ProcessImage");
    if (localImageClass == NULL) {
        LOGE("error!!! localImageClass is null");
        return;
    }
    processImageClass = (jclass) env->NewGlobalRef(localImageClass);

    handleFieldID = env->GetFieldID(entryClass, "handle", "J");
    if (handleFieldID == NULL) {
        LOGE("error!!! handleFieldID is null");
        return;
    }
    widthFieldID = env->GetFieldID(entryClass, "width", "I");
    if (widthFieldID == NULL) {
        LOGE("error!!! widthFieldID is null");
        return;
    }
    heightFieldID = env->GetFieldID(entryClass, "height", "I");
    if (heightFieldID == NULL) {
        LOGE("error!!! heightFieldID is null");
        return;
    }
    algoDebugFieldID = env->GetFieldID(entryClass, "enableAlgoDebug", "Z");
    if (algoDebugFieldID == NULL) {
        LOGE("error!!! algoDebugFieldID is null");
        return;
    }
    outputWidthFieldID = env->GetFieldID(entryClass, "outputWidth", "I");
    if (outputWidthFieldID == NULL) {
        LOGE("error!!! outputWidthFieldID is null");
        return;
    }
    outputHeightFieldID = env->GetFieldID(entryClass, "outputHeight", "I");
    if (outputHeightFieldID == NULL) {
        LOGE("error!!! outputHeightFieldID is null");
        return;
    }
    delayNumFieldID = env->GetFieldID(entryClass, "delayNum", "I");
    if (delayNumFieldID == NULL) {
        LOGE("error!!! delayNumFieldID is null");
        return;
    }
    blurFieldID = env->GetFieldID(entryClass, "blurStrength", "I");
    if (blurFieldID == NULL) {
        LOGE("error!!! blurFieldID is null");
        return;
    }
    cropWFieldID = env->GetFieldID(entryClass, "cropW", "I");
    if (cropWFieldID == NULL) {
        LOGE("error!!! cropWFieldID is null");
        return;
    }
    cropHFieldID = env->GetFieldID(entryClass, "cropH", "I");
    if (cropHFieldID == NULL) {
        LOGE("error!!! cropHFieldID is null");
        return;
    }
    metaNumFieldID = env->GetFieldID(entryClass, "metaNum", "I");
    if (metaNumFieldID == NULL) {
        LOGE("error!!! metaNumFieldID is null");
        return;
    }
    gyroNumFieldID = env->GetFieldID(entryClass, "gyroNum", "I");
    if (gyroNumFieldID == NULL) {
        LOGE("error!!! gyroNumFieldID is null");
        return;
    }
    metaPathFieldID = env->GetFieldID(entryClass, "metaPath", "Ljava/util/List;");
    if (metaPathFieldID == NULL) {
        LOGE("error!!! metaPathFieldID is null");
        return;
    }
    gyroPathFieldID = env->GetFieldID(entryClass, "gyroPath", "Ljava/util/List;");
    if (gyroPathFieldID == NULL) {
        LOGE("error!!! gyroPathFieldID is null");
        return;
    }

    ldcPathFieldID = env->GetFieldID(entryClass, "ldcPath", "Ljava/lang/String;");
    if (ldcPathFieldID == NULL) {
        LOGE("error!!! ldcPathFieldID is null");
        return;
    }

    dumpPathFieldID = env->GetFieldID(entryClass, "dumpPath", "Ljava/lang/String;");
    if (dumpPathFieldID == NULL) {
        LOGE("error!!! dumpPathFieldID is null");
        return;
    }

    imgNumFieldID = env->GetFieldID(inputClass, "imgNum", "I");
    if (imgNumFieldID == NULL) {
        LOGE("error!!! imgNumFieldID is null");
        return;
    }
    inputFrameIdFieldID = env->GetFieldID(inputClass, "frameId", "I");
    if (inputFrameIdFieldID == NULL) {
        LOGE("error!!! inputFrameIdFieldID is null");
        return;
    }
    inputWidthFieldID = env->GetFieldID(inputClass, "width", "I");
    if (inputWidthFieldID == NULL) {
        LOGE("error!!! inputWidthFieldID is null");
        return;
    }
    inputHeightFieldID = env->GetFieldID(inputClass, "height", "I");
    if (inputHeightFieldID == NULL) {
        LOGE("error!!! inputHeightFieldID is null");
        return;
    }
    inputFormatFieldID = env->GetFieldID(inputClass, "format", "I");
    if (inputFormatFieldID == NULL) {
        LOGE("error!!! inputFormatFieldID is null");
        return;
    }
    inputTSFieldID = env->GetFieldID(inputClass, "timestamp", "J");
    if (inputTSFieldID == NULL) {
        LOGE("error!!! inputTSFieldID is null");
        return;
    }
    imagesFieldID = env->GetFieldID(inputClass, "images", "Ljava/util/List;");
    if (imagesFieldID == NULL) {
        LOGE("error!!! imagesFieldID is null");
        return;
    }
    resultTSFieldID = env->GetFieldID(outputClass, "timestamp", "J");
    if (resultTSFieldID == NULL) {
        LOGE("error!!! resultTSFieldID is null");
        return;
    }
    resultDataFieldID = env->GetFieldID(outputClass, "result", "[B");
    if (resultDataFieldID == NULL) {
        LOGE("error!!! resultDataFieldID is null");
        return;
    }
    resultFrameIDFieldID = env->GetFieldID(outputClass, "frameId", "I");
    if (resultFrameIDFieldID == NULL) {
        LOGE("error!!! resultValidFieldID is null");
        return;
    }
    resultFormatFieldID = env->GetFieldID(outputClass, "format", "I");
    if (resultFormatFieldID == NULL) {
        LOGE("error!!! resultFormatFieldID is null");
        return;
    }
    resultWidthFieldID = env->GetFieldID(outputClass, "width", "I");
    if (resultWidthFieldID == NULL) {
        LOGE("error!!! resultWidthFieldID is null");
        return;
    }
    resultHeightFieldID = env->GetFieldID(outputClass, "height", "I");
    if (resultHeightFieldID == NULL) {
        LOGE("error!!! resultHeightFieldID is null");
        return;
    }
    listGetMethodID = env->GetMethodID(listClass, "get", "(I)Ljava/lang/Object;");
    if (listGetMethodID == NULL) {
        LOGE("error!!! listGetMethodID null");
        return;
    }

    dataFieldID = env->GetFieldID(processImageClass, "data", "[B");
    if (dataFieldID == NULL) {
        LOGE("error!!! dataFieldID is null");
        return;
    }
}