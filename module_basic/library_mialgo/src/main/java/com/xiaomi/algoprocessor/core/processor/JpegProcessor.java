package com.xiaomi.algoprocessor.core.processor;

import static com.xiaomi.algoprocessor.core.processor.ProcessParams.TOKEN;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.PROCESSOR_TYPE_JPEG;

import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;

import com.xiaomi.algoprocessor.core.utils.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xiaomi.algoprocessor.core.utils.WatermarkUtil;

public class JpegProcessor extends BaseProcessor {
    private static final String TAG = "JpegProcessor";

    public JpegProcessor(Context context, ProcessorListener listener) {
        super(context, listener, PROCESSOR_TYPE_JPEG);
    }

    @Deprecated
    public boolean process(final String dir, @Nullable String outputPath, final String ldcPath, final String token) throws RemoteException {
        return process(dir, outputPath, ldcPath, token, WatermarkUtil.WATERMARK_TYPE_NONE);
    }


    @Deprecated
    public boolean process(final String dir, @Nullable String outputPath, final String ldcPath, final String token, final int watermarkType) throws RemoteException {
        if (mProcessor != null) {
            Log.i(TAG, "process jpeg " + token);
            return mProcessor.processJpeg(mProcessorId, dir, outputPath, ldcPath, token, watermarkType);
        }
        Log.e(TAG, "process null processor");
        return false;
    }

    /**
     * 使用 ProcessParams.Builder来构建Bundle
     * <p>
     * ProcessParams.Builder builder = new ProcessParams.Builder();
     * builder.setInputDir(dir).setOutputPath(outputPath).setLdcPath(ldcPath);
     * .setToken(token);
     * <p>
     * 下面是一些可选的参数
     * builder.setWatermarkType(watermarkType);//是否添加水印，默认不添加
     * builder.setOutputImageType(outputImageType);//输出是jpeg还是heic，默认是jpeg.
     * builder.setUnrevisedPath(unrevisedPath);//输出未做水平矫正的图片路径，默认为空
     * Bundle bundle = builder.build();
     *
     * @param bundle
     * @return
     */
    public boolean process(@NonNull Bundle bundle) throws RemoteException {
        if (mProcessor != null) {
            Log.i(TAG, "process jpeg bundle " + bundle.getString(TOKEN));
            return mProcessor.processJpegBundle(mProcessorId, bundle);
        }
        Log.e(TAG, "process jpeg bundle null processor");
        return false;
    }

    public boolean genDefaultImage(@NonNull Bundle bundle) throws RemoteException {
        if (mProcessor != null) {
            Log.i(TAG, "genDefaultImage " + bundle.getString(TOKEN));
            return mProcessor.genDefaultImage(mProcessorId, bundle);
        }
        Log.e(TAG, "genDefaultImage null processor");
        return false;
    }
}
