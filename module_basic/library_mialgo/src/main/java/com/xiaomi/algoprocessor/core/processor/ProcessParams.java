package com.xiaomi.algoprocessor.core.processor;

import android.os.Bundle;

/**
 * 并不想做成Parcel，内部使用Bundle更有扩展性, 谁知道以后会有什么乱七八糟，变来变去的需求
 */
public class ProcessParams {
    //为了兼容，0作为jpeg的默认值
    public static final int OUTPUT_IMAGE_TYPE_JPEG = 0;
    public static final int OUTPUT_IMAGE_TYPE_HEIC = 1;

    public static final String DIR = "dir";
    public static final String OUT_PATH = "outPath";

    public static final String OUT_UNREVISED_PATH = "outUnrevisedPath";
    public static final String LDC_PATH = "ldcPath";
    public static final String TOKEN = "token";
    public static final String WATERMARK_TYPE = "watermarkType";
    public static final String DELAY_NUM = "delayNum";
    public static final String OUTPUT_WIDTH = "outputWidth";
    public static final String OUTPUT_HEIGHT = "outputHeight";

    /**
     * 值是OUTPUT_IMAGE_TYPE_JPEG 和 OUTPUT_IMAGE_TYPE_HEIC 二选一
     */
    public static final String OUTPUT_IMAGE_TYPE = "outputImageType";

    public static class Builder {
        Bundle mBundle;

        public Builder() {
            mBundle = new Bundle();
        }

        public Builder setInputDir(String dir) {
            mBundle.putString(DIR, dir);
            return this;
        }

        public Builder setOutputPath(String outPath) {
            mBundle.putString(OUT_PATH, outPath);
            return this;
        }

        public Builder setUnrevisedPath(String outUnrevisedPath) {
            mBundle.putString(OUT_UNREVISED_PATH, outUnrevisedPath);
            return this;
        }

        public Builder setLdcPath(String ldcPath) {
            mBundle.putString(LDC_PATH, ldcPath);
            return this;
        }

        public Builder setToken(String token) {
            mBundle.putString(TOKEN, token);
            return this;
        }

        public Builder setWatermarkType(int watermarkType) {
            mBundle.putInt(WATERMARK_TYPE, watermarkType);
            return this;
        }

        public Builder setDelayNum(int delayNum) {
            mBundle.putInt(DELAY_NUM, delayNum);
            return this;
        }

        public Builder setOutputWidth(int outputWidth) {
            mBundle.putInt(OUTPUT_WIDTH, outputWidth);
            return this;
        }

        public Builder setOutputHeight(int outputHeight) {
            mBundle.putInt(OUTPUT_HEIGHT, outputHeight);
            return this;
        }

        public Builder setOutputImageType(int outputImageType) {
            mBundle.putInt(OUTPUT_IMAGE_TYPE, outputImageType);
            return this;
        }

        public Bundle build() {
            return mBundle;
        }

    }
}
