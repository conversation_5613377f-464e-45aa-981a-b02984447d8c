package com.xiaomi.algoprocessor.core.processor.server;

import static android.media.MediaFormat.KEY_LEVEL;
import static android.media.MediaFormat.KEY_PROFILE;

import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaFormat;
import com.xiaomi.algoprocessor.core.utils.Log;
import android.util.Range;

import com.xiaomi.algoprocessor.core.data.ProcessEntry;
import com.xiaomi.algoprocessor.core.data.ProcessInput;
import com.xiaomi.algoprocessor.core.data.ProcessOutput;
import com.xiaomi.algoprocessor.core.utils.SystemProperties;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public abstract class BaseProcessorServer {
    private static final String TAG = "BPServer";
    private static final String VERSION = "122507221";
    private static final String PROP_PRI_DEBUG = "com.chicodong.priv.debug";
    private static final String PROP_ALGO_DEBUG = "com.chicodong.priv.algo_debug";
    public static boolean DEBUG = SystemProperties.getBoolean(PROP_PRI_DEBUG, false);
    protected static boolean mEnableAlgoDebug = SystemProperties.getBoolean(PROP_ALGO_DEBUG, false);
    protected static final boolean DUMP_ENABLE = false;

    public static final int PROCESSOR_TYPE_JPEG = 1;
    public static final int PROCESSOR_TYPE_VIDEO = 2;

    public static final String KEY_PROCESSOR_TYPE = "processor_type";

    public static final int ERROR_CODE_UNKNOWN = 0;
    public static final int ERROR_CODE_INVALID_INPUT = 1;
    public static final int ERROR_CODE_NO_TRACK = 10;
    public static final int ERROR_CODE_CREATE_EXTRACTOR = 11;
    public static final int ERROR_CODE_PROCESS_NATIVE = 12;
    public static final int ERROR_CODE_CREATE_DECODER = 13;

    public static final int ERROR_CODE_CREATE_ENCODER = 14;

    public static final int ERROR_CODE_DECODE_PROCESSING = 15;

    public static final int ERROR_CODE_ENCODE_PROCESSING = 16;

    public static final int ERROR_CODE_UNSUPPORTED_RESOLUTION = 17;

    public static final int ERROR_CODE_MEMORY_NOT_ENOUGH = 18;

    public static final int ERROR_CODE_OUTPUT_EXISTS = 19;

    public static final int ERROR_CODE_HEVC_DECODER = 20;

    public static final int ERROR_CODE_INIT_ERROR = 21;

    public static final int ERROR_CODE_GYRO_LOST = 22;

    public static final int ERROR_CODE_NO_SPACE_LEFT = 23;

    public static final int SERVER_MSG_STOP_FOREGROUND = 1;
    public static final int SERVER_MSG_START_FOREGROUND = 2;

    protected ProcessorListenerServer mListener;

    protected ProcessEntry mProcessingEntry;
    protected String mDumpDir;
    protected int mCountL;

    protected final List<ProcessEntry> mQueue;
    protected int mLimit;

    public BaseProcessorServer(ProcessorListenerServer listener, String dumpDir, int limit) {
        mListener = listener;
        mDumpDir = dumpDir;
        mLimit = limit;
        mQueue = new ArrayList<>(mLimit);
        Log.e(TAG, "processor version " + VERSION);
    }

    public abstract int init(ProcessEntry entry);

    public abstract void uninit(long handle);

    //单独一次算法处理
    public abstract void process(final ProcessInput input, ProcessOutput output);

    public final void enableAlgoDebug(boolean enable) {
        Log.i(TAG, "enableAlgoDebug " + enable);
        mEnableAlgoDebug = enable;
        DEBUG = enable;
    }

    public final void setDump(String dumpDir) {
        Log.i(TAG, "setDump " + dumpDir);
        mDumpDir = dumpDir;
    }

    public abstract void quit();

    public abstract void remove(String clientId);

    public String getProcessingEntry() {
        synchronized (this) {
            if (mProcessingEntry != null) {
                return mProcessingEntry.token;
            }
        }
        return null;
    }

    public abstract boolean cancel(String clientId, String token);

    public final int getProcessingCount(String clientId) {
        if (clientId == null) {
            synchronized (this) {
                return mCountL;
            }
        } else {
            synchronized (this) {
                int count = 0;
                for (ProcessEntry entry : mQueue) {
                    if (entry.processorId.equals(clientId)) {
                        count++;
                    }
                }
                return count;
            }
        }
    }

    public boolean checkResolution(String mimeType, int width, int height, boolean needDecoder) {
        List<MediaCodecInfo> infos = getSupportedCodecs(mimeType, needDecoder);
        for (MediaCodecInfo info : infos) {
            if (isResolutionSupported(info, mimeType, width, height)) {
                return true;
            }
        }
        return false;
    }

    public Set<Integer> getSupportedCodecColorFormat(String mimeType, int width, int height, boolean needDecoder) {
        List<MediaCodecInfo> infos = getSupportedCodecs(mimeType, needDecoder);
        Set<Integer> result = new HashSet<>();
        for (MediaCodecInfo info : infos) {
            if (isResolutionSupported(info, mimeType, width, height)) {
                MediaCodecInfo.CodecCapabilities caps = info.getCapabilitiesForType(mimeType);
                int[] formats = caps.colorFormats;
                if (formats != null) {
                    for (int i : formats) {
                        result.add(i);
                    }
                }
            }
        }
        return result;
    }

    public static boolean isH265(String mimeType) {
        return MediaFormat.MIMETYPE_VIDEO_HEVC.equalsIgnoreCase(mimeType);
    }

    public static boolean supportH265(boolean decoder) {
        return getSupportedCodecs(MediaFormat.MIMETYPE_VIDEO_HEVC, decoder) != null;
    }

    private static List<MediaCodecInfo> getSupportedCodecs(String mimeType, boolean needDecoder) {
        List<MediaCodecInfo> result = new ArrayList<>();
        MediaCodecList mediaCodecList = new MediaCodecList(MediaCodecList.REGULAR_CODECS);
        MediaCodecInfo[] infos = mediaCodecList.getCodecInfos();

        for (MediaCodecInfo codecInfo : infos) {
            if (codecInfo.isEncoder() == needDecoder) {
                continue;
            }
            String[] types = codecInfo.getSupportedTypes();
            for (String type : types) {
                if (type.equalsIgnoreCase(mimeType)) {
                    result.add(codecInfo);
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 宽，高， 帧率， 比特率， profile, level, complexity, quality
     *
     * @param mimeType
     * @param width
     * @param height
     * @param format
     * @return
     */
    public MediaCodecInfo getEncodeCodecInfo(String mimeType, int width, int height, MediaFormat format) {
        List<MediaCodecInfo> infos = getSupportedCodecs(mimeType, false);
        MediaCodecInfo result = null;
        for (MediaCodecInfo info : infos) {
            if (isResolutionSupported(info, mimeType, width, height)) {
                MediaCodecInfo.CodecCapabilities cc = info.getCapabilitiesForType(mimeType);
                Log.i(TAG, "encoder name " + info.getName() + ", cname " + info.getCanonicalName());
                if (cc.isFormatSupported(format)) {
                    Log.i(TAG, "matched encoder " + info.getName());
                    result = info;
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 当自定义的一些参数不支持的时候， 我们在矫正一下， 保证帧率， 比特率，然后移除profile ,level, 如果还不行就毁灭吧
     *
     * @param mime
     * @param width
     * @param height
     * @param format
     */
    public void reviseEncoderMediaFormat(String mime, int width, int height, MediaFormat format) {
        List<MediaCodecInfo> infos = getSupportedCodecs(mime, false);
        for (MediaCodecInfo info : infos) {
            if (isResolutionSupported(info, mime, width, height)) {
                //随便找一个
                MediaCodecInfo.CodecCapabilities cc = info.getCapabilitiesForType(format.getString(MediaFormat.KEY_MIME));
                MediaCodecInfo.VideoCapabilities vc = cc.getVideoCapabilities();

                Range<Double> sfr = vc.getSupportedFrameRatesFor(width, height);
                Range<Integer> sbt = vc.getBitrateRange();

                if (!sfr.contains((double) (format.getInteger(MediaFormat.KEY_FRAME_RATE)))) {
                    format.setInteger(MediaFormat.KEY_FRAME_RATE, sfr.getUpper().intValue());
                }

                if (!sbt.contains(format.getInteger(MediaFormat.KEY_BIT_RATE))) {
                    format.setInteger(MediaFormat.KEY_BIT_RATE, sbt.getUpper());
                }

                //编码器随意使用支持的Profile和LEVEL吧，我们不指定了
                format.removeKey(KEY_PROFILE);
                format.removeKey(KEY_LEVEL);
                Log.i(TAG, "revise encoder name " + info.getName() + ", cname " + info.getCanonicalName()
                        + ", sfr " + sfr
                        + ", sbt " + sbt);
            }
        }
    }

    private static boolean isResolutionSupported(MediaCodecInfo codecInfo, String mimeType, int width, int height) {
        MediaCodecInfo.CodecCapabilities capabilities = codecInfo.getCapabilitiesForType(mimeType);
        if (capabilities == null) {
            Log.e(TAG, "not supported mime " + mimeType);
            return false;
        }

        MediaCodecInfo.VideoCapabilities videoCaps = capabilities.getVideoCapabilities();
        if (videoCaps == null) {
            Log.e(TAG, "codec not support video");
            return false;
        }

        return videoCaps.isSizeSupported(width, height);
    }

    public interface ProcessorListenerServer {
        void onProcessStarted(String processorId, String token);

        //算法处理某一帧失败
        void onProcessFailed(String processorId, String token, int errorCode);

        //算法处理某一帧成功
        void onProcessCompleted(String processorId, ProcessOutput output, String token);

        //正式开始处理整个文件, 最多一次回调
        void onProcessSequenceStarted(String processorId, String token);

        //整个流程被取消，最多一次回调
        void onProcessSequenceCanceled(String processorId, String token);

        //整个处理流程成功，且生成了对应的MP4文件，最多一次回调
        void onProcessSequenceCompleted(String processorId, String token, int extraCode);

        //整个流程被失败，不能生成MP4文件, 可能多次回调
        void onProcessSequenceFailed(String processorId, String token, int errorCode);
    }
}
