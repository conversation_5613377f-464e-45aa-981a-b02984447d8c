package com.xiaomi.algoprocessor.core.processor.server;

import static android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC;
import static androidx.core.app.NotificationCompat.VISIBILITY_SECRET;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.KEY_PROCESSOR_TYPE;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.PROCESSOR_TYPE_JPEG;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.PROCESSOR_TYPE_VIDEO;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.SERVER_MSG_START_FOREGROUND;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.SERVER_MSG_STOP_FOREGROUND;

import android.app.Notification;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteCallbackList;
import android.os.RemoteException;

import androidx.core.app.NotificationChannelCompat;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.xiaomi.algoprocessor.IProcessor;
import com.xiaomi.algoprocessor.IProcessorCallback;
import com.xiaomi.algoprocessor.R;
import com.xiaomi.algoprocessor.core.data.ProcessOutput;
import com.xiaomi.algoprocessor.core.utils.Log;

public class ProcecssorService extends Service {
    private static final String TAG = "ProcecssorService";
    private static final int QUEUE_LIMITED_SIZE = 30;

    public static final String CHANNEL_ID = "Procecssor_Service";
    public static final int NOTIFICATION_ID = 101;
    private JpegProcessorStub mJpegProcessor;
    private VideoProcessorStub mVideoProcessor;

    public ProcecssorService() {
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "onCreate");
        createNotificationChannel();
        mJpegProcessor = new JpegProcessorStub(this);
        mVideoProcessor = new VideoProcessorStub(this);
    }

    @Override
    public int onStartCommand(final Intent intent, final int flags, final int startId) {
        Log.i(TAG, "onStartCommand");
        startForeground();
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public IBinder onBind(Intent intent) {
        startForeground();
        int processorType = intent.getIntExtra(KEY_PROCESSOR_TYPE, 0);
        Log.i(TAG, "onBind " + processorType);
        if (processorType == PROCESSOR_TYPE_JPEG) {
            return mJpegProcessor;
        } else if (processorType == PROCESSOR_TYPE_VIDEO) {
            return mVideoProcessor;
        } else {
            return null;
        }
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "onDestroy");
        stopForeground(STOP_FOREGROUND_REMOVE);
        mJpegProcessor.quit();
        mVideoProcessor.quit();
        super.onDestroy();
    }

    @Override
    public void onTimeout(final int startId) {
        Log.i(TAG, "onTimeout");
        stopSelf();
        super.onTimeout(startId);
    }

    boolean tryStopForeground() {
        int count = getTotalProcessingCount();
        Log.e(TAG, "try stop while processing count " + count);
        if (count == 0) {
            stopForeground(STOP_FOREGROUND_REMOVE);
            return true;
        }
        return false;
    }

    public int getTotalProcessingCount() {
        int count = 0;
        if (mVideoProcessor != null) {
            try {
                count += mVideoProcessor.getProcessingCount(null);
            } catch (RemoteException e) {
                Log.e(TAG, "error video getTotalProcessingCount: " + e.getMessage());
            }
        }
        if (mJpegProcessor != null) {
            try {
                count += mJpegProcessor.getProcessingCount(null);
            } catch (RemoteException e) {
                Log.e(TAG, "error jpeg getTotalProcessingCount: " + e.getMessage());
            }
        }
        return count;
    }

    /**
     * 该方法很大概率不准，因为在处理失败的时候，我们在很多情况下是先回调，然后在置空processing entry
     *
     * @return
     */
    private boolean isProcessing() {
        if (mVideoProcessor != null) {
            return mVideoProcessor.getProcessingEntry() != null;
        }
        if (mJpegProcessor != null) {
            return mJpegProcessor.getProcessingEntry() != null;
        }
        return false;
    }

    private void createNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= 26) {
            CharSequence name = getString(R.string.foreground_channel_name);
            String description = getString(R.string.foreground_channel_name);
            int importance = NotificationManagerCompat.IMPORTANCE_DEFAULT;
            NotificationChannelCompat.Builder builder = new NotificationChannelCompat.Builder(CHANNEL_ID, importance);
            builder.setName(name)
                    .setDescription(description)
                    .setShowBadge(false)
                    .setLightsEnabled(false)
                    .setVibrationEnabled(false)
                    .setImportance(NotificationManagerCompat.IMPORTANCE_DEFAULT);
            // Register the channel with the system; you can't change the importance
            // or other notification behaviors after this
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(this.getApplicationContext());
            notificationManager.createNotificationChannel(builder.build());
        }
    }

    private Notification buildNotification() {
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this.getApplicationContext(), CHANNEL_ID);
        builder.setSmallIcon(R.mipmap.app_icon)
                .setContentTitle(getResources().getString(R.string.foreground_notification_name))
                .setContentText(getResources().getString(R.string.foreground_notification_content))
                .setVisibility(VISIBILITY_SECRET)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);
        return builder.build();
    }

    private void startForeground() {
        try {
            startForeground(NOTIFICATION_ID, buildNotification(), FOREGROUND_SERVICE_TYPE_DATA_SYNC);
            Log.i(TAG, "startForeground");
        } catch (Exception e) {
            Log.e(TAG, "error!!! startForegorund " + e.getMessage());
        }

    }

    public static void startForegroundService(Context context) {
        Log.i(TAG, "startForegroundService ProcecssorService");
        Intent intent = new Intent(context, ProcecssorService.class);
        context.startForegroundService(intent);
    }

    private static class VideoProcessorStub extends IProcessor.Stub {
        private final Context mContext;
        private volatile VideoProcessorServer mVideoProcessor;

        private final ListenerServer mListenerServer;

        private String mDumpDir;

        private VideoProcessorStub(Context context) {
            mContext = context;
            mListenerServer = new ListenerServer(context, PROCESSOR_TYPE_VIDEO);
        }

        @Override
        public boolean processJpeg(final String processorId, final String dir, final String outputPath, final String ldcPath, final String token, final int watermarkType) throws RemoteException {
            return false;
        }

        @Override
        public boolean processVideo(final String processorId, final String dir, final String outPath, final String ldcPath, final String token, final int delayNum, final int outputWidth, final int outputHeight) throws RemoteException {
            if (mVideoProcessor == null) {
                synchronized (this) {
                    if (mVideoProcessor == null) {
                        mVideoProcessor = new VideoProcessorServer(mContext.getApplicationContext(), mListenerServer, QUEUE_LIMITED_SIZE, mDumpDir);
                    }
                }
            }
            return mVideoProcessor.process(processorId, dir, outPath, ldcPath, token, delayNum, outputWidth, outputHeight);
        }

        @Override
        public boolean processJpegBundle(final String processorId, final Bundle bundle) throws RemoteException {
            return false;
        }

        @Override
        public boolean genDefaultImage(final String processorId, final Bundle bundle) throws RemoteException {
            return false;
        }

        @Override
        public boolean processVideoBundle(final String processorId, final Bundle bundle) throws RemoteException {
            if (mVideoProcessor == null) {
                synchronized (this) {
                    if (mVideoProcessor == null) {
                        mVideoProcessor = new VideoProcessorServer(mContext.getApplicationContext(), mListenerServer, QUEUE_LIMITED_SIZE, mDumpDir);
                    }
                }
            }
            return mVideoProcessor.processBundle(processorId, bundle);
        }

        @Override
        public void setDump(final String dump) throws RemoteException {
            mDumpDir = dump;
            if (mVideoProcessor != null) {
                mVideoProcessor.setDump(mDumpDir);
            }
        }

        @Override
        public void setDebug(final boolean debug) throws RemoteException {
            if (mVideoProcessor != null) {
                mVideoProcessor.enableAlgoDebug(debug);
            }
        }

        @Override
        public boolean cancel(final String processorId, final String token) throws RemoteException {
            boolean result = false;
            if (mVideoProcessor != null) {
                result = mVideoProcessor.cancel(processorId, token);
            }
            return result;
        }

        @Override
        public void registerCallback(IProcessorCallback callback) {
            mListenerServer.addListener(callback);
        }

        @Override
        public void unregisterCallback(IProcessorCallback callback) {
            mListenerServer.removeListener(callback);
        }

        @Override
        public void notifyClientQuit(final String processorId, final boolean keep) throws RemoteException {
            if (!keep) {
                if (mVideoProcessor != null) {
                    mVideoProcessor.remove(processorId);
                }
            }
        }

        @Override
        public int getProcessingCount(final String processorId) throws RemoteException {
            int count = 0;
            if (mVideoProcessor != null) {
                count = mVideoProcessor.getProcessingCount(processorId);
            }
            return count;
        }

        String getProcessingEntry() {
            if (mVideoProcessor != null) {
                return mVideoProcessor.getProcessingEntry();
            }
            return null;
        }

        @Override
        public boolean sendMessage(final int what, final Bundle args) throws RemoteException {
            switch (what) {
                case SERVER_MSG_START_FOREGROUND: {
                    ProcecssorService service = ((ProcecssorService) mContext);
                    service.startForeground();
                    return true;
                }
                case SERVER_MSG_STOP_FOREGROUND: {
                    ProcecssorService service = ((ProcecssorService) mContext);
                    return service.tryStopForeground();
                }
                default:
                    return true;
            }
        }

        public void quit() {
            if (mVideoProcessor != null) {
                mVideoProcessor.quit();
                mVideoProcessor = null;
            }
        }
    }

    private static class JpegProcessorStub extends IProcessor.Stub {
        private final Context mContext;
        private volatile JpegProcessorServer mJpegProcessor;

        private final ListenerServer mListenerServer;

        private String mDumpDir;

        private JpegProcessorStub(Context context) {
            mContext = context;
            mListenerServer = new ListenerServer(context, PROCESSOR_TYPE_JPEG);
        }

        @Override
        public boolean processJpeg(final String processorId, final String dir, final String outputPath, final String ldcPath, final String token, final int watermarkType) throws RemoteException {
            if (mJpegProcessor == null) {
                synchronized (this) {
                    if (mJpegProcessor == null) {
                        mJpegProcessor = new JpegProcessorServer(mContext.getApplicationContext(), mListenerServer, QUEUE_LIMITED_SIZE, mDumpDir);
                    }
                }
            }
            return mJpegProcessor.process(processorId, dir, outputPath, ldcPath, token, watermarkType);
        }

        @Override
        public boolean processVideo(final String processorId, final String dir, final String outPath, final String ldcPath, final String token, final int delayNum, final int outputWidth, final int outputHeight) throws RemoteException {
            return false;
        }

        @Override
        public boolean processJpegBundle(final String processorId, final Bundle bundle) throws RemoteException {
            if (mJpegProcessor == null) {
                synchronized (this) {
                    if (mJpegProcessor == null) {
                        mJpegProcessor = new JpegProcessorServer(mContext.getApplicationContext(), mListenerServer, QUEUE_LIMITED_SIZE, mDumpDir);
                    }
                }
            }
            return mJpegProcessor.processBundle(processorId, bundle);
        }

        @Override
        public boolean genDefaultImage(final String processorId, final Bundle bundle) throws RemoteException {
            if (mJpegProcessor == null) {
                synchronized (this) {
                    if (mJpegProcessor == null) {
                        mJpegProcessor = new JpegProcessorServer(mContext.getApplicationContext(), mListenerServer, QUEUE_LIMITED_SIZE, mDumpDir);
                    }
                }
            }
            return mJpegProcessor.genDefaultImage(processorId, bundle);
        }

        @Override
        public boolean processVideoBundle(final String processorId, final Bundle bundle) throws RemoteException {
            return false;
        }

        @Override
        public void setDump(final String dump) throws RemoteException {
            mDumpDir = dump;
            if (mJpegProcessor != null) {
                mJpegProcessor.setDump(mDumpDir);
            }
        }

        @Override
        public void setDebug(final boolean debug) throws RemoteException {
            if (mJpegProcessor != null) {
                mJpegProcessor.enableAlgoDebug(debug);
            }
        }

        @Override
        public boolean cancel(final String processorId, final String token) throws RemoteException {
            boolean result = false;
            if (mJpegProcessor != null) {
                result = mJpegProcessor.cancel(processorId, token);
            }
            return result;
        }

        @Override
        public void registerCallback(IProcessorCallback callback) {
            mListenerServer.addListener(callback);
        }

        @Override
        public void unregisterCallback(IProcessorCallback callback) {
            mListenerServer.removeListener(callback);
        }

        @Override
        public void notifyClientQuit(final String processorId, final boolean keep) throws RemoteException {
            if (!keep) {
                if (mJpegProcessor != null) {
                    mJpegProcessor.remove(processorId);
                }
            }
        }

        @Override
        public int getProcessingCount(final String processorId) throws RemoteException {
            int count = 0;
            if (mJpegProcessor != null) {
                count = mJpegProcessor.getProcessingCount(processorId);
            }
            return count;
        }

        String getProcessingEntry() {
            if (mJpegProcessor != null) {
                return mJpegProcessor.getProcessingEntry();
            }
            return null;
        }

        @Override
        public boolean sendMessage(final int what, final Bundle args) throws RemoteException {
            switch (what) {
                case SERVER_MSG_START_FOREGROUND: {
                    ProcecssorService service = ((ProcecssorService) mContext);
                    service.startForeground();
                    return true;
                }
                case SERVER_MSG_STOP_FOREGROUND: {
                    ProcecssorService service = ((ProcecssorService) mContext);
                    return service.tryStopForeground();
                }
                default:
                    return true;
            }
        }

        public void quit() {
            if (mJpegProcessor != null) {
                mJpegProcessor.quit();
                mJpegProcessor = null;
            }
        }
    }

    private static class ListenerServer implements BaseProcessorServer.ProcessorListenerServer {
        final RemoteCallbackList<IProcessorCallback> mCallbacks = new RemoteCallbackListInner();

        private final int mType;
        private final Context mContext;

        public ListenerServer(Context context, int type) {
            mContext = context;
            mType = type;
        }

        public void addListener(IProcessorCallback callback) {
            try {
                synchronized (mCallbacks) {
                    if (checkValid(callback.getProcessorId())) {
                        mCallbacks.register(callback);
                    }
                }
            } catch (RemoteException e) {
                Log.e(TAG, "error!!! addListener " + e.getMessage());
            }
        }

        public void removeListener(IProcessorCallback callback) {
            mCallbacks.unregister(callback);
        }

        private boolean checkValid(String id) {
            if (id == null) {
                Log.e(TAG, "try to add a null processor");
                return false;
            }
            int cb = mCallbacks.beginBroadcast();
            for (int i = 0; i < cb; i++) {
                try {
                    String processorId = mCallbacks.getBroadcastItem(i).getProcessorId();
                    if (id.equals(processorId)) {
                        Log.e(TAG, "try to add a duplicated processor");
                        mCallbacks.finishBroadcast();
                        return false;
                    }
                } catch (RemoteException e) {
                    Log.e(TAG, "error!!! check processorId " + e.getMessage());
                }
            }
            mCallbacks.finishBroadcast();
            return true;
        }

        @Override
        public void onProcessStarted(final String processorId, final String token) {
            //do nothing
        }

        @Override
        public void onProcessFailed(final String processorId, final String token, final int errorCode) {
            //do nothing
        }

        @Override
        public void onProcessCompleted(final String processorId, final ProcessOutput output, final String token) {
            //do nothing
        }

        @Override
        public void onProcessSequenceStarted(final String processorId, final String token) {
            Log.d(TAG, "sequence started " + token);
            ProcecssorService service = (ProcecssorService) mContext;
            service.startForeground();
            synchronized (mCallbacks) {
                int cb = mCallbacks.beginBroadcast();
                for (int i = 0; i < cb; i++) {
                    try {
                        IProcessorCallback callback = mCallbacks.getBroadcastItem(i);
                        String id = callback.getProcessorId();
                        if (id.equals(processorId)) {
                            callback.onProcessSequenceStarted(token);
                            mCallbacks.finishBroadcast();
                            return;
                        }
                    } catch (RemoteException e) {
                        Log.e(TAG, "error!!! onProcessSequenceStarted " + e.getMessage());
                    }
                }
                mCallbacks.finishBroadcast();
            }
        }

        @Override
        public void onProcessSequenceCanceled(final String processorId, final String token) {
            Log.d(TAG, "sequence canceled " + token);
            ProcecssorService service = (ProcecssorService) mContext;
            service.tryStopForeground();
            synchronized (mCallbacks) {
                int cb = mCallbacks.beginBroadcast();
                for (int i = 0; i < cb; i++) {
                    try {
                        IProcessorCallback callback = mCallbacks.getBroadcastItem(i);
                        String id = callback.getProcessorId();
                        if (id.equals(processorId)) {
                            callback.onProcessSequenceCanceled(token);
                            mCallbacks.finishBroadcast();
                            return;
                        }
                    } catch (RemoteException e) {
                        Log.e(TAG, "error!!! onProcessSequenceStarted " + e.getMessage());
                    }
                }
                mCallbacks.finishBroadcast();
            }
        }

        @Override
        public void onProcessSequenceCompleted(final String processorId, final String token, final int extraCode) {
            Log.d(TAG, "sequence completed " + token);
            ProcecssorService service = (ProcecssorService) mContext;
            service.tryStopForeground();
            synchronized (mCallbacks) {
                int cb = mCallbacks.beginBroadcast();
                for (int i = 0; i < cb; i++) {
                    try {
                        IProcessorCallback callback = mCallbacks.getBroadcastItem(i);
                        String id = callback.getProcessorId();
                        if (id.equals(processorId)) {
                            callback.onProcessSequenceCompleted(token, extraCode);
                            mCallbacks.finishBroadcast();
                            return;
                        }
                    } catch (RemoteException e) {
                        Log.e(TAG, "error!!! onProcessSequenceStarted " + e.getMessage());
                    }
                }
                mCallbacks.finishBroadcast();
            }
        }

        @Override
        public void onProcessSequenceFailed(final String processorId, final String token, final int errorCode) {
            Log.d(TAG, "sequence failed " + token);
            ProcecssorService service = (ProcecssorService) mContext;
            service.tryStopForeground();
            synchronized (mCallbacks) {
                int cb = mCallbacks.beginBroadcast();
                for (int i = 0; i < cb; i++) {
                    try {
                        IProcessorCallback callback = mCallbacks.getBroadcastItem(i);
                        String id = callback.getProcessorId();
                        if (id.equals(processorId)) {
                            callback.onProcessSequenceFailed(token, errorCode);
                            mCallbacks.finishBroadcast();
                            return;
                        }
                    } catch (RemoteException e) {
                        Log.e(TAG, "error!!! onProcessSequenceStarted " + e.getMessage());
                    }
                }
                mCallbacks.finishBroadcast();
            }
        }

        private static class RemoteCallbackListInner extends RemoteCallbackList<IProcessorCallback> {
            @Override
            public void onCallbackDied(final IProcessorCallback callback, final Object cookie) {
                super.onCallbackDied(callback, cookie);
            }

            @Override
            public void onCallbackDied(final IProcessorCallback callback) {
                super.onCallbackDied(callback);
                Log.e(TAG, "onCallbackDied for listener");
            }
        }
    }
}