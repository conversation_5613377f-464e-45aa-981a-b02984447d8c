package com.xiaomi.algoprocessor.core.processor.server;

import static android.media.MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible;
import static android.media.MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420PackedSemiPlanar;
import static android.media.MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Planar;
import static android.media.MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420SemiPlanar;
import static android.media.MediaFormat.COLOR_RANGE_FULL;
import static android.media.MediaFormat.COLOR_STANDARD_BT601_PAL;
import static android.media.MediaFormat.COLOR_TRANSFER_SDR_VIDEO;
import static android.media.MediaFormat.KEY_BITRATE_MODE;
import static android.media.MediaFormat.KEY_BIT_RATE;
import static android.media.MediaFormat.KEY_LEVEL;
import static android.media.MediaFormat.KEY_PRIORITY;
import static android.media.MediaFormat.KEY_PROFILE;
import static android.media.MediaFormat.KEY_QUALITY;
import static android.os.Environment.DIRECTORY_DOWNLOADS;
import static com.xiaomi.algoprocessor.core.data.ProcessEntry.PROCESS_STATUS_DECODING;
import static com.xiaomi.algoprocessor.core.data.ProcessEntry.PROCESS_STATUS_ENCODING;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.DELAY_NUM;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.DIR;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.LDC_PATH;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.OUTPUT_HEIGHT;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.OUTPUT_WIDTH;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.OUT_PATH;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.TOKEN;

import android.content.Context;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Process;
import android.os.SystemClock;
import android.util.Size;

import androidx.annotation.AnyThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xiaomi.algoprocessor.core.data.AdditionalInfo;
import com.xiaomi.algoprocessor.core.data.BufferToBeAlgo;
import com.xiaomi.algoprocessor.core.data.BufferToBeEncoded;
import com.xiaomi.algoprocessor.core.data.ProcessEntry;
import com.xiaomi.algoprocessor.core.data.ProcessImage;
import com.xiaomi.algoprocessor.core.data.ProcessInput;
import com.xiaomi.algoprocessor.core.data.ProcessOutput;
import com.xiaomi.algoprocessor.core.data.ThreadId;
import com.xiaomi.algoprocessor.core.utils.BlockingRunnable;
import com.xiaomi.algoprocessor.core.utils.FileUtil;
import com.xiaomi.algoprocessor.core.utils.ImageUtil;
import com.xiaomi.algoprocessor.core.utils.Log;
import com.xiaomi.algoprocessor.core.utils.LooperHandler;
import com.xiaomi.algoprocessor.core.utils.MemoryChecker;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

public class VideoProcessorServer extends BaseProcessorServer {
    private static final String TAG = "VPServer";
    private static final int FORMAT_NV12 = 1;
    private static final int FORMAT_NV21 = 2;
    private static final long QUEUE_TIMEOUT = 1000;
    public static final int CROP_RATIO = 80;
    public static final int BLUR_STRENGTH = 0;

    private static final int MSG_START_ALGO = 1;
    //先缓存1s的数据？
    private static final int MAX_BUFFER_SIZE = 4;

//    public static final String MTK_FORCE_PIXEL_FORMAT = "vendor.mtk.vdec.force.pixel.format.value";
//    public static final int MTK_FORCE_PIXEL_FORMAT_VALUE = 4096;

    private MediaExtractor mExtractor;
    private MediaCodec mMp4Decoder;
    private Mp4Encoder mMp4Encoder;
    private DecoderCallback mDecoderCallback;
    private EncoderCallback mEncoderCallback;

    private static Boolean supportH265Decoder;
    private static Boolean supportH265Encoder;

    private int mVideoTrackIndex = -1;
    private int mAudioTrackIndex = -1;
    private int mVideoTrackIndexInMuxer = -1;
    private int mAudioTrackIndexInMuxer = -1;
    private final LooperHandler mDecodeHandler;
    private final LooperHandler mEncodeHandler;
    private final AlgoHandler mAlgoHandler;
    private final BlockingQueue<BufferToBeAlgo> mAlgoQueue;
    private final BlockingQueue<BufferToBeEncoded> mEncodeQueue;
    boolean mNeedDump = true;

    //传入dumpDir就会开启dump， 否则不开启
    public VideoProcessorServer(@Nullable Context context, ProcessorListenerServer listener, int limit, @Nullable String dumpDir) {
        super(listener, dumpDir, limit);
        mAlgoQueue = new LinkedBlockingQueue<>(MAX_BUFFER_SIZE);
        mEncodeQueue = new LinkedBlockingQueue<>(MAX_BUFFER_SIZE);
        mDecodeHandler = new LooperHandler("_decode", Process.THREAD_PRIORITY_DEFAULT);
        mEncodeHandler = new LooperHandler("_encode", Process.THREAD_PRIORITY_DEFAULT);
        mAlgoHandler = new AlgoHandler("_algo", Process.THREAD_PRIORITY_DEFAULT);
    }

    @Override
    public int init(ProcessEntry entry) {
        Log.i(TAG, "init:E");
        //给handle赋值
        int result = initNative(entry);
        Log.i(TAG, "init:X " + result);
        return result;
    }

    @Override
    public void uninit(long handle) {
        Log.i(TAG, "uninit:E");
        uninitNative(handle);
        Log.i(TAG, "uninit:X");
    }

    //处理单帧
    @Override
    public void process(final ProcessInput input, ProcessOutput output) {
        if (input.handle > 0) {
            processNative(input.handle, input, output);
        }
    }

    public boolean processBundle(final String processorId, final Bundle bundle) {
        return process(processorId, bundle.getString(DIR), bundle.getString(OUT_PATH),
                bundle.getString(LDC_PATH), bundle.getString(TOKEN), bundle.getInt(DELAY_NUM),
                bundle.getInt(OUTPUT_WIDTH), bundle.getInt(OUTPUT_HEIGHT));
    }

    /**
     * 异步处理文件的入口
     *
     * @param dir
     * @param outPath
     * @param token
     * @param delayNum
     * @return
     */
    public boolean process(final String processorId, final String dir, final String outPath, final String ldcPath, final String token, int delayNum, int outputWidth, int outputHeight) {
        Log.i(TAG, "receive a process request " + token + ",output " + outPath + ", input dir " + dir);
        synchronized (this) {
            if (mLimit > 0 && mCountL >= mLimit) {
                Log.e(TAG, "process： exceed limit " + mLimit);
                return false;
            }
        }

        ProcessEntry entry = new ProcessEntry(token);
        entry.processorId = processorId;
        List<String> mp4 = FileUtil.filterSuffix(dir, FileUtil.SUFFIX_MP4);
        if (mp4.isEmpty()) {
            Log.e(TAG, "find no mp4 files");
            return false;
        } else if (mp4.size() > 1) {
            Log.e(TAG, "find more than one mp4 files");
            return false;
        }

        entry.inputPath = mp4.get(0);
        entry.outputPath = outPath;
        entry.dumpPath = mDumpDir;
        if (outputWidth > 0 && outputHeight > 0) {
            entry.outputWidth = outputWidth;
            entry.outputHeight = outputHeight;
        }

        //先尝试找一下，找不到也没有关系
        Size size = FileUtil.filterSizeFromDir(dir);
        if (size != null) {
            entry.width = size.getWidth();
            entry.height = size.getHeight();
        }

        File inputFile = new File(entry.inputPath);
        if (!inputFile.isFile()) {
            Log.e(TAG, "is not a normal mp4 file");
            return false;
        }
        if (!inputFile.canRead()) {
            Log.e(TAG, "can not read this mp4 file");
            return false;
        }

        File outputFile = new File(entry.outputPath);
        if (outputFile.exists()) {
            Log.e(TAG, "output file is already exist");
            return false;
        }

        if (ldcPath == null) {
            List<String> bins = FileUtil.filterFiles(dir, FileUtil.REGEX_NAME_LDC_BIN);
            if (bins.isEmpty()) {
                Log.e(TAG, "could not find ldc bin");
                return false;
            }
            if (bins.size() > 1) {
                Log.e(TAG, "find more than one ldc bins");
                return false;
            }
            entry.ldcPath = bins.get(0);
        } else {
            entry.ldcPath = ldcPath;
        }
        Log.e(TAG, "find bin path " + entry.ldcPath);
        if (entry.ldcPath == null) {
            return false;
        }

        List<String> metas = FileUtil.filterFiles(dir, FileUtil.REGEX_NAME_META);
        if (metas.isEmpty()) {
            Log.e(TAG, "could not find meta files");
            return false;
        }
        if (metas.size() > 1) {
            Log.e(TAG, "mete file more than one");
            return false;
        }
        entry.metaNum = metas.size();
        entry.metaPath = metas;

        List<String> gyros = FileUtil.filterFiles(dir, FileUtil.REGEX_NAME_GYRO);
        if (gyros.isEmpty()) {
            Log.e(TAG, "could not find gyro files");
            return false;
        }
        if (gyros.size() > 1) {
            Log.e(TAG, "gyro files more than one");
            return false;
        }
        entry.gyroNum = gyros.size();
        entry.gyroPath = gyros;

        entry.delayNum = delayNum;
        entry.blurStrength = BLUR_STRENGTH;

        synchronized (this) {
            //mCountL 和 mQueue.size保持同步了
            synchronized (entry.lock) {
                if (entry.cancel) {
                    Log.e(TAG, "canceled while inserting task");
                    return false;
                }
            }
            mQueue.add(entry);
            mCountL++;
            if (mCountL == 1) {
                tryHandleNext();
            }
        }
        return true;
    }

    @AnyThread
    private void tryHandleNext() {
        Log.i(TAG, "tryHandleNext");
        synchronized (this) {
            if (mProcessingEntry != null) {
                Log.i(TAG, "tryHandleNext already processing " + mProcessingEntry.token);
                return;
            }
        }

        //开启处理新文件之前，清理一下现场，目前只保留三个线程
        //总感觉复用线程会有潜在bug, 毕竟不知道在interrupt的时候，BQ是否阻塞在put/take中
        //如果阻塞的话， 线程被打断，符合预期
        //如果没有阻塞的话， 线程无法被打断，然后我们会重置线程的interrupt, 等到线程执行到 BQ的 put/take方法的时候，依然会阻塞
        //务必要等线程在BQ的put/take方法中阻塞的时候在调用reset方法
        reset();
        synchronized (this) {
            if (mProcessingEntry != null) {
                Log.i(TAG, "tryHandleNext already processing2");
                return;
            }
            if (mCountL > 0) {
                mCountL--;
                ProcessEntry entry = mQueue.remove(0);
                //每次处理前都要先init，获取handle
                mProcessingEntry = entry;
                sendProcessRequest(entry);
            }
        }
    }

    @AnyThread
    private void sendProcessRequest(final ProcessEntry entry) {
        Log.e(TAG, "sendProcessRequest");
        mDecodeHandler.post(() -> {
            handleProcessRequest(entry);
        });
    }

    @ThreadId("decode")
    private void handleProcessRequest(final ProcessEntry entry) {
        Log.i(TAG, "process:E handleProcessRequest");
        synchronized (this) {
            synchronized (entry.lock) {
                if (entry.cancel) {
                    uninitAndNext(entry);
                    Log.e(TAG, "handleProcessRequest: canceled not start");
                    mListener.onProcessSequenceCanceled(entry.processorId, entry.token);
                    return;
                }
            }
        }
        File outputFile = new File(entry.outputPath);
        if (outputFile.exists()) {
            Log.e(TAG, "error!!! handleProcessRequest: output file already exist");
            uninitAndNext(entry);
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_OUTPUT_EXISTS);
            return;
        }
        resetIndex();
        if (reInitExtractor(entry)) {
            if (startDecoder(entry)) {
                //extractor 和 codec都准备好了，并开始处理数据
                Log.i(TAG, "handleProcessRequest: old algo runnable " + mAlgoHandler.mRunnable);
                mAlgoHandler.quitRunnable();
                mAlgoHandler.blockingRun(new AlgoRunnable(entry));
                synchronized (VideoProcessorServer.this) {
                    synchronized (entry.lock) {
                        entry.processStatus = PROCESS_STATUS_DECODING;
                        if (entry.cancel) {
                            reset();
                            uninitAndNext(entry);
                            mListener.onProcessSequenceCanceled(entry.processorId, entry.token);
                            return;
                        }
                    }
                }
                mListener.onProcessSequenceStarted(entry.processorId, entry.token);
            } else {
                Log.e(TAG, "handleProcessRequest: start decoder failed");
                releaseExtractorAndNext(entry);
                Log.i(TAG, "process:X failed");
            }
        } else {
            uninitAndNext(entry);
            Log.i(TAG, "process:X failed");
            Log.e(TAG, "error!!! handleProcessRequest, extractor error, token " + entry.token);
        }
    }

    private void uninitAndNext(ProcessEntry entry) {
        synchronized (VideoProcessorServer.this) {
            synchronized (entry.lock) {
                if (entry.handle != 0) {
                    uninit(entry.handle);
                    entry.handle = 0;
                }
            }
            if (entry == mProcessingEntry) {
                mProcessingEntry = null;
            }
        }
        tryHandleNext();
    }

    @ThreadId("decode")
    private void releaseExtractorAndNext(ProcessEntry entry) {
        Log.i(TAG, "releaseExtractor " + entry.token);
        if (mExtractor != null) {
            mExtractor.release();
            mExtractor = null;
        }
        uninitAndNext(entry);
    }

    @ThreadId("decode")
    private boolean reInitExtractor(final ProcessEntry entry) {
        Log.i(TAG, "reInitExtractor " + entry.token);
        if (mExtractor != null) {
            mExtractor.release();
            mExtractor = null;
        }
        mExtractor = new MediaExtractor();
        try {
            mExtractor.setDataSource(entry.inputPath);
        } catch (Exception e) {
            Log.e(TAG, "error!!! reInitExtractor " + e);
            mExtractor.release();
            mExtractor = null;
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_CREATE_EXTRACTOR);
            return false;
        }

        for (int i = 0; i < mExtractor.getTrackCount(); i++) {
            MediaFormat format = mExtractor.getTrackFormat(i);
            String mime = format.getString(MediaFormat.KEY_MIME);
            if (mime.startsWith("video/")) {
                mVideoTrackIndex = i;
            } else if (mime.startsWith("audio/")) {
                mAudioTrackIndex = i;
            }
        }

        if (mVideoTrackIndex == -1 || mAudioTrackIndex == -1) {
            mExtractor.release();
            mExtractor = null;
            Log.e(TAG, "reInitExtractor invalid videoTrack " + mVideoTrackIndex + ", or audioTrack " + mAudioTrackIndex);
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_NO_TRACK);
            return false;
        } else {
            mExtractor.selectTrack(mVideoTrackIndex);
        }
        return true;
    }

    private void resetIndex() {
        mVideoTrackIndex = -1;
        mAudioTrackIndex = -1;
        mVideoTrackIndexInMuxer = -1;
        mAudioTrackIndexInMuxer = -1;
    }

    @ThreadId("decode")
    private boolean startDecoder(final ProcessEntry entry) {
        Log.i(TAG, "startDecoder " + entry.token);
        if (mDecoderCallback != null) {
            mDecoderCallback.stop();
            mDecoderCallback = null;
        }
        if (mEncoderCallback != null) {
            mEncoderCallback.stop();
            mEncoderCallback = null;
        }
        if (mMp4Decoder != null) {
            mMp4Decoder.release();
            mMp4Decoder = null;
        }
        if (mMp4Encoder != null) {
            mMp4Encoder.release();
            mMp4Encoder = null;
        }

        MediaFormat videoFormat = mExtractor.getTrackFormat(mVideoTrackIndex);
        String mime = videoFormat.getString(MediaFormat.KEY_MIME);
        int width = videoFormat.getInteger(MediaFormat.KEY_WIDTH, entry.width);
        int height = videoFormat.getInteger(MediaFormat.KEY_HEIGHT, entry.height);
        Log.i(TAG, "startDecoder: decode mimeType " + mime + ",wxh " + width + "x" + height);

        if (isH265(mime)) {
            if (supportH265Decoder == null) {
                supportH265Decoder = supportH265(true);
            }
            if (!supportH265Decoder) {
                Log.e(TAG, "error!!! do not support hecv decoder");
                mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_HEVC_DECODER);
                return false;
            }
        }

        if (!MemoryChecker.checkAppHeapMemory(width * height * 3 / 2 * (MAX_BUFFER_SIZE * 2 + 2))) {
            Log.e(TAG, "error!!! not enough memory for " + entry.token);
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_MEMORY_NOT_ENOUGH);
            return false;
        }
        if (!checkResolution(mime, width, height, true)) {
            Log.e(TAG, "error!!! not supported resolution for " + width + "x" + height);
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_UNSUPPORTED_RESOLUTION);
            return false;
        }

        entry.width = width;
        entry.height = height;
        if (entry.outputHeight == 0 && entry.outputWidth == 0) {
            entry.outputWidth = width;
            entry.outputHeight = height;
        }
        //我也不知道out大于input会出现什么结果
        entry.cropW = entry.outputWidth == entry.width ? CROP_RATIO : entry.outputWidth * 100 / entry.width;
        entry.cropH = entry.outputHeight == entry.height ? CROP_RATIO : entry.outputHeight * 100 / entry.height;
        //decoder启动之前要初始化算法，否则decoder吐出结果解码数据的时候， 算法handle可能为空
        entry.enableAlgoDebug = mEnableAlgoDebug;

        //不需要传handler,就在decode线程回调
        //videoFormat.setInteger(MTK_FORCE_PIXEL_FORMAT, MTK_FORCE_PIXEL_FORMAT_VALUE);
        Set<Integer> colorFormats = getSupportedCodecColorFormat(mime, entry.width, entry.height, true);
        if (colorFormats.contains(COLOR_FormatYUV420SemiPlanar)) {
            entry.decoderColorFormat = COLOR_FormatYUV420SemiPlanar;
            Log.i(TAG, "decoder format semi planar");
        } else if (colorFormats.contains(COLOR_FormatYUV420Planar)) {
            entry.decoderColorFormat = COLOR_FormatYUV420Planar;
            Log.i(TAG, "decoder format planar");
        } else {
            entry.decoderColorFormat = COLOR_FormatYUV420Flexible;
            Log.i(TAG, "decoder format flexible");
        }

        long duration = videoFormat.getLong(MediaFormat.KEY_DURATION, 0);
        int bitRate = videoFormat.getInteger(MediaFormat.KEY_BIT_RATE, 0);//没有比特率信息的
        int iFrame = videoFormat.getInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 0);
        int frameRate = videoFormat.getInteger(MediaFormat.KEY_FRAME_RATE, 0);//这个必须有
        entry.frameRate = frameRate;

        int colorRange = videoFormat.getInteger(MediaFormat.KEY_COLOR_RANGE, COLOR_RANGE_FULL);
        int colorStand = videoFormat.getInteger(MediaFormat.KEY_COLOR_STANDARD, COLOR_STANDARD_BT601_PAL);
        int colorTrans = videoFormat.getInteger(MediaFormat.KEY_COLOR_TRANSFER, COLOR_TRANSFER_SDR_VIDEO);
        int colorFormat = videoFormat.getInteger(MediaFormat.KEY_COLOR_FORMAT, 0);
        int orientationHint = videoFormat.getInteger(MediaFormat.KEY_ROTATION, 0);//也没有方向信息的
        int profile = videoFormat.getInteger(KEY_PROFILE, 0);
        int level = videoFormat.getInteger(KEY_LEVEL, 0);
        int priority = videoFormat.getInteger(KEY_PRIORITY, -1);
        int quality = videoFormat.getInteger(KEY_QUALITY, 0);
        int btm = videoFormat.getInteger(KEY_BITRATE_MODE, -1);
        Log.e(TAG, "startDecoder: duration " + duration + ", bt " + bitRate
                + ", iFrame " + iFrame + ", profile " + profile + ", level " + level
                + ", priority " + priority + ", fr " + frameRate + ", range " + colorRange
                + ", stand " + colorStand + ", trans " + colorTrans
                + ", format " + colorFormat + ", ori " + orientationHint
                + ", qa " + quality + ", btm " + btm);

        videoFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, entry.decoderColorFormat);

        if (init(entry) == 0) {
            try {
                mMp4Decoder = MediaCodec.createDecoderByType(mime);
            } catch (IOException e) {
                mMp4Decoder = null;
                Log.e(TAG, "error!!! create decoder " + e.getMessage());
                mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_CREATE_DECODER);
                return false;
            }
            mDecoderCallback = new DecoderCallback(entry, videoFormat);
            mMp4Decoder.setCallback(mDecoderCallback, mDecodeHandler);
            mMp4Decoder.configure(videoFormat, null, null, 0);
            mMp4Decoder.start();
            return true;
        } else {
            Log.e(TAG, "init error");
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_INIT_ERROR);
            return false;
        }
    }

    private boolean startEncoder(ProcessEntry entry, MediaFormat originFormat, MediaFormat videoFormat) {
        //初始化解码器
        String mime = originFormat.getString(MediaFormat.KEY_MIME);
        if (isH265(mime)) {
            if (supportH265Encoder == null) {
                supportH265Encoder = supportH265(false);
            }
            if (!supportH265Encoder) {
                //不支持H265,还是要退回到video/avc
                mime = MediaFormat.MIMETYPE_VIDEO_AVC;
            }
        }
        Log.i(TAG, "startEncoder " + entry.token + ",mime " + mime);

        long oldDuration = originFormat.getLong(MediaFormat.KEY_DURATION, 0);
        int bitRate = videoFormat.getInteger(MediaFormat.KEY_BIT_RATE,
                (int) calculateBitRate(entry.inputPath, oldDuration, entry.width, entry.height, isH265(mime)));//没有比特率信息的
        int iFrame = videoFormat.getInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);
        int oldFrameRate = originFormat.getInteger(MediaFormat.KEY_FRAME_RATE, 0);
        if (oldFrameRate == 0) {
            Log.i(TAG, "could not get fr from original file");
            oldFrameRate = 30;
        }
        int frameRate = videoFormat.getInteger(MediaFormat.KEY_FRAME_RATE, oldFrameRate);//这个必须有

        int colorRange = videoFormat.getInteger(MediaFormat.KEY_COLOR_RANGE, COLOR_RANGE_FULL);
        int colorStand = videoFormat.getInteger(MediaFormat.KEY_COLOR_STANDARD, COLOR_STANDARD_BT601_PAL);
        int colorTrans = videoFormat.getInteger(MediaFormat.KEY_COLOR_TRANSFER, COLOR_TRANSFER_SDR_VIDEO);
        int orientationHint = videoFormat.getInteger(MediaFormat.KEY_ROTATION, 0);//也没有方向信息的
        int profile = videoFormat.getInteger(KEY_PROFILE, 0);
        int level = videoFormat.getInteger(KEY_LEVEL, 0);
        int priority = videoFormat.getInteger(KEY_PRIORITY, -1);

        MediaFormat format = null;
        if (entry.outputWidth > 0 && entry.outputHeight > 0) {
            format = MediaFormat.createVideoFormat(mime, entry.outputWidth, entry.outputHeight);
        } else {
            format = MediaFormat.createVideoFormat(mime, entry.width, entry.height);
        }
        //TODO:这些参数要定制化，再次确认一下这些参数的合适值
        format.setInteger(MediaFormat.KEY_BIT_RATE, bitRate);
        format.setInteger(MediaFormat.KEY_FRAME_RATE, frameRate);

        Set<Integer> colorFormats = getSupportedCodecColorFormat(mime, entry.width, entry.height, false);
        int colorFormat = COLOR_FormatYUV420Flexible;
        if (entry.decoderColorFormat != 0 && colorFormats.contains(entry.decoderColorFormat)) {
            colorFormat = entry.decoderColorFormat;
        }
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT, colorFormat);
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, iFrame);
        format.setInteger(MediaFormat.KEY_COLOR_RANGE, colorRange);
        format.setInteger(MediaFormat.KEY_COLOR_STANDARD, colorStand);
        format.setInteger(MediaFormat.KEY_COLOR_TRANSFER, colorTrans);

        Log.e(TAG, "startEncoder: duration " + oldDuration + ", bt " + bitRate
                + ", iFrame " + iFrame + ", profile " + profile + ", level " + level
                + ", priority " + priority + ", fr " + frameRate + ", range " + colorRange
                + ", stand " + colorStand + ", trans " + colorTrans
                + ", format " + colorFormat + ", ori " + orientationHint);

        int oldProfile = originFormat.getInteger(KEY_PROFILE, 0);
        int oldLevel = originFormat.getInteger(KEY_LEVEL, 0);
        if (oldProfile != 0 && oldLevel != 0) {
            format.setInteger(KEY_PROFILE, oldProfile);
            format.setInteger(KEY_LEVEL, oldLevel);
        }
        MediaCodecInfo codecInfo = getEncodeCodecInfo(mime, entry.width, entry.height, format);
        if (codecInfo == null) {
            //如果指定的参数不符合，就在尝试更正一下
            reviseEncoderMediaFormat(mime, entry.width, entry.height, format);
        }

        try {
            mMp4Encoder = new Mp4Encoder(mime, entry.outputPath);
        } catch (IOException e) {
            Log.e(TAG, "error!!! create mp4 encoder " + e.getMessage());
            //编码器初始化失败，记得释放解码器，我们原来的代码中有很多没有释放的
            if (mMp4Decoder != null) {
                mMp4Decoder.stop();
                mMp4Decoder.release();
                mMp4Decoder = null;
            }
            mMp4Encoder = null;
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_CREATE_ENCODER);
            return false;
        }

        //这个需要在编码器线程回调
        mEncoderCallback = new EncoderCallback(entry);
        mMp4Encoder.setCallback(mEncoderCallback, mEncodeHandler);

        EncoderInfo info = new EncoderInfo();
        info.format = format;
        info.orientationHint = orientationHint;
        mMp4Encoder.configure(info);
        mMp4Encoder.startCodec();
        return true;
    }

    private long calculateBitRate(String path, long duration, int width, int height, boolean h265) {
        int factor = h265 ? 8 : 10;
        long result = (long) width * height * factor;
        Log.e(TAG, "calculateBitRate1: " + result);
        if (duration == 0) {
            return result;
        }
        File file = new File(path);
        long length = file.length();
        long time = duration / 1000000;
        if (time == 0) {
            Log.e(TAG, "duration is too short " + duration);
            return result;
        }
        long estimate = (length * 8) / time;
        Log.e(TAG, "calculateBitRate2: " + estimate);
        return Math.max(result, estimate);
    }

    @ThreadId("algo")
    private void algoProcess(BufferToBeAlgo algoBuffer) {
        ProcessInput input = new ProcessInput();
        ProcessOutput output = new ProcessOutput();
        input.handle = algoBuffer.entry.handle;
        input.width = algoBuffer.entry.width;
        input.height = algoBuffer.entry.height;
        input.frameId = algoBuffer.frameId;
        //ts可能为0
        input.timestamp = algoBuffer.timestamp;
        if (algoBuffer.entry.decoderOutputFormat == COLOR_FormatYUV420SemiPlanar || algoBuffer.entry.decoderOutputFormat == COLOR_FormatYUV420Flexible) {
            //姑且先认为是NV12
            input.format = FORMAT_NV12;
        } else if (algoBuffer.entry.decoderOutputFormat == COLOR_FormatYUV420PackedSemiPlanar) {
            //姑且认为是NV21
            input.format = FORMAT_NV21;
        } else {
            Log.e(TAG, "unknown color format " + algoBuffer.entry.decoderOutputFormat);
        }

        output.handle = algoBuffer.entry.handle;
        output.width = algoBuffer.entry.outputWidth;
        output.height = algoBuffer.entry.outputHeight;

        if ((algoBuffer.flag & AdditionalInfo.FLAG_REISSUE) != 0) {
            input.imgNum = 0;
        } else {
            //如果是EOS帧，就不送算法了,这肯定是最后一帧
            if ((algoBuffer.flag & AdditionalInfo.FLAG_EOS) != 0) {
                enqueueBufferToBeEncoded(algoBuffer.entry, output, AdditionalInfo.FLAG_EOS);
                return;
            } else {
                input.imgNum = 1;

                ProcessImage image = new ProcessImage();
                image.data = algoBuffer.data;
                List<ProcessImage> images = new ArrayList<>(1);
                images.add(image);
                input.images = images;
            }
        }

        mListener.onProcessStarted(algoBuffer.entry.processorId, algoBuffer.entry.token);
        int result = -1;
        if ((input.frameId - algoBuffer.entry.lastAlgoNum) != 1) {
            Log.e(TAG, "error!!! algo may encounter drop frame, last " + algoBuffer.entry.lastAlgoNum + ", incoming " + input.frameId);
        }
        algoBuffer.entry.lastAlgoNum = input.frameId;
        try {
            result = processNative(algoBuffer.entry.handle, input, output);
        } catch (Error e) {
            Log.i(TAG, "error processNative " + e.getMessage());
            mListener.onProcessFailed(algoBuffer.entry.processorId, algoBuffer.entry.token, ERROR_CODE_PROCESS_NATIVE);
        }
        if (result == 0) {
            //processNative返回true, 意味着 output 中 frameId, timestamp, result都被赋值了
            //所以缓存起来做mp4压缩, 务必注意这里info可能为空
            if (DUMP_ENABLE && mNeedDump) {
                mNeedDump = false;
                ImageUtil.dumpYUV(output.result, output.width, output.height,
                        mDumpDir == null ? Environment.getExternalStoragePublicDirectory(DIRECTORY_DOWNLOADS).getAbsolutePath() : mDumpDir, "" + output.frameId, "processed");
            }
            enqueueBufferToBeEncoded(algoBuffer.entry, output, AdditionalInfo.FLAG_NORMAL);
            mListener.onProcessCompleted(algoBuffer.entry.processorId, output, algoBuffer.entry.token);
        } else if (result == 10) {
            if (DEBUG) {
                Log.e(TAG, "processNative cached");
            }
            //processNative返回10只是代表缓存起来了，并不是代表出错了
            //mListener.onProcessFailed(algoBuffer.entry.processorId, algoBuffer.entry.token, ERROR_CODE_PROCESS_NATIVE);
        } else {
            if (result == 3003 || result == 3005) {
                algoBuffer.entry.extraResultCode = ERROR_CODE_GYRO_LOST;
            }
            mListener.onProcessFailed(algoBuffer.entry.processorId, algoBuffer.entry.token, ERROR_CODE_PROCESS_NATIVE);
        }
    }

    @ThreadId("decode")
    private void enqueueBufferToBeAlgo(ProcessEntry entry, int frameNum, byte[] yuv, long ts, @AdditionalInfo.DataFlag int flag) {
        BufferToBeAlgo buffer = new BufferToBeAlgo();
        buffer.data = yuv;
        buffer.entry = entry;
        buffer.frameId = frameNum;
        buffer.flag = flag;
        //第一帧和EOS 可能ts都为0
        buffer.timestamp = ts;

        try {
            boolean success = false;
            if (!entry.decoderStopped) {
                do {
                    success = mAlgoQueue.offer(buffer, QUEUE_TIMEOUT, TimeUnit.MILLISECONDS);
                } while (!success && !entry.decoderStopped);
            }
            if (DEBUG) {
                Log.e(TAG, "queue buffer to algo " + mAlgoQueue.size() + ",success " + success + ",fn " + frameNum);
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "error!!! enqueueAlgoBuffer " + e.getMessage());
        }
    }

    @ThreadId("algo")
    private void enqueueBufferToBeEncoded(ProcessEntry entry, ProcessOutput output, @AdditionalInfo.DataFlag int flag) {
        BufferToBeEncoded buffer = new BufferToBeEncoded();
        buffer.entry = entry;
        buffer.output = output;
        buffer.flag = flag;
        try {
            boolean success = false;
            if (!entry.decoderStopped) {
                do {
                    success = mEncodeQueue.offer(buffer, QUEUE_TIMEOUT, TimeUnit.MILLISECONDS);
                } while (!success && !entry.decoderStopped);
            }
            if (DEBUG) {
                Log.e(TAG, "queue buffer to encode " + mEncodeQueue.size() + ",success " + success + ",fn " + output.frameId);
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "error!!! enqueueEncodeBuffer " + e.getMessage());
        }
    }

    @ThreadId("encode")
    private boolean writeFrameToMp4(ByteBuffer buffer, MediaCodec.BufferInfo info) {
        if (mVideoTrackIndexInMuxer == -1) {
            Log.e(TAG, "error!!! writeFrameToMp4 invalid video muxer track");
            return false;
        }
        return mMp4Encoder.writeSampleData(mVideoTrackIndexInMuxer, buffer, info);
    }

    //该方法工作在encode线程
    private boolean processAudio() {
        Log.e(TAG, "processAudio");
        if (mAudioTrackIndex == -1) {
            return false;
        }
        if (mMp4Encoder == null) {
            return false;
        }
        if (mAudioTrackIndexInMuxer == -1) {
            return false;
        }

        mExtractor.unselectTrack(mAudioTrackIndex);
        mExtractor.selectTrack(mAudioTrackIndex);

        MediaFormat audioFormat = mExtractor.getTrackFormat(mAudioTrackIndex);
        int readByte = audioFormat.getInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 512);

        Log.i(TAG, "processAudio: desired audio buffer size " + readByte);
        ByteBuffer audioBuffer = ByteBuffer.allocate(readByte);
        MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();

        int sampleSize;
        while ((sampleSize = mExtractor.readSampleData(audioBuffer, 0)) >= 0) {
            info.presentationTimeUs = mExtractor.getSampleTime();
            info.size = sampleSize;
            info.flags = 0;

            audioBuffer.position(0);
            audioBuffer.limit(sampleSize);
            if (mMp4Encoder.writeSampleData(mAudioTrackIndexInMuxer, audioBuffer, info)) {
                mExtractor.advance();
            } else {
                mExtractor.unselectTrack(mAudioTrackIndex);
                return false;
            }
        }

        mExtractor.unselectTrack(mAudioTrackIndex);
        Log.i(TAG, "process:X");
        return true;
    }

    /**
     * 在处理新的请求之前调用， 会重置所有现场， 方便新请求的处理。
     * 目前会重置 编码线程， 解码线程，算法处理线程， 以及 媒体提取器，媒体编解码器，媒体混合器
     * 以及还会重置两级缓存Buffer.
     * <p>
     * 注意这里的线程， 否则会有问题。 现在没有发现什么问题， 等有问题在同步吧
     */
    private void reset() {
        Log.i(TAG, "reset: algo qSize " + mAlgoQueue.size() + ", encode qSize " + mEncodeQueue.size() + ",entry " + mProcessingEntry);
        mNeedDump = true;

        //Decode线程往一级缓存放数据，若BQ满了，则可能会堵塞住
        //若堵塞则尝试从堵塞中退出， 然后清空Looper，此时数据也不会往BQ中存放， 相当于丢弃了数据
        //如没有堵塞，则表达decode线程可能在做耗时操作，最终还是会堵塞在BQ的put方法中
        if (mDecoderCallback != null) {
            mDecoderCallback.stop();
            mDecoderCallback = null;
        }
        if (mMp4Decoder != null) {
            mDecodeHandler.postAndWait(new BlockingRunnable(() -> {
                if (mMp4Decoder != null) {
                    mMp4Decoder.stop();
                    mMp4Decoder.release();
                    mMp4Decoder = null;
                }
            }, new Object()), QUEUE_TIMEOUT, true);
        }
        mDecodeHandler.removeCallbacksAndMessages(null);

        //Encode线程从二级缓存取数据，若BQ为空，可能会堵塞住
        //若堵塞则尝试从堵塞中退出， 然后清空Looper，此时取出数据为null
        //若没有堵塞，则表明encode线程可能在做耗时操作，最终还是会堵塞在BQ的take方法中
        if (mEncoderCallback != null) {
            mEncoderCallback.stop();
            mEncoderCallback = null;
        }
        if (mMp4Encoder != null) {
            mEncodeHandler.postAndWait(new BlockingRunnable(() -> {
                if (mMp4Encoder != null) {
                    mMp4Encoder.release();
                    mMp4Encoder = null;
                }
            }, new Object()), QUEUE_TIMEOUT, true);
        }
        mEncodeHandler.removeCallbacksAndMessages(null);

        //Algo线程从一级缓存取数据，可能会堵塞， 然后送算法处理， 并放入二级缓存，可能会堵塞住
        //如果堵塞在了从一级缓存中取数据，那么interrupt之后, 不会在送算法处理，不会在往二级缓存送数据
        //如果堵塞在了二级缓存中放数据， 那么interrupt之后，不会在往二级缓存中放入数据
        AlgoRunnable runnable = mAlgoHandler.quitRunnable();
        //如果algo线程正在执行算法处理， 防止其他线程进行uninit操作
        mAlgoHandler.removeCallbacksAndMessages(null);
        if (runnable != null && Looper.myLooper() != mAlgoHandler.getLooper()) {
            Log.d(TAG, "algo wait done begin");
            runnable.waitQuitDone();
            Log.d(TAG, "algo wait done end");
        }

        if (mExtractor != null) {
            mExtractor.release();
            mExtractor = null;
        }
        //清空缓存
        mAlgoQueue.clear();
        mEncodeQueue.clear();
    }

    /**
     * 注意这里的线程，否则会有问题
     */
    @Override
    public void quit() {
        Log.i(TAG, "quit");
        synchronized (this) {
            if (!mQueue.isEmpty()) {
                mQueue.forEach(entry -> {
                    mListener.onProcessSequenceCanceled(entry.processorId, entry.token);
                });
            }
            mQueue.clear();
            mCountL = 0;
        }

        if (mExtractor != null) {
            mExtractor.release();
            mExtractor = null;
        }

        if (mDecoderCallback != null) {
            mDecoderCallback.stop();
            mDecoderCallback = null;
        }
        if (mMp4Decoder != null) {
            mDecodeHandler.postAndWait(new BlockingRunnable(() -> {
                if (mMp4Decoder != null) {
                    mMp4Decoder.stop();
                    mMp4Decoder.release();
                    mMp4Decoder = null;
                }
            }, new Object()), QUEUE_TIMEOUT, true);
        }
        mDecodeHandler.quit();

        if (mEncoderCallback != null) {
            mEncoderCallback.stop();
            mEncoderCallback = null;
        }
        if (mMp4Encoder != null) {
            mEncodeHandler.postAndWait(new BlockingRunnable(() -> {
                if (mMp4Encoder != null) {
                    mMp4Encoder.release();
                    mMp4Encoder = null;
                }
            }, new Object()), QUEUE_TIMEOUT, true);
        }
        mEncodeHandler.quit();

        AlgoRunnable runnable = mAlgoHandler.quitRunnable();
        if (runnable != null && Looper.myLooper() != mAlgoHandler.getLooper()) {
            runnable.waitQuitDone();
        }
        mAlgoHandler.quit();

        mEncodeQueue.clear();
        mAlgoQueue.clear();
        synchronized (this) {
            if (mProcessingEntry != null) {
                synchronized (mProcessingEntry.lock) {
                    if (mProcessingEntry.handle != 0) {
                        uninit(mProcessingEntry.handle);
                        mProcessingEntry.handle = 0;
                    }
                }
                mProcessingEntry = null;
            }
        }
    }

    @Override
    public void remove(final String clientId) {
        Log.i(TAG, "remove");
        synchronized (this) {
            mQueue.removeIf(entry -> entry.processorId.equals(clientId));
            mCountL = mQueue.size();
        }
    }

    @Override
    public boolean cancel(String clientId, String token) {
        Log.e(TAG, "cancel task " + token);
        boolean result = false;
        synchronized (this) {
            result = mQueue.removeIf(entry -> entry.processorId.equals(clientId) && entry.token.equals(token));
            mCountL = mQueue.size();
        }
        if (result) {
            Log.e(TAG, "cancel pending task " + token);
            mListener.onProcessSequenceCanceled(clientId, token);
            return result;
        }
        synchronized (this) {
            if (mProcessingEntry != null && mProcessingEntry.token.equals(token)) {
                Log.i(TAG, "cancel current task " + token);
                mDecodeHandler.doOrPost(() -> {
                    cancelInner(clientId, token);
                }, true);
                result = true;
            }
        }
        return result;
    }

    @ThreadId("_decode")
    private void cancelInner(String clientId, String token) {
        synchronized (VideoProcessorServer.this) {
            if (mProcessingEntry != null && mProcessingEntry.token.equals(token)) {
                synchronized (mProcessingEntry.lock) {
                    mProcessingEntry.cancel = true;
                    if (mProcessingEntry.processStatus == PROCESS_STATUS_DECODING || mProcessingEntry.processStatus == PROCESS_STATUS_ENCODING) {
                        Log.i(TAG, "reset current task " + token);
                        reset();
                        uninitAndNext(mProcessingEntry);
                        mListener.onProcessSequenceCanceled(clientId, token);
                    }
                }
            }
        }
    }

    private class DecoderCallback extends MediaCodec.Callback {
        ProcessEntry mEntry;

        MediaFormat mFormat;

        private int mFrameNum;
        private int mColorFormat = COLOR_FormatYUV420SemiPlanar;
        boolean mEOS;
        volatile boolean mStop;
        int mWidth;
        int mHeight;

        boolean needDump = true;

        String mOutputMime;

        long MAX_DROP_INTERNAL;

        long lastPresentationTime = -1L;

        boolean formatChanged;

        public DecoderCallback(ProcessEntry entry, MediaFormat format) {
            mEntry = entry;
            mFormat = format;
            if (entry.frameRate > 0) {
                MAX_DROP_INTERNAL = 1300_000L / entry.frameRate;
            }
        }

        @Override
        public void onInputBufferAvailable(@NonNull final MediaCodec codec, final int index) {
            if (mEOS || mStop) {
                return;
            }
            if (codec != mMp4Decoder) {
                Log.e(TAG, "decode onInputBufferAvailable maybe released");
                return;
            }
            ByteBuffer inputBuffer = codec.getInputBuffer(index);
            inputBuffer.clear();
            int bytesRead = mExtractor.readSampleData(inputBuffer, 0);
            long presentationTimeUs = mExtractor.getSampleTime();
            if (DEBUG) {
                Log.e(TAG, "decode onInputBufferAvailable, read size " + bytesRead);
            }
            if (bytesRead < 0) {
                codec.queueInputBuffer(index, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                mEOS = true;
            } else {
                codec.queueInputBuffer(index, 0, bytesRead, presentationTimeUs, 0);
                mExtractor.advance();
            }
        }

        @Override
        public void onOutputBufferAvailable(@NonNull final MediaCodec codec, final int index, @NonNull final MediaCodec.BufferInfo info) {
            if (mStop) {
                return;
            }
            if (codec != mMp4Decoder) {
                Log.e(TAG, "decode onOutputBufferAvailable maybe released");
                return;
            }
            ByteBuffer outputBuffer = codec.getOutputBuffer(index);

            if (DEBUG) {
                Log.e(TAG, "decode onOutputBufferAvailable " + outputBuffer + ", " + mWidth + "x" + mHeight + ",size " + info.size + ",offset " + info.offset + ",ts " + info.presentationTimeUs + ",flag " + info.flags);
            }
            byte[] yuv = null;
            if (info.size > 0 && outputBuffer != null) {
                outputBuffer.position(info.offset);
                outputBuffer.limit(info.offset + info.size);
                yuv = ImageUtil.convertToYUV(outputBuffer, mWidth, mHeight, info.size, mColorFormat);
                if (DUMP_ENABLE && needDump) {
                    needDump = false;
                    ImageUtil.dumpYUV(yuv, mWidth, mHeight,
                            mDumpDir == null ? Environment.getExternalStoragePublicDirectory(DIRECTORY_DOWNLOADS).getAbsolutePath() : mDumpDir, "" + mFrameNum, "unprocessed");
                }
            }
            //先归还Buffer
            codec.releaseOutputBuffer(index, false);
            if (MAX_DROP_INTERNAL > 0) {
                if (lastPresentationTime != -1L && info.presentationTimeUs != 0 && (info.presentationTimeUs - lastPresentationTime) >= MAX_DROP_INTERNAL) {
                    Log.e(TAG, "error!!! decoder may encounter drop frame, last " + lastPresentationTime + ", incoming " + info.presentationTimeUs);
                }
            }

            // 第一帧和EOS帧的presentationTimeUs可能为空
            if (info.presentationTimeUs != 0 || lastPresentationTime < 0) {
                lastPresentationTime = info.presentationTimeUs;
            }

            if ((info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                Log.e(TAG, "decode onOutputBufferAvailable EOS " + yuv);
                //最后一帧处理完了，直接销毁
                codec.stop();
                codec.release();
                mMp4Decoder = null;
                mEntry.eosPresentationTime = lastPresentationTime;

                //解码到最后一帧， 补发delayNum帧
                for (int i = 0; i < mEntry.delayNum; i++) {
                    enqueueBufferToBeAlgo(mEntry, mFrameNum++, null, 0, AdditionalInfo.FLAG_REISSUE);
                }
                //然后在发送EOS, 这里yuv也应该为null
                enqueueBufferToBeAlgo(mEntry, mFrameNum++, yuv, 0, AdditionalInfo.FLAG_EOS);
            } else if (yuv != null) {
                enqueueBufferToBeAlgo(mEntry, mFrameNum++, yuv, info.presentationTimeUs, AdditionalInfo.FLAG_NORMAL);
            } else {
                Log.e(TAG, "error!!! decoder onOutputBufferAvailable yuv is null while not EOS");
            }
        }

        @Override
        public void onError(@NonNull final MediaCodec codec, @NonNull final MediaCodec.CodecException e) {
            Log.i(TAG, "process:X failed, decode onError " + e.getMessage());
            uninitAndNext(mEntry);
            mListener.onProcessSequenceFailed(mEntry.processorId, mEntry.token, ERROR_CODE_DECODE_PROCESSING);
        }

        @Override
        public void onOutputFormatChanged(@NonNull final MediaCodec codec, @NonNull final MediaFormat format) {
            try {
                //突然发现这个方法在有的机型上会回调多次，比如某友商某加的机器上
                if (formatChanged) {
                    Log.e(TAG, "decode format changed more than one time");
                    return;
                }
                formatChanged = true;
                mColorFormat = format.getInteger(MediaFormat.KEY_COLOR_FORMAT, COLOR_FormatYUV420Flexible);
                mEntry.decoderOutputFormat = mColorFormat;
                mOutputMime = format.getString(MediaFormat.KEY_MIME);
                mWidth = format.getInteger(MediaFormat.KEY_WIDTH, mEntry.width);
                mHeight = format.getInteger(MediaFormat.KEY_HEIGHT, mEntry.height);
                Log.i(TAG, "decode onOutputFormatChanged colorFormat: " + mColorFormat + ", outputMime " + mOutputMime);
                synchronized (VideoProcessorServer.this) {
                    synchronized (mEntry.lock) {
                        if (!mEntry.cancel) {
                            if (!startEncoder(mEntry, mFormat, format)) {
                                Log.i(TAG, "process:X failed");
                                stop();
                                releaseExtractorAndNext(mEntry);
                            } else {
                                mEntry.processStatus = PROCESS_STATUS_ENCODING;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private void stop() {
            mStop = true;
            mEntry.decoderStopped = true;
        }
    }

    private class EncoderCallback extends MediaCodec.Callback {
        ProcessEntry mEntry;
        boolean mEOS;

        volatile boolean mStop;

        boolean isFirstFrame = true;

        boolean needDump = true;

        int mWidth;
        int mHeight;

        String mOutputMime;

        int lastEncodeNum = -1;

        volatile boolean formatChanged;

        List<OutputSample> mNotReadyList;

        public EncoderCallback(ProcessEntry entry) {
            mEntry = entry;
            mNotReadyList = new LinkedList<>();
        }

        @Override
        public void onInputBufferAvailable(@NonNull final MediaCodec codec, final int index) {
            if (codec != mMp4Encoder.mEncoder) {
                Log.e(TAG, "encode onInputBufferAvailable maybe released");
                return;
            }
            BufferToBeEncoded encodeBuffer = null;
            try {
                if (!mEOS && !mStop) {
                    //没有数据就先堵着吧
                    do {
                        encodeBuffer = mEncodeQueue.poll(QUEUE_TIMEOUT, TimeUnit.MILLISECONDS);
                    } while (encodeBuffer == null && !mEOS && !mStop);
                }
                if (DEBUG) {
                    if (encodeBuffer != null) {
                        Log.e(TAG, "encode onInputBufferAvailable, encode qSize " + mEncodeQueue.size() + ",encodeBuffer " + encodeBuffer + ",fn " + encodeBuffer.output.frameId);
                    } else {
                        Log.e(TAG, "encode onInputBufferAvailable, encode qSize " + mEncodeQueue.size() + ",why encodeBuffer null??");
                    }
                }
            } catch (InterruptedException e) {
                Log.e(TAG, "error!!! take from encode queue " + e.getMessage());
            }
            //编码时必须使用 encodeBuffer.out中携带的时间戳，不能使用 encodeBuffer.info中的时间戳，我是不是就不应该保存这个info?
            if (encodeBuffer != null && encodeBuffer.entry.handle != 0 && encodeBuffer.entry.handle == mEntry.handle) {
                if (((encodeBuffer.output.frameId - lastEncodeNum) != 1) && encodeBuffer.output.frameId != 0) {
                    Log.e(TAG, "error!!! encoder may encounter drop frame, last " + lastEncodeNum + ", incoming " + encodeBuffer.output.frameId);
                }
                lastEncodeNum = encodeBuffer.output.frameId;

                if ((encodeBuffer.flag & AdditionalInfo.FLAG_EOS) != 0) {
                    Log.e(TAG, "encode onInputBufferAvailable EOS");
                    ByteBuffer buffer = codec.getInputBuffer(index);
                    buffer.clear();
                    codec.queueInputBuffer(index, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                    mEOS = true;
                } else {
                    if (DEBUG) {
                        Log.i(TAG, "encode onInputBufferAvailable ts " + encodeBuffer.output.timestamp + ", size " + encodeBuffer.output.result.length);
                    }
                    if (encodeBuffer.output.result != null) {
                        //这里output.result必须有值，否则就是大的异常
                        ByteBuffer buffer = codec.getInputBuffer(index);
                        buffer.clear();
                        if (DUMP_ENABLE && needDump) {
                            ImageUtil.dumpYUV(encodeBuffer.output.result, encodeBuffer.output.width, encodeBuffer.output.height,
                                    mDumpDir == null ? Environment.getExternalStoragePublicDirectory(DIRECTORY_DOWNLOADS).getAbsolutePath() : mDumpDir, "" + encodeBuffer.output.frameId, "encode");
                            needDump = false;
                        }
                        buffer.put(encodeBuffer.output.result);
                        codec.queueInputBuffer(index, 0, encodeBuffer.output.result.length,
                                encodeBuffer.output.timestamp, isFirstFrame ? MediaCodec.BUFFER_FLAG_KEY_FRAME : 0);
                        isFirstFrame = false;
                    }
                }
            } else {
                Log.i(TAG, "encode onInputBufferAvailable: empty element or wrong owner");
            }
        }

        @Override
        public void onOutputBufferAvailable(@NonNull final MediaCodec codec, final int index, @NonNull final MediaCodec.BufferInfo info) {
            if (mStop) {
                return;
            }
            if (codec != mMp4Encoder.mEncoder) {
                Log.e(TAG, "encode onOutputBufferAvailable maybe released");
                return;
            }
            ByteBuffer outputBuffer = codec.getOutputBuffer(index);
            if (DEBUG) {
                Log.e(TAG, "encode onOutputBufferAvailable " + outputBuffer + ",size " + info.size + ",offset " + info.offset + ",ts " + info.presentationTimeUs + ", flag " + info.flags);
            }
            if (info.size > 0) {
                outputBuffer.position(info.offset);
                outputBuffer.limit(info.offset + info.size);
                if (!formatChanged) {
                    Log.d(TAG, "encode output available while format not ready " + info.flags);
                    synchronized (this) {
                        mNotReadyList.add(new OutputSample(outputBuffer, info));
                    }
                } else {
                    if (!writeFrameToMp4(outputBuffer, info)) {
                        Log.e(TAG, "encoder write sample failed " + info.flags);
                        codec.releaseOutputBuffer(index, false);
                        stop();
                        uninitAndNext(mEntry);
                        mListener.onProcessSequenceFailed(mEntry.processorId, mEntry.token, ERROR_CODE_NO_SPACE_LEFT);
                        return;
                    }
                }
            }
            codec.releaseOutputBuffer(index, false);
            if ((info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                if (processAudio()) {
                    uninitAndNext(mEntry);
                    mListener.onProcessSequenceCompleted(mEntry.processorId, mEntry.token, mEntry.extraResultCode);
                } else {
                    uninitAndNext(mEntry);
                    mListener.onProcessSequenceFailed(mEntry.processorId, mEntry.token, ERROR_CODE_NO_SPACE_LEFT);
                }
            }
        }

        @Override
        public void onError(@NonNull final MediaCodec codec, @NonNull final MediaCodec.CodecException e) {
            Log.e(TAG, "error!!! encoder " + e.getMessage());
            Log.i(TAG, "process:X failed");
            uninitAndNext(mEntry);
            mListener.onProcessSequenceFailed(mEntry.processorId, mEntry.token, ERROR_CODE_ENCODE_PROCESSING);
        }

        @Override
        public void onOutputFormatChanged(@NonNull final MediaCodec codec, @NonNull final MediaFormat format) {
            //友商的某些机器上， 该方法的回调晚于onOutputBufferAvailable的回调
            if (!formatChanged) {
                mOutputMime = format.getString(MediaFormat.KEY_MIME);
                mWidth = format.getInteger(MediaFormat.KEY_WIDTH, mEntry.width);
                mHeight = format.getInteger(MediaFormat.KEY_HEIGHT, mEntry.height);
                Log.i(TAG, "encoder onOutputFormatChanged " + mOutputMime + ", profile " + format.getInteger(KEY_PROFILE, 0)
                        + ",level " + format.getInteger(KEY_LEVEL, 0) + ", bt " + format.getInteger(KEY_BIT_RATE, 0)
                        + ", btm " + format.getInteger(KEY_BITRATE_MODE, 0));
                mVideoTrackIndexInMuxer = mMp4Encoder.addTrack(format);
                MediaFormat audioFormat = mExtractor.getTrackFormat(mAudioTrackIndex);
                mAudioTrackIndexInMuxer = mMp4Encoder.addTrack(audioFormat);
                mMp4Encoder.startMuxer();
                synchronized (this) {
                    if (!mNotReadyList.isEmpty()) {
                        for (OutputSample sample : mNotReadyList) {
                            if (!writeFrameToMp4(sample.buffer, sample.info)) {
                                Log.e(TAG, "encoder format changed write sample failed " + sample.info.flags);
                                mStop = true;
                                uninitAndNext(mEntry);
                                mListener.onProcessSequenceFailed(mEntry.processorId, mEntry.token, ERROR_CODE_NO_SPACE_LEFT);
                                break;
                            }
                        }
                        mNotReadyList.clear();
                    }
                }
            } else {
                Log.e(TAG, "encoder format change more than once");
            }
            formatChanged = true;
        }

        public void stop() {
            mStop = true;
            synchronized (this) {
                if (!mNotReadyList.isEmpty()) {
                    Log.i(TAG, "encode stop while output not empty");
                    mNotReadyList.clear();
                }
            }
        }
    }

    private static class OutputSample {
        ByteBuffer buffer;
        MediaCodec.BufferInfo info;

        OutputSample(ByteBuffer buffer, MediaCodec.BufferInfo info) {
            this.buffer = buffer;
            this.info = info;
        }
    }

    private class AlgoHandler extends LooperHandler {

        private volatile AlgoRunnable mRunnable;

        public AlgoHandler(final String name, final int priority) {
            super(name, priority);
        }

        @Override
        public void handleMessage(@NonNull final Message msg) {
            switch (msg.what) {
                case MSG_START_ALGO:
                    AlgoRunnable runnable = (AlgoRunnable) msg.obj;
                    runnable.run();
                    break;
                default:
                    break;
            }
        }

        public void blockingRun(AlgoRunnable runnable) {
            mRunnable = runnable;
            Message msg = obtainMessage();
            msg.what = MSG_START_ALGO;
            msg.obj = runnable;
            sendMessage(msg);
        }

        public AlgoRunnable quitRunnable() {
            AlgoRunnable runnable = mRunnable;
            if (mRunnable != null) {
                removeMessages(MSG_START_ALGO);
                mRunnable.stop();
                mRunnable = null;
            }
            return runnable;
        }

        public Runnable getRunnable() {
            return mRunnable;
        }
    }

    private class AlgoRunnable implements Runnable {
        private ProcessEntry mEntry;
        private volatile boolean mStop = false;
        private volatile boolean blockingRunning = false;

        public AlgoRunnable(ProcessEntry entry) {
            mEntry = entry;
        }

        @Override
        public void run() {
            while (!mStop) {
                blockingRunning = true;
                BufferToBeAlgo buffer = null;
                try {
                    do {
                        buffer = mAlgoQueue.poll(QUEUE_TIMEOUT, TimeUnit.MILLISECONDS);
                    } while (buffer == null && !mStop);
                    if (DEBUG && buffer != null) {
                        Log.e(TAG, "take from algo qSize " + mAlgoQueue.size() + ", buffer " + buffer + ",fn " + buffer.frameId);
                    }
                } catch (InterruptedException e) {
                    Log.e(TAG, "error!!! take from algo queue " + e.getMessage());
                }

                if (buffer != null && buffer.entry.handle != 0 && buffer.entry.handle == mEntry.handle) {
                    //这里主动stop一下？
                    algoProcess(buffer);
                    if ((buffer.flag & AdditionalInfo.FLAG_EOS) != 0) {
                        Log.e(TAG, "AlgoRunnable stopSelf by EOS");
                        stop();
                    }
                } else {
                    Log.i(TAG, "AlgoRunnable: empty element or wrong owner");
                }
            }
            blockingRunning = false;
            synchronized (this) {
                this.notifyAll();
            }
        }

        public void stop() {
            mStop = true;
        }

        public void waitQuitDone() {
            synchronized (this) {
                long now = SystemClock.elapsedRealtime();
                long end = now + QUEUE_TIMEOUT;
                while (blockingRunning && now < end) {
                    try {
                        this.wait(end - now);
                    } catch (InterruptedException e) {
                        Log.e(TAG, "retryAndBlock interrupted " + e.getMessage());
                    }
                    now = SystemClock.elapsedRealtime();
                }
            }
        }
    }

    public class Mp4Encoder {
        MediaCodec mEncoder;
        MediaMuxer mMuxer;

        boolean isCreated;

        private Mp4Encoder(String mime, String outPath) throws IOException {
            try {
                mEncoder = MediaCodec.createEncoderByType(mime);
            } catch (IOException e) {
                Log.e(TAG, "error!!! create encoder " + e.getMessage());
                throw new IOException(e);
            }

            try {
                mMuxer = new MediaMuxer(outPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            } catch (IOException e) {
                Log.e(TAG, "error!!! create muxer " + e.getMessage());
                if (mEncoder != null) {
                    //销毁已经创建的编码器，防止泄露
                    mEncoder.release();
                    mEncoder = null;
                }
                throw new IOException(e);
            }
            isCreated = true;
        }

        void configure(EncoderInfo info) {
            checkCreated();
            mEncoder.configure(info.format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            mMuxer.setOrientationHint(info.orientationHint);
        }

        void startCodec() {
            checkCreated();
            mEncoder.start();
        }

        void startMuxer() {
            checkCreated();
            mMuxer.start();
        }

        void setCallback(MediaCodec.Callback callback, Handler handler) {
            checkCreated();
            mEncoder.setCallback(callback, handler);
        }

        void release() {
            if (mEncoder != null) {
                mEncoder.stop();
                mEncoder.release();
                mEncoder = null;
            }
            if (mMuxer != null) {
                mMuxer.release();
                mMuxer = null;
            }
        }

        public int addTrack(MediaFormat format) {
            checkCreated();
            return mMuxer.addTrack(format);
        }

        public boolean writeSampleData(int track, ByteBuffer buffer, MediaCodec.BufferInfo info) {
            checkCreated();
            try {
                mMuxer.writeSampleData(track, buffer, info);
            } catch (IllegalStateException e) {
                Log.e(TAG, "error!!! writeSampleData: maybe no space left " + e.getMessage());
                return false;
            }
            return true;
        }

        private void checkCreated() {
            if (!isCreated) {
                throw new IllegalStateException("not created");
            }
        }
    }

    private static class EncoderInfo {
        int orientationHint;
        MediaFormat format;
    }

    private native int initNative(ProcessEntry entry);

    private native void uninitNative(long handle);

    private native int processNative(long handle, ProcessInput input, ProcessOutput output);

    private static native void setupNative();

    static {
        System.loadLibrary("video_processor");
        setupNative();
    }
}
