<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string-array name="ai_speech_guide_glass_query">
        <item>小爱同学，镜片换成粉色</item>
        <item>镜片暗一点</item>
    </string-array>
    <string-array name="ai_speech_guide_glass_pop">
        <item>“镜片换个颜色”</item>
        <item>“把镜片调成透明的”</item>
        <item>“镜片调到最亮”</item>
        <item>“镜片暗一点”</item>
        <item>“镜片透光率调到3挡”</item>
    </string-array>

    <string-array name="ai_speech_guide_camera_query">
        <item>小爱同学，拍照片</item>
        <item>开始录像</item>
    </string-array>
    <string-array name="ai_speech_guide_camera_pop">
        <item>“拍照”</item>
        <item>“拍视频”</item>
        <item>“录音”</item>
        <item>“开始直播”</item>
    </string-array>

    <string-array name="ai_speech_guide_translate_query">
        <item>小爱同学，开始实时翻译</item>
        <item>这款眼镜太酷了，是哪家公司出品的？定价大概多少？</item>
    </string-array>
    <string-array name="ai_speech_guide_translate_pop">
        <item>“帮我实时翻译”</item>
        <item>“开始面对面翻译”</item>
        <item>“请帮我把下面这对话翻译成英文”</item>
        <item>“我需要同声传译”</item>
    </string-array>

    <string-array name="ai_speech_guide_gpt_query">
        <item>小爱同学，春节的由来是什么？</item>
    </string-array>
    <string-array name="ai_speech_guide_gpt_pop">
        <item>“宇宙是如何产生的？”</item>
        <item>“新婚姻法跟之前的主要差别是什么？”</item>
        <item>“下届奥运会在哪里举行？”</item>
        <item>“百家争鸣分别是哪百家？”</item>
        <item>“小猫身体不舒服会有哪些表现？”</item>
    </string-array>

    <string-array name="ai_speech_guide_image_query">
        <item>小爱同学，这幅画是谁画的？</item>
    </string-array>
    <string-array name="ai_speech_guide_image_pop">
        <item>“这是什么植物？”</item>
        <item>“屏幕上这个人是谁？”</item>
        <item>“这一顿摄入了多少卡路里？”</item>
        <item>“这是什么品种的小狗？”</item>
        <item>“介绍一下这座桥的历史”</item>
    </string-array>

    <string-array name="ai_speech_guide_news_query">
        <item>小爱同学，今天有什么热点新闻？</item>
        <item>播报今天NBA实时赛况</item>
    </string-array>
    <string-array name="ai_speech_guide_news_pop">
        <item>“今天国内有什么热点新闻？”</item>
        <item>“特朗普胜选后近期有什么动向？”</item>
        <item>“伊拉克叙利亚战场怎么样了？”</item>
        <item>“播报一下今早的NBA常规赛结果”</item>
        <item>“北清快速路最新修建情况”</item>
    </string-array>

    <string-array name="ai_speech_guide_control_query">
        <item>小爱同学，播放音乐</item>
        <item>下一首</item>
    </string-array>
    <string-array name="ai_speech_guide_control_pop">
        <item>“放首歌”</item>
        <item>“大点声”</item>
        <item>“暂停”</item>
        <item>“下一首”</item>
        <item>“单曲循环”</item>
    </string-array>

    <string-array name="ai_speech_guide_recommend_query">
        <item>小爱同学，来点轻松愉悦的歌单</item>
        <item>下一首</item>
    </string-array>
    <string-array name="ai_speech_guide_recommend_pop">
        <item>“今天心情不好，来点治愈歌曲”</item>
        <item>“周五了，我要听点摇滚嗨起来”</item>
        <item>“放点适合婚礼现场的伴奏”</item>
        <item>“该emo了，放点苦情歌”</item>
        <item>“我想听听高评分欧美电子舞曲”</item>
    </string-array>

    <string-array name="ai_speech_guide_music_query">
        <item>小爱同学，这首歌是谁唱的？</item>
        <item>再给我播一首他的其他作品</item>
    </string-array>
    <string-array name="ai_speech_guide_music_pop">
        <item>“这首歌是谁唱的？”</item>
        <item>“这首歌出自哪张专辑？”</item>
        <item>“跟我说下这首歌的创作背景？”</item>
        <item>“这首歌中间那部分的采样出自哪里？”</item>
        <item>“我刚刚听的这首歌是什么类型的？”</item>
    </string-array>

    <string-array name="ai_speech_guide_chat_query">
        <item>小爱同学，陪我聊聊天</item>
        <item>今天太累了，活干不完</item>
    </string-array>
    <string-array name="ai_speech_guide_chat_pop">
        <item>“和小爱聊天”</item>
        <item>“和小爱对话”</item>
        <item>“打开小爱陪伴”</item>
        <item>“打开小爱聊天”</item>
        <item>“开始实时对话”</item>
    </string-array>
    <string-array name="ai_speech_guide_agent_query">
        <item>小爱同学，开始英语口语训练</item>
        <item>Sure, I saw it on the last weekend.</item>
    </string-array>
    <string-array name="ai_speech_guide_agent_pop">
        <item>“和口语老师对话”</item>
        <item>“我想和英语教练学口语”</item>
        <item>“帮我训练一下英语口语”</item>
        <item>“让我跟英语口语老师聊聊天”</item>
        <item>“Let\'s begin oral English training”</item>
    </string-array>

    <string-array name="translate_lan">
        <item>中文</item>
        <item>英文</item>
        <item>法语</item>
        <item>日语</item>
        <item>韩语</item>
        <item>意大利语</item>
        <item>德语</item>
        <item>葡语</item>
        <item>西语</item>
        <item>印尼语</item>
        <item>俄语</item>
    </string-array>
</resources>